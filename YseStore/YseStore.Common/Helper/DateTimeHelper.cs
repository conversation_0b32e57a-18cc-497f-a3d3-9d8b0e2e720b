using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    public static class DateTimeHelper
    {
        public static DateTime ConvertToBeijingTime(this long unixTimestamp)
        {
            // UNIX时间戳是从1970年1月1日（UTC）开始的秒数
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(unixTimestamp);
            TimeZoneInfo beijingTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
            DateTime beijingTime = TimeZoneInfo.ConvertTime(dateTimeOffset, beijingTimeZone).DateTime;
            return beijingTime;
        }

        /// <summary>
        /// 转成当地时间
        /// "China Standard Time"
        /// Atlantic/South_Georgia
        /// </summary>
        /// <param name="unixTimestamp"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static DateTime ConvertToLocalTime(this long unixTimestamp, string zoneId)
        {
            // UNIX时间戳是从1970年1月1日（UTC）开始的秒数
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(unixTimestamp);
            TimeZoneInfo beijingTimeZone = TimeZoneInfo.FindSystemTimeZoneById(zoneId);
            DateTime beijingTime = TimeZoneInfo.ConvertTime(dateTimeOffset, beijingTimeZone).DateTime;
            return beijingTime;
        }

        public static int ConvertToUnixTimestamp(this DateTime localTime)
        {
            // 创建一个 DateTimeOffset 对象，表示本地时间
            DateTimeOffset dateTimeOffset = new DateTimeOffset(localTime);
            int unixTimestamp = (int)dateTimeOffset.ToUnixTimeSeconds();
            return unixTimestamp;
        }






        /// <summary>
        /// 将Unix时间戳转换为本地时间字符串
        /// </summary>
        /// <param name="timestamp">时间戳（自动识别秒/毫秒）</param>
        /// <param name="format">输出格式（默认：yyyy-MM-dd HH:mm:ss）</param>
        /// <returns>格式化后的本地时间字符串</returns>
        public static string ConvertUnixTimestampToLocalTime(this long timestamp, string format = "yyyy-MM-dd HH:mm:ss")
        {
            // 自动判断时间戳单位（秒或毫秒）
            bool isMilliseconds = timestamp > 9_999_999_999L; // 10位最大秒级时间戳为9999999999（2286年）

            DateTimeOffset dateTimeOffset = isMilliseconds ?
                DateTimeOffset.FromUnixTimeMilliseconds(timestamp) :
                DateTimeOffset.FromUnixTimeSeconds(timestamp);

            return dateTimeOffset.ToLocalTime().ToString(format);
        }
    }
}

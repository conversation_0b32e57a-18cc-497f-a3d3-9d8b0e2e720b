using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common
{


    public static class JsonUtil
    {

      
        private static JsonSerializerSettings options = new JsonSerializerSettings
        {
            DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };

        public static string ToJson(this object obj, bool allowCircularRef = false)
        {
            string text = "{}";
            if (allowCircularRef)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                };
                return JsonConvert.SerializeObject(obj, settings);
            }

            return JsonConvert.SerializeObject(obj, options);
        }

        public static T JsonToObj<T>(this string json, bool allowCircularRef = false)
        {
            T val = default(T);
            if (allowCircularRef)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                };
                return JsonConvert.DeserializeObject<T>(json, settings);
            }

            return JsonConvert.DeserializeObject<T>(json, options);
        }
    }

}

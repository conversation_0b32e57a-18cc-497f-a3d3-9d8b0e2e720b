/**
 * 图片URL处理助手
 * 统一处理图片URL，添加OSS处理参数
 */

var ImageUrlHelper = {
    
    /**
     * OSS图片处理参数配置
     */
    ossParams: {
        // 缩略图参数 - 240x240 webp格式，高质量 (提高分辨率避免模糊)
        thumbnail: '?x-oss-process=image/format,webp/resize,m_lfit,h_240,w_240/quality,q_90',

        // 中等尺寸图片参数 - 400x400 webp格式，高质量
        medium: '?x-oss-process=image/format,webp/resize,m_lfit,h_400,w_400/quality,q_90',

        // 大图参数 - 800x800 webp格式，高质量
        large: '?x-oss-process=image/format,webp/resize,m_lfit,h_800,w_800/quality,q_95',

        // 原图webp格式，最高质量
        webp: '?x-oss-process=image/format,webp/quality,q_95',

        // 高清缩略图 - 用于高DPI屏幕
        thumbnailHD: '?x-oss-process=image/format,webp/resize,m_lfit,h_480,w_480/quality,q_95'
    },

    /**
     * 添加OSS处理参数到图片URL
     * @param {string} imageUrl - 原始图片URL
     * @param {string} size - 尺寸类型 (thumbnail, medium, large, webp)
     * @returns {string} 处理后的图片URL
     */
    addOssParams: function(imageUrl, size) {
        if (!imageUrl || typeof imageUrl !== 'string') {
            return imageUrl;
        }

        // 如果URL已经包含参数，则不重复添加
        if (imageUrl.indexOf('x-oss-process') !== -1) {
            return imageUrl;
        }

        // 默认使用缩略图参数
        size = size || 'thumbnail';
        
        var params = this.ossParams[size];
        if (!params) {
            params = this.ossParams.thumbnail;
        }

        return imageUrl + params;
    },

    /**
     * 获取缩略图URL
     * @param {string} imageUrl - 原始图片URL
     * @returns {string} 缩略图URL
     */
    getThumbnailUrl: function(imageUrl) {
        return this.addOssParams(imageUrl, 'thumbnail');
    },

    /**
     * 获取中等尺寸图片URL
     * @param {string} imageUrl - 原始图片URL
     * @returns {string} 中等尺寸图片URL
     */
    getMediumUrl: function(imageUrl) {
        return this.addOssParams(imageUrl, 'medium');
    },

    /**
     * 获取大图URL
     * @param {string} imageUrl - 原始图片URL
     * @returns {string} 大图URL
     */
    getLargeUrl: function(imageUrl) {
        return this.addOssParams(imageUrl, 'large');
    },

    /**
     * 获取WebP格式图片URL
     * @param {string} imageUrl - 原始图片URL
     * @returns {string} WebP格式图片URL
     */
    getWebpUrl: function(imageUrl) {
        return this.addOssParams(imageUrl, 'webp');
    },

    /**
     * 批量处理图片URL数组
     * @param {Array} imageUrls - 图片URL数组
     * @param {string} size - 尺寸类型
     * @returns {Array} 处理后的图片URL数组
     */
    processImageUrls: function(imageUrls, size) {
        if (!Array.isArray(imageUrls)) {
            return imageUrls;
        }

        var self = this;
        return imageUrls.map(function(url) {
            return self.addOssParams(url, size);
        });
    },

    /**
     * 更新页面中所有图片的src属性
     * @param {string} selector - 图片选择器
     * @param {string} size - 尺寸类型
     */
    updateImageSources: function(selector, size) {
        var self = this;
        $(selector).each(function() {
            var $img = $(this);
            var originalSrc = $img.attr('src');
            if (originalSrc) {
                var newSrc = self.addOssParams(originalSrc, size);
                $img.attr('src', newSrc);
            }
        });
    },

    /**
     * 检查URL是否已经包含OSS处理参数
     * @param {string} imageUrl - 图片URL
     * @returns {boolean} 是否包含OSS参数
     */
    hasOssParams: function(imageUrl) {
        return imageUrl && imageUrl.indexOf('x-oss-process') !== -1;
    },

    /**
     * 移除OSS处理参数，获取原始URL
     * @param {string} imageUrl - 包含OSS参数的图片URL
     * @returns {string} 原始图片URL
     */
    getOriginalUrl: function(imageUrl) {
        if (!imageUrl || typeof imageUrl !== 'string') {
            return imageUrl;
        }

        var paramIndex = imageUrl.indexOf('?x-oss-process');
        if (paramIndex !== -1) {
            return imageUrl.substring(0, paramIndex);
        }

        return imageUrl;
    },

    /**
     * 根据容器尺寸自动选择合适的图片尺寸
     * @param {string} imageUrl - 原始图片URL
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {string} 处理后的图片URL
     */
    getOptimalUrl: function(imageUrl, containerWidth, containerHeight) {
        if (!imageUrl) {
            return imageUrl;
        }

        var maxSize = Math.max(containerWidth || 0, containerHeight || 0);
        var size = 'thumbnail'; // 默认

        // 根据实际显示尺寸选择合适的图片尺寸
        // 使用2倍分辨率确保在高DPI屏幕上清晰显示
        if (maxSize <= 120) {
            size = 'thumbnail'; // 240x240 for 120px display
        } else if (maxSize <= 200) {
            size = 'medium';    // 400x400 for 200px display
        } else if (maxSize <= 400) {
            size = 'large';     // 800x800 for 400px display
        } else {
            size = 'webp';      // 原图webp格式
        }

        return this.addOssParams(imageUrl, size);
    },

    /**
     * 为产品预览图片选择合适的尺寸
     * @param {string} imageUrl - 原始图片URL
     * @param {jQuery} $container - 图片容器jQuery对象
     * @returns {string} 处理后的图片URL
     */
    getProductPreviewUrl: function(imageUrl, $container) {
        if (!imageUrl) {
            return imageUrl;
        }

        // 检测是否为高DPI屏幕
        var isHighDPI = window.devicePixelRatio && window.devicePixelRatio > 1;

        if ($container && $container.length > 0) {
            var containerWidth = $container.width();
            var containerHeight = $container.height();

            // 对于高DPI屏幕，使用更高分辨率的图片
            if (isHighDPI && Math.max(containerWidth, containerHeight) <= 240) {
                return this.addOssParams(imageUrl, 'thumbnailHD');
            }

            return this.getOptimalUrl(imageUrl, containerWidth, containerHeight);
        }

        // 默认使用缩略图尺寸，高DPI屏幕使用高清版本
        return isHighDPI ?
            this.addOssParams(imageUrl, 'thumbnailHD') :
            this.getThumbnailUrl(imageUrl);
    },

    /**
     * 获取高清缩略图URL
     * @param {string} imageUrl - 原始图片URL
     * @returns {string} 高清缩略图URL
     */
    getThumbnailHDUrl: function(imageUrl) {
        return this.addOssParams(imageUrl, 'thumbnailHD');
    }
};

// 导出到全局作用域
window.ImageUrlHelper = ImageUrlHelper;

// 兼容性：为了向后兼容，提供简化的全局函数
window.addImageOssParams = function(imageUrl, size) {
    return ImageUrlHelper.addOssParams(imageUrl, size);
};

window.getThumbnailImageUrl = function(imageUrl) {
    return ImageUrlHelper.getThumbnailUrl(imageUrl);
};

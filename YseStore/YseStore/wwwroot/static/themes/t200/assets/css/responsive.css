/*********************************************************************************
    Template Name: Shoplook Multipurpose eCommerce Bootstrap5 Html Template
    Description: A perfect template to build beautiful and unique Electronics websites. It comes with nice and clean design.
    Version: 1.0
**********************************************************************************/

@media only screen and (max-width: 1450px) {
    .slideshow__text-content.bottom { bottom:2%; }	
    .slideshow .slideshow__title { font-size:40px; }
    .slideshow .slideshow__subtitle { font-size:16px; }

    .collection-hero__image { height:auto; }
    .collection-banners.style7 .collection-grid-item .details { max-width: 250px; }	
}

@media only screen and (min-width: 1199px) {
    .lookbook-page-full .lookbook .col-md-4.col-lg-2 { -ms-flex:0 0 20%; flex:0 0 20%; max-width:20%; }
}

@media only screen and (max-width: 1199px) {
    .container-fluid { padding:0 30px; }
    .index-demo2 #siteNav.left { padding-left:20px; }

    .header-16 #siteNav a { font-size:12px; }

    .iconset { padding:0 3px; }
    .topheader .list-inline > li { padding-right:3px; padding-left:3px; }

    #siteNav > li > a { padding:0 5px; }
    #siteNav > li .megamenu { left:-150px; }
    .index-demo4 #siteNav > li .megamenu { left:0; }
    #siteNav > li .megamenu.style1 { left:0; }

    .search-drawer .input-box { width:83%; }
    .lookbook.grid { margin-left:0; }

    .slideshow__text-content {
        max-width: 100%;
        width: 100%;
    }
    .slideshow .slideshow__title { font-size:30px; }
    .slideshow .slideshow__subtitle { font-size:14px; }

    .collection-banners.style5 .collection-grid-item__title { font-size:14px; }
    .grid-sizer.grid-6col, .collection-banners.style5 .collection-page-item { width:20%; }

    .product-nav { display:none; }
    .productSlider .slick-prev, .productPageSlider .slick-prev, .productSlider-style1 .slick-prev,
    .productSlider-style2 .slick-prev, .productSlider-fullwidth .slick-prev { left:10px; }
    .productSlider .slick-next, .productPageSlider .slick-next, .productSlider-style1 .slick-next,
    .productSlider-style2 .slick-next, .productSlider-fullwidth .slick-next { right:10px; }

    .left .slideshow__text-content { left:10px; }

    .index-demo3 .slideshow-wrapper, .index-demo3 .slideshow-wrapper .slick-slider, .index-demo3 .slideshow-wrapper .slick-slider .slick-list,
    .index-demo3 .slideshow-wrapper .slick-slider .slick-track, .index-demo3 .slideshow-wrapper .slick-initialized .slick-slide,
    .index-demo3 .slideshow-wrapper.slideshow .slide, .index-demo3 .slideshow-wrapper.slideshow .slick-slide img, .index-demo3 .slideshow-wrapper.slideshow .blur-up,
    .index-demo3 .slideshow-wrapper .slick-slide > div { height:100%; }

    .index-demo9 .collection-banners.style7 .collection-grid-item .details { background: #ffffffd4 !important; }
    .index-demo9 .collection-banners .details .title { font-size: 18px !important; }
    .index-demo9 .collection-banners .details p { font-size: 13px; }
}

@media only screen and (max-width: 1024px) {
    .header { min-height:50px; }
    .stickyNav { background-color:#fff; }

    .header-2 .iconset .icon { font-size:20px; }

    .top-header-wrapper .top-header { font-size:12px; }
    .collection-box .slick-arrow,
    .collection-box:hover .slick-arrow,
    .productSlider .slick-arrow, .productPageSlider .slick-arrow, .productSlider-style1 .slick-arrow, .productSlider-style2 .slick-arrow,
    .productSlider-fullwidth .slick-arrow { opacity:1; visibility:visible; margin:0; background-color:rgba(255,255,255,0.78); padding:0; }
    .productSlider .slick-next, .collection-box .collection-grid .slick-next, .productPageSlider .slick-next { right:10px; }
    .productSlider .slick-prev, .collection-box .collection-grid .slick-prev, .productPageSlider .slick-prev { left:10px; }
    .productSlider .slick-prev:before, .productSlider .slick-next:before { font-size:15px; line-height:18px; }

    .button-set { opacity:1; top:5px; }
    .variants.add { bottom:0px; margin-top:10px; position:absolute; left:0; right:0; }
    .variants.add .btn { padding:5px 12px; font-size:10px; background-color:#f7f7f7; }

    .saleTime.desktop { display:none; }
    .timermobile { display:block; }
    .timermobile .saleTime { display:block; position:relative; margin-top:20px; }
    .grid-products .item:hover .timermobile .saleTime { display:block; }

    .button-style2 .variants.add button { font-size:13px; padding:0; }
    .button-style2, .button-style2 .variants.add { margin-top:0; }
    .button-style2 i { vertical-align:middle; }

    .lookbook .grid-lb { padding:5px; width:130px; display:block; table-layout:inherit; }
    .lookbook .grid-lb.left { left:auto; right:-20px; }
    .lookbook .grid-lb .pro-img { margin-bottom:10px; display:block; width:100%; }
    .lookbook .grid-lb .detail { padding-left:0; text-align:center; display:block; width:100%; }

    .slick-prev:before, .slick-next:before { font-size:15px; line-height:18px; }

    .box-content-category .slick-dots, .product-slider .slick-dots, .blog-post-slider .slick-dots { margin-top:30px; }

    .index-demo3 .slideshow-wrapper { padding-top: 0; }
    .index-demo4 .topheader { display: none; }

    .header.header-6 { position: relative; }
}

@media only screen and (max-width: 991px) {
    html { overflow-x:hidden; }
    .site-header__logo.mobileview { display:block; }

    .container-fluid { padding-left:15px; padding-right:15px; }
    .mobile-logo { text-align:center; }

    .topheader .customer-links { display:none; position:absolute; right:0; top:100%; z-index:222; margin:0; width:140px; background:#fff; box-shadow:1px 1px 3px rgba(0,0,0,0.2); -webkit-box-shadow:1px 1px 3px rgba(0,0,0,0.2); }
    .topheader .customer-links li { display:block; text-align:left; margin:0; padding:0; }
    .topheader .customer-links li a { color:#555; padding:10px; display:block; border-bottom:1px solid #ddd; }
    .topheader .customer-links li span { display:none; }
    .topheader .customer-links li a:hover { opacity:0.7; }
    .myaccount-links { position:relative; }
    .myaccount-links .user-menu i { font-size:19px; cursor:pointer; }

    .icons-col .site-search { display:none; }
    .mobile-icons .btn--link { display:inline-block; padding-right:10px; }
    .logo a { float:none; }
    .logo img { margin:0 auto; }

    .iconset { padding:0 5px; }
    .site-cart-count, .wishlist-count, .compare-count { right:0; top:-11px; }
    #siteNav, .d-menu-col, .main-menu, .iconset .label { display:none; }

    .search-drawer { padding:30px 20px; }
    .search-drawer .input-box { width:80%; }

    .page-wrapper { position:relative; left:0; -ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
    .mobile-nav-wrapper { display:block; }

    body.menuOn { overflow: hidden; }
    body.menuOn:after { content:""; background: rgba(0,0,0,0.5); position: fixed; top: 0; bottom: 0; left: 0; right: 0; z-index: 99; }
    body.menuOn .page-wrapper { left:270px }

    #pageWrapper { position:relative; left:0; -ms-transition:all 0.4s ease-in-out; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
    .js-mobile-nav-toggle .icon { display:none; color:#000; }
    .site-header__menu { border:0; padding:0; font-size:17px; display:block; cursor:pointer; }
    .js-mobile-nav-toggle.mobile-nav--open .an-bars,
    .js-mobile-nav-toggle.mobile-nav--close .icon { color:#000; display:inline-block; font-size:23px; }
    .header-7 .js-mobile-nav-toggle.mobile-nav--open .an-bars,
    .header-7 .js-mobile-nav-toggle.mobile-nav--close .icon { color:#000; }
    .mobile-nav--close .an-bars { display:none; }
    #MobileNav { height:100%; overflow:auto; list-style:none; padding:0; margin:0; }
    .mobile-nav-wrapper .closemobileMenu { color:#000; font-size:13px; padding:10px; background-color:#eee; cursor:pointer; text-transform:uppercase; font-weight:600; }
    .mobile-nav-wrapper .closemobileMenu .an { font-size:13px; padding:4px; float:right; }
    .mobile-nav-wrapper { width:270px; height:100%;position:fixed; left:-270px; top:0; z-index:10000; background-color:#ffffff; box-shadow:0 0 5px rgba(0,0,0,0.3); opacity:0; visibility:hidden;-ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
    .mobile-nav-wrapper.active { left:0; opacity:1; visibility:visible; }
    #MobileNav li { border-top:1px solid #eeeeee; position:relative }
    #MobileNav li.grid__item {float:none;padding:0 }
    #MobileNav li a { color:#333333; font-size:14px; text-decoration:none; display:block; padding:10px 45px 10px 10px; opacity:1; -webkit-font-smoothing:antialiased; font-weight:400; letter-spacing:0.05em; text-transform:uppercase; position:relative }
    #MobileNav li a .vm-icon { left:0; }
    #MobileNav li a .an, #MobileNav .vm-megamenu .sub-menu-title .an { color:#333333; font-size:15px; display:block; width:40px; height:40px; line-height:40px; position:absolute; right:0; top:0; text-align:center; cursor:pointer; }
    #MobileNav li a .lbl { color:#ffffff; font-size:10px; font-weight:400; letter-spacing:0; line-height:1; text-transform:uppercase; display:inline-block; padding:2px 4px; border-radius:3px; background-color:#f00; box-shadow:0 0 3px rgba(0,0,0,0.3); position:relative; vertical-align:middle }
    #MobileNav li a .lbl:after { content:" "; display:block; width:0; height:0; position:absolute; bottom:3px; left:-7px; border:4px solid transparent; border-right-color:#f00 }
    #MobileNav li a .lbl.nm_label1 { background-color:#01bad4 }
    #MobileNav li a .lbl.nm_label1:after { border-right-color:#01bad4 }
    #MobileNav li a .lbl.nm_label2 { background-color:#f54337 }
    #MobileNav li a .lbl.nm_label2:after { border-right-color:#f54337 }
    #MobileNav li a .lbl.nm_label3 { background-color:#fb6c3e }
    #MobileNav li a .lbl.nm_label3:after { border-right-color:#fb6c3e  }
    #MobileNav li a .lbl.nm_label4 {background-color:#d0a306  }
    #MobileNav li a .lbl.nm_label4:after { border-right-color:#d0a306 }
    #MobileNav li a .lbl.nm_label5 { background-color:#af4de2  }
    #MobileNav li a .lbl.nm_label5:after { border-right-color:#af4de2 }
    #MobileNav li ul { display:none; background-color:#f2f2f2; list-style:none; padding:0; margin:0; }
    #MobileNav li li a { padding-left:20px  }
    #MobileNav li li li a { padding-left:30px; } 
    #MobileNav li li li li a { padding-left:40px; }

    #MobileNav .vm-megamenu li a { padding-left:40px; }
    #MobileNav .vm-megamenu li a .vm-icon { font-size:20px; }
    #MobileNav .vm-megamenu .sub-menu-title { position:relative; font-size: 15px; padding:10px 45px 10px 15px; border-top:1px solid #eeeeee; margin:0; }
    #MobileNav .vm-megamenu .megamenu.type2 .col-6 { width:100%; }
    #MobileNav .vm-megamenu .megamenu li a, #MobileNav .vm-megamenu .megamenu.type2 li a, #MobileNav .vm-megamenu .dropdown li a { padding-left:15px; }
    #MobileNav .vm-megamenu .banner-fixed { display:none; }

    .header-2 .header-cart .minicart-label { display:none; }
    .header-2 .header-cart .icon-in { position:static; }
    .header-2 .header-cart .btn-minicart { padding-right:0; }
    .header-2 .header-cart.iconset .icon { font-size:21px; }
    .header-2 .header-cart .site-cart-count { right:-3px; top:-11px; }

    .index-demo5 .topheader .customer-links { right: auto; left: 0; }
    .index-demo6 .featured-content .featured-text p { padding: 0 0px; }
    .index-demo9 .slideshow-carousel { padding-top: 0; }

    .slideshow .slideshow__title { font-size:26px; }
    .slideshow .slideshow__subtitle { font-size:13px; line-height:normal; margin-bottom:10px; }
    .slideshow__text-content.bottom { bottom:1%; }
    .slideshow .btn { padding:5px 10px; font-size:12px; }

    .newsletter-section .justify-content-end { -webkit-flex-pack:center !important; -ms-flex-pack:center !important; justify-content:center !important; }
    .footer-social { margin-top:30px; }

    .template-collection .collection-header { margin-bottom:20px; }
    .template-product .tabs-listing .product-tabs li { margin-right: 7px; }

    .product-details-img { margin-bottom:20px; overflow:hidden; }
    h1.product-single__title, .product-single__title.h1 { font-size:20px; }
    .template-product .tabs-listing .product-tabs a { padding:10px 10px; }

    .left-content-product { width:100%; padding-right:0; }
    .sidebar-product { width:100%; padding-left:0; }
    .sidebar-product .related-product .grid__item { width:50%; }
    .sidebar-product .related-product .grid__item:nth-child(2n+1) { clear:left; }
    .sidebar-product .sub-heading { max-width:100%; }
    .prSidebar .col-12 { padding-left:0; padding-right:0; }

    .latest-blog .wrap-blog .article__grid-image, .latest-blog .wrap-blog .article__grid-meta { vertical-align:top; }
    .latest-blog .wrap-blog .wrap-blog-inner { padding:0 20px; margin-left:0; }

    .image-banner-1 { padding-left:15px !important; margin-bottom:30px; }
    .image-banner-2 { padding-right:15px !important; }
    .image-banner-2 .mt-4 { margin-top:30px !important; }
    .custom-text-banner .h1 { font-size: 20px; }

    .img-grid-banner2 .col-12 { padding-right:15px !important; padding-left:15px !important; }

    .hero .text-large .mega-title { font-size:35px; }
    .hero .text-large .mega-subtitle { font-size:18px; }
    .hero { height:400px; }
    .hero.hero--small { height:300px; }
    .hero .text-medium .mega-title { font-size:25px; }
    .hero .text-medium .mega-subtitle { font-size:15px; }
    .index-demo7 .hero .font-bold .mega-title { font-size:40px; }
    .index-demo7 .hero .text-small .mega-subtitle { font-size:15px; }

    .featured-content .list-items { margin-left:0; margin-right:0; }
    .feature-row__text .row-text { padding: 20px; }

    .grid-products .item { margin: 0 0 20px; }
    .product-labels.rounded .lbl { height:35px; width:35px; font-size:10px; }
    .sold-out span { padding: 6px; }

    .grid-sizer.grid-6col, .collection-banners.style5 .collection-page-item,
    .grid-sizer.grid-7col, .collection-banners.style6 .collection-page-item { width:33.333%; }
    .collection-banners.style4 .collection-grid-item__title, .collection-banners.style5 .collection-grid-item__title, .collection-banners.style6 .collection-grid-item__title { font-size:13px; }

    .button-set.style1 { opacity:1; visibility:visible; bottom:10px; }
    .button-set.style1 li .btn-icon { font-size:14px; height:30px; width:30px; line-height:28px; margin:0px; }
    .button-set li .btn-icon.btn-square { line-height:27px; }
    .button-set .tooltip-label { top:-28px; }
    .button-set.style2 { opacity:1; visibility:visible; }
    .button-set.style2 .tooltip-label { line-height:21px; }
    .index-demo17 .grid-products.style2 .item .button-set .tooltip-label { bottom:44px; }

    .footer-top .social-icons { margin-bottom:15px; }

    .store-info.style2 li { text-align:center; }
    .store-info.style2 .anm { display:block; float:none; margin-bottom:10px; }
    .store-info.style2 p { padding-left:0; }

    .imgBanners.style3 .row .img-banner-item:nth-of-type(3) { margin-top:20px; }
    .slideshow-carousel.slideshow .slick-prev, .slideshow-carousel.slideshow .slick-next { opacity:1; visibility:visible; }

    .index-demo5 .home-instagram #instafeed .insta-img { width:20%; }

    .footer.footer-3 .newsletter-col, .footer.footer-3 .footer-links:nth-of-type(4) { margin-top:20px; }
    .footer.footer-6 .about-us-col { margin-top:20px; }
    .footer .instagram-col { max-width:100%; flex:0 0 100%; -webkit-flex:0 0 100%; margin-top:20px; }

    .index-demo8 .hero .mega-title { font-size:30px; }

    .tab_container .grid-products .slick-arrow:before { font-size:15px; line-height:18px; }
    .imgBanners.style4 .details .title { font-size:24px; }

    .slideshow.style2 .slideshow__title { font-size:30px; }
    .slideshow.style2 .slideshow__subtitle { font-size:13px; }

    .collection-grid-slider .slick-arrow { opacity:1; visibility:visible; }
    .collection-slider-full .slick-prev { left:30px; }
    .collection-slider-full .slick-next { right:30px; }
    .collection-slider-full .collection-grid-slider { padding:0 20px; } 

    .imgBanners.style7 .inner .ttl { padding:5px; }
    .imgBanners.style7 .ttl .tt-small { font-size:13px; }
    .imgBanners.style7 .ttl .tt-big { font-size:20px; line-height:28px; }

    .section { padding-top:50px; padding-bottom:50px; }

    .button-set.style3 { opacity:1; visibility:visible; bottom:10px; }

    .slideshow-carousel .slick-dots { position:static; text-align:center; -webkit-transform:none; transform:none; -ms-transform:none; margin-top:10px; }
    .instagram-section .item { margin-bottom:20px; }

    .footer.footer-3 .social-col { margin-top: 20px; }
    .footer-8 .newsletter-col { margin-top: 20px; }

    .lookbook .zoom-img { opacity: 1; visibility: visible; }
    .marquee-text .top-info-bar .flex-item a { font-size: 12px; }

}

@media only screen and (min-width: 767px) {
    .home6-modern #page-content { padding-top:13px; }

    .prstyle2 .prFeatures { float:left; width:100%; padding-top:30px; }
    .prstyle2 .prFeatures img { max-width:40px; }
    .prstyle2 .prFeatures .details { margin-left:55px; }
    .prstyle3 .prFeatures .grid__item { margin-bottom:30px }
    .prstyle3 .prFeatures img { max-width:40px }
    .prstyle3 .prFeatures .details { margin-left:50px }

    .top-text-block { text-align:center; font-size:14px; max-width:800px; margin:0 auto; }
}

@media only screen and (max-width: 767px) {
    h1, .h1 { font-size:1.69231em }
    h2, .h2 { font-size:1.38462em; }
    h3, .h3 { font-size:1.15385em; }

    .social-sharing .icon { padding:0 3px; }
    .logo img { max-width:150px; }

    .mobile-hide { display:none; }
    .container { padding-left:15px; padding-right:15px; }
    .search-drawer .container { padding-left:0; padding-right:0; }

    .search-drawer { padding:20px; }
    .search-drawer .closeSearch, .search-drawer .action.search { right:0; }
    .search-drawer .closeSearch { top:0; }
    .search-drawer .input-text { padding-right:40px; padding-left:5px; }
    .search-drawer .input-box { width:75%; }

    .pb-section { padding-bottom:15px; }
    .section { padding-top:30px; padding-bottom:30px; }

    .collection-box .slick-arrow:before, .collection-box:hover .slick-arrow, 
    .productSlider .slick-arrow:before, .productPageSlider .slick-arrow:before, 
    .productSlider-style1 .slick-arrow:before, .productSlider-style2 .slick-arrow:before, .productSlider-fullwidth .slick-arrow:before { font-size:15px; line-height:20px; }

    .slideshow .mega-small-title { display:none; }
    .slideshow .slideshow__title { font-size:22px; }
    .slideshow .slideshow__subtitle { font-size:13px; display:none; }
    .slideshow__text-content.bottom { bottom:0; }
    .slideshow .slick-prev, .slideshow .slick-next { width:30px; height:30px; }
    .slideshow .slick-prev::before, .slideshow .slick-next::before { font-size:13px; line-height:18px; }
    .slideshow img.desktop-hide { display:none; }
    .slideshow img.mobile-hide { display:block; }
    .home-slideshow-carousel .slide .details::before { display:none; }
    .slideshow-carousel.style2 .slide .details { bottom:0; }

    .page-header { padding:40px 0; }

    .grid-products.style2 .row .col-sm-4 { flex: 0 0 100%; max-width:100%; }
    .grid-products.style2 .row .col-sm-4 .item { width:50%; float:left; padding-right:10px; margin-bottom:20px; }
    .grid-products.style2 .row .col-sm-4.col-md-6 .item { width:100%; padding-left:0; padding-right:0; }
    .grid-products.style2 .row .col-sm-4 .item:nth-of-type(2n) { padding-right:0; padding-left:10px; }
    .grid-products.style2 .row .col-sm-4 .item .product-image { margin-bottom:0; }
    .grid-products.style2 .item .button-set li .btn-icon { font-size:16px; height:28px; width:28px; line-height:24px; }
    .grid-products .item .btn-addto-cart i { font-size: 16px; }
    .grid-products.style2 .button-set .tooltip-label { top:-28px; height:21px; line-height:16px; }

    .button-set li .btn-icon { font-size:16px; line-height:25px; height:28px; width:28px; }
    .button-set.style2 { width:30px; }

    .header-9 { min-height:50px; }

    .index-demo4 .hero-section { padding:0; }
    .index-demo6 .btn, .index-demo6 .slideshow .btn { display: inline-block; }

    .section-header-left { flex: 0 0 55%; -webkit-flex: 0 0 55%; -ms-flex: 0 0 55%; }

    .blog-single-page .article blockquote { font-size: 13px; padding: 20px; }
    .imgBanners.style2 .details .title { font-size:17px; } 

    .tab-slider-product .tab_drawer_heading { display:block; }
    .grid-products.productSlider .slick-arrow { margin-top:-40px; }
    .collection-box:hover .slick-arrow { margin:0; line-height:0; }
    .filter-widget .widget-title::after { top:0; display:none; }

    .collection-banners .details { width:80%; bottom:10px; padding:15px; }
    .collection-banners .details.w-50 { width:80% !important; }
    .collection-banners .details .btn { font-size:12px; padding:5px 15px; margin-top:10px; }
    .collection-banners .details .title { font-size:15px; }
    .index-demo1 .collection-banners .details .title { font-size:14px; }

    .template-product .tabs-listing .product-tabs { display:none; }
    .template-product .tabs-listing .tabs-ac-style { display:block; }
    .tabs-listing .tabs-ac-style { font-size:14px; font-family:"Poppins",Helvetica,Tahoma,Arial,sans-serif; position:relative; line-height:normal; text-transform:uppercase; color:#000; background-color:#f5f5f5; border:1px solid #ddd; display:block; padding:10px 30px 10px 15px; margin-bottom:5px; cursor:pointer; }
    .tabs-listing .tabs-ac-style.active { color:#000000; background-color:#ffffff; border:1px solid #dddddd; padding:10px; display:block; padding:10px 30px 10px 15px; }
    .tabs-listing .tabs-ac-style:before { font-family:"annimex-bold"; content: "\f107"; font-size:13px; position:absolute; right:15px; top:50%; margin-top:-8px; font-weight: 900; }
    .tabs-listing .tabs-ac-style.active:before { content: "\f106"; }
    .tabs-listing .tab-container .tab-content { padding:20px 0; }
    .tabs-listing.tabs-listing-style2 .tabs-ac-style { background-color:#f7f7f7; border-color:#f7f7f7; }
    .tabs-listing.tabs-listing-style2 .tabs-ac-style.active { color:#fff; background-color:#2e6ed5; border-color:#2e6ed5; }

    .product-single-style2 .product-details-img .zoompro-wrap,
    .product-single-style2 .product-details-img .zoompro,
    .product-single-style4 .product-details-img .zoompro-wrap,
    .product-single-style4 .product-details-img .zoompro{ pointer-events:none; }
    .product-single-style2 .product-details-img .slick-arrow,
    .product-single-style4 .product-details-img .slick-arrow { color:#000; opacity: 1; background-color:rgba(255,255,255,0.89); width:30px; height:30px; display:block; }
    .product-single-style2 .product-details-img .slick-arrow.slick-prev,
    .product-single-style4 .product-details-img .slick-arrow.slick-prev { left:5px; }
    .product-single-style2 .product-details-img .slick-arrow.slick-next,
    .product-single-style4 .product-details-img .slick-arrow.slick-next { right:5px; }

    .category-text-banner { height:120px; }
    .category-text-banner .page-title { padding:0 20px; margin-top:-33px; }
    .full-page-title .page-title { padding:40px 0; }

    .collection-grid-item .details { width:100%; }
    .imgBanners .inner .ttl { font-size:15px; padding:10px; }
    .imgBanners .row .img-banner-item:first-of-type { margin-bottom:20px; }

    .index-demo2 .section .section-header h2 { font-size:20px; }
    .index-demo2 .section .section-header p { font-size:14px; }

    .tab-slider-product .tab_drawer_heading { font-size:17px; text-transform:uppercase; border:0; color:#111; display:block; padding:15px 30px 15px 15px; text-decoration:none; background-color:#eee; position:relative; cursor:pointer; }
    .tab-slider-product-style1 .tab_drawer_heading { font-size:13px; text-align:center; }
    .tab-slider-product	.tab_drawer_heading.d_active { color:#ffffff; background-color:#2358ad; }
    .tab-slider-product	.tab_drawer_heading .an { font-size:14px; position:absolute; top:16px; right:15px; }
    .tab-slider-product .tab_content { margin-bottom:20px; }
    .index-demo6 .tab-slider-product .tab_drawer_heading.d_active { color: #fff; background-color: #333e48; }
    .index-demo8 .tab-slider-product .tab_drawer_heading.d_active { background-color: #333; }
    .index-demo9 .tab-slider-product .tab_drawer_heading { border-radius: 5px; }
    .index-demo9 .tab-slider-product .tab_drawer_heading.d_active { background-color: #191919; }

    .index-demo2 .tab-slider-product .tab_drawer_heading.d_active { color:#ffffff; background-color:#ff802b; }
    .index-demo5 .tab-slider-product .tab_drawer_heading.d_active { background-color: #ee307c; }

    .box-content-category .slick-dots, .product-slider .slick-dots, .blog-post-slider .slick-dots { margin-top:10px; }

    .collection-slider .slick-arrow, .collection-slider:hover .slick-arrow, .home-blog-post .slick-arrow { opacity:1; visibility:visible; }
    .home-blog-post .slick-arrow { background:rgba(255,255,255,0.5); text-align:center; height:30px; width:30px; line-height:29px; border-radius:50%; font-size:0; }
    .home-blog-post .slick-next:before, .home-blog-post .slick-next:after,
    .home-blog-post .slick-prev:before, .home-blog-post .slick-prev:after { font-size:16px; line-height:29px; }
    .collection-slider .collection-grid-item, .blogpost-item { margin:0; }
    .blog-post-slider-style1 .blogpost-item { margin:0 5px 0; }

    .latest-blog .wrap-blog { margin-bottom:20px; }
    .latest-blog .wrap-blog .wrap-blog-inner { margin-left:0; padding:0 20px; }

    .store-features .store-info .col-lg-4 { margin-bottom:10px; }

    .grid-view-item.style2 .button-set > form button, a.quick-view, a.wishlist, .cartIcon, .add-to-compare { width:26px; height:26px; line-height:26px; }
    .list-view-items .button-box > div .btn-icon { width:35px; }

    h2, .h2, .home2-default .section-header h2, .home2-default .section-header .h2 { font-size:18px; }

    .index-demo8 .button-set.style4, .button-set.style4 { bottom:10px; opacity:1; visibility:visible; }
    .button-set.style4 .btn-icon { height:35px; width:35px; line-height:35px; -webkit-transform:inherit; -moz-transform:inherit; transform:inherit; }

    .footer .footer-top { padding:25px 0; }
    .footer .footer-top .footer-links { padding-top:8px; padding-bottom:8px }
    .footer .footer-top .footer-links .h4 { cursor:pointer; border-bottom:1px dotted #d0d0d0; padding-bottom:15px; margin-bottom:0; position:relative; }
    .footer .footer-top .footer-links .h4:after { content: "\f107"; font-family:"annimex-bold" !important; font-size:11px; display:block; position:absolute; right:10px; top:0; }
    .footer .footer-top .footer-links .h4.active:after { content: "\f106"; }
    .footer .footer-top .footer-links ul { display:none; padding-top:10px; }
    .newsletter-col { margin-top:10px; }

    .footer.footer-1 .about-us-col p { margin-bottom: 10px; }

    .footer-4 .newsletter-col { margin-top:0; margin-bottom: 15px; }
    .footer-4.footer .footer-top .footer-links:last-of-type .h4 { border-bottom: 0; }

    .footer-bottom { text-align:center; }
    .footer-bottom .payment-icons { font-size:24px; float:none; display:block; text-align:center; margin-bottom:10px; }
    .footer-bottom .copytext { float:none; line-height:normal; }

    .footer-8 .d-app-col { margin-top: 10px; }

    .custom-search .search { padding: 2px 5px; }
    .blog-sidebar-page .sidebar { margin-bottom:20px; }
    .blog-sidebar-page .list-sidebar-products { margin-top:0; }
    .blog-sidebar-page .sidebar .sidebar_widget { margin-bottom:15px; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-title { cursor:pointer; border-bottom:1px dotted #d0d0d0; padding-bottom:15px; margin-bottom:0; position:relative; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-title h2 { margin-bottom:0; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-title h2:after { margin: 10px 0 0; display: none; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-title:after { content: "\f107"; font-family:"annimex-bold"; font-weight: 900; font-size:12px; display:block; position:absolute; right:0; top:0; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-title.active:after { content: "\f106"; }
    .blog-sidebar-page .sidebar_tags .sidebar_widget .widget-content { display:none; padding-top:15px; }

    .collection-hero h1.collection-hero__title, .collection-hero .collection-hero__title.h1 { font-size:18px; }

    .btn-filter { margin-bottom:20px; width:100%; }
    .shop-heading-page .btn-filter { margin-bottom:20px; margin-top:20px; }
    .filter-sidebar { background-color:#fff; padding:0; }
    .size-swacthes .swacth-list .swacth-btn { background-color:#f5f5f5; }

    .filterbar { opacity:0; visibility:hidden; width:270px; height:100%; padding:0 !important; background-color:#ffffff; box-shadow:0 0 5px rgba(0,0,0,0.3); position:fixed; top:0; left:-275px; z-index:9999; -ms-transition:0.5s; -webkit-transition:0.5s; transition:0.5s; }
    .filterbar.active { left:0; opacity:1; visibility:visible; overflow:inherit; }
    .filterbar.active .closeFilter { color:#ffffff; font-size:15px; line-height:30px; height:30px; width:30px; text-align:center; cursor:pointer; position:absolute; top:10px; left:100%; background-color:#000000; box-shadow:0 0 5px #ddd; -ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
    .filterbar.active .closeFilter:after { content:''; background:rgba(0,0,0,0.5); position:fixed; top:0; bottom:0; left:0; right:0; z-index:-1; }
    .filterbar .sidebar_tags { position:relative; z-index:9; background:#ffffff; padding:20px; height:100%; overflow:auto; }
    .filterbar .sidebar_widget:not(.filterBox), .filterbar .static-banner-block { display:none; }

    .shop-fullwidth-layout .filters-toolbar__item--count { display:none !important; }
    .shop-fullwidth-layout .btn-filter { width:auto; }

    .product-template__container .shareRow .medium-up--one-third { width:100%; }
    .product-template__container .shareRow .display-table-cell { display:block; text-align:left !important; }
    .prstyle2 .prFeatures, .prstyle3 .prFeatures { padding-left:20px }

    .product-right-sidebar .product-details-img { width:100%; float:left; padding-right:0; margin-bottom:20px; }
    .product-right-sidebar .product-information { width:100%; float:left; padding-left:0; }
    .selector-wrapper.product-form__item { -webkit-flex:1 1 100%; -moz-flex:1 1 100%; -ms-flex:1 1 100%; flex:1 1 100%; }

    .password-table .password-cell { display:none; }
    .password-table .password-cell + .password-cell { display:table; width:100%; }

    .template-blog .sidebar { margin-top:30px; }
    .template-blog .mini-list-item .mini-view_image { width:80px; }
    .template-blog .mini-list-item .details { margin-left:0; }

    .cart thead, .cart-price, .cart .cart__update-wrapper { display:none; padding-top:0; padding-bottom:15px; }
    .cart .small--hide { display:none; }
    .cart tr, .cart tbody { width:100%; display:table; } 
    .cart tbody { display:block; }
    .cart-flex { display:block; width:100%; }
    .cart-flex-item { display:table-cell; min-width:0; }
    .cart__price-wrapper { text-align:right; }

    .checkout-page-style2 .table-bordered .thumb { width:40px; }
    .checkout-page-style2 .order-table .table thead th { font-size:12px; }

    .compare-page .table th.product-name { min-width: 110px; }
    .compare-page .table .featured-image { max-width: 180px; }

    .contact-us-page2 .contact-form-in { padding: 20px; }
    .contact-us-page2 .map-section.map iframe { height: 300px; }

    #content_quickview.modal { overflow:auto; }
    #quickview_popup .share-icon { margin-top: 5px; margin-bottom: 5px; }

    #popup-container { margin:20px; }
    #popup-container .width40 { display:none; }
    #popup-container .width60 { width:100%; }

    #newsletter-modal.style2 { max-width:320px; }
    #newsletter-modal.style2 .newsltr-tbl .newsltr-img { display:none !important; }
    #newsletter-modal.style2 .newsltr-tbl .newsltr-text { display:block; }
    #newsletter-modal.style2 .newsltr-tbl .newsltr-img img,
    #newsletter-modal.style2 .newsltr-tbl .newsltr-text img { float:none; }
    #newsletter-modal.style2 .newsltr-tbl .wraptext { padding:0; }

    .hero--large { height:488px; }
    .index-demo4 .hero--large { height:400px; }
    .hero { background-position:50% 50% !important; background-attachment:scroll !important; }
    .hero .text-small .mega-title { font-size:24px; }
    .hero .hero__inner span, .hero .text-small .mega-subtitle { font-size:15px; }
    .featured-content .featured-content-bg { margin-top:40px !important; }
    .featured-content .display-table-cell { display:block; }
    .featured-content .featured-text { padding:20px !important; }
    .featured-content .featured-content-bg .display-table-cell:first-of-type { padding:30px; }
    .featured-content .featured-text h2 { font-size:24px; }
    .featured-content .featured-text p { padding: 0; }

    .logo-bar .slick-slide { margin: 0 5px; }

    .layout-boxed { padding:0 20px; }
    .layout-boxed .imgBanners { display:none; }
    .feature-row__text .row-text { margin-left:0; padding:10px 0; }
    .feature-row__text .row-text { margin-right:0; }
    .layout-boxed .grid-products .slick-arrow { margin-top:0; }
    .featured-column .text-center { margin-bottom:30px; }
    .feature-row { -webkit-flex-direction:column; -moz-flex-direction:column; -ms-flex-direction:column; flex-direction:column; }
    .feature-row__item { -webkit-flex:1 1 auto; -moz-flex:1 1 auto; -ms-flex:1 1 auto; flex:1 1 auto; max-width:100%; width:auto; padding:0; }
    .feature-row__text { order:2; padding-bottom:0; padding:0; }
    .feature-row .feature-row__item { width:100% !important; }
    .feature-row .feature-row__item img { width:100%; margin-bottom:20px; }

    .featuredContentStyle3.featuredContent .row-text { padding: 30px; }
    .layout-boxed .slideshow__text-content { text-align:left; } 

    .quote-wraper .quotes-slider blockquote { font-size:13px; line-height:23px; }
    .quotes-slider .slick-arrow { opacity:1; visibility:visible; }
    .quote-wraper .slick-next { right:-5px; }
    .quote-wraper .slick-prev { left:-3px; }

    .hero .text-large .mega-title { font-size:30px; }
    .hero .text-large .mega-subtitle { font-size:16px; }
    .hero .text-medium .mega-title { font-size:22px; }
    .hero .text-medium .mega-subtitle { font-size:14px; }

    .product-single .display-table, .product-single .display-table-cell { display:block; }
    .product-single .product-featured-img { margin-bottom:30px; }
    .product-single .grid_item-title { font-size:22px; margin-bottom:10px; }
    .product-single .display-table-cell { padding-left:0; padding-right:0; }
    .product-template__container .product-single__meta { margin-bottom:0; }

    .store-feature-top { display:none; }
    .home13-auto-parts .slideshow .slideshow__title { font-size:22px; }
    .slideshow .mobile-show { display:block; }
    .slideshow .desktop-show { display:none; }
    .home13-auto-parts .footer-links .h4, .home14-bags .footer-links .h4 { border-color:#454545; }

    .categories-item { width:48.5%; }
    .categories-item:nth-of-type(1n) { margin-bottom:20px; }
    .categories-item:nth-of-type(2n) { margin-right:0; margin-bottom:20px; }
    .categories-item:nth-of-type(2n+1) { clear:left; }
    .categories-list-items .row { margin-bottom:0; }
    .categories-item img { width:100%; }
    .categories-list-items .btn { font-size:12px; padding:5px 10px; }

    #site-scroll { font-size:14px; line-height:30px; height:30px; width:30px; right:15px; bottom:30px; }
    #site-scroll i { line-height:30px; }

    .close-announcement { right:10px; }
    #sizechart table, .tab-container table { table-layout:fixed; }

    .store-info li { width:100%; border-left:0 !important; border-bottom:1px dotted #ddd; margin-bottom:10px; padding-bottom:10px; }
    .store-info li:last-child { border-bottom:0; }

    #footer .addressFooter li:last-of-type { padding-bottom:0; }

    .stickyCart .img, .stickyCart .sticky-title, .stickyCart .wrapQtyBtn { display:none; }
    .stickyCart .stickyOptions { width:50%; margin-right:5px; }
    .stickyCart .stickyOptions .selectedOpt { width:100%; }
    .stickyCart .product-form__cart-submit { width:calc(50% - 10px); }

    .quotes-slider .slick-arrow { height:30px; line-height:29px; width:30px; } 
    .quotes-slider .slick-prev:before, .quotes-slider .slick-next:before { font-size:15px; line-height:30px; }

    .index-demo4 .hero .mega-subtitle { font-size:14px; }
    .index-demo4 .hero__inner { padding:20px 0; }
    .index-demo4 .hero .hero__inner .wrap-text { padding:20px; max-width:390px; }
    .index-demo4 .hero .hero__inner .count-inner { margin-bottom:5px; }
    .index-demo4 .hero .mega-title { font-size:24px; margin-bottom:10px; }
    .index-demo4 .hero .mega-subtitle { margin-bottom:15px; line-height:20px; }

    .imgBanners .details { padding:10px; }
    .imgBanners .details.right { right:10px; bottom:10px; }
    .imgBanners .details.left { left:10px; bottom:10px; }
    .imgBanners .details.center, .imgBanners .details.left, .imgBanners .details.right { bottom:10px; }
    .imgBanners .details.left-top { top:10px; left:10px; }
    .imgBanners .details.right-top { top:10px; right:10px; }
    .imgBanners .w-50 { width:70% !important; }
    .imgBanners.style3 .row .img-banner-item:nth-of-type(1),
    .imgBanners.style3 .row .img-banner-item:nth-of-type(2),
    .imgBanners.style3 .row .img-banner-item:nth-of-type(3) { padding-right:10px; padding-left:10px; }
    .imgBanners.style3 .row .img-banner-item:nth-of-type(3) { margin-top:20px; }
    .imgBanners.style3 .details .tt-small { font-size:13px; }
    .imgBanners.style3 .details .title { font-size:22px; }
    .index-demo17 .imgBanners.style3 .row .img-banner-item:nth-of-type(3) { margin-top:20px; }

    .slideshow .slick-dots { bottom:20px; }
    .index-demo5 .slideshow .slideshow__title { font-size:20px; }
    .index-demo5 .slideshow .mega-small-title { display:block; font-size:14px; }
    .index-demo5 .section.home-instagram { padding-bottom:0; }

    .topbar-slider-items .slick-arrow,
    .topbar-slider-items:hover .slick-arrow { opacity:1; visibility:visible; }

    .hero__inner .right { float:none; }
    .footer .instagram-col .insta-img { max-width:68px; }

    .hero .hero__inner .wrap-text.bottomleft, .hero .hero__inner .wrap-text.topleft,
    .hero .hero__inner .wrap-text.bottomright { left:0; right:0; position:relative; }
    .hero .hero__inner .wrap-text { padding:20px; display:table; margin:0 auto; }
    .index-demo8 .hero .hero__inner .wrap-text { background-color: #ffffffed; }
    .index-demo8 .hero { background-position: 100% 50% !important; }
    .index-demo8 .hero .text-small .mega-title { font-size: 30px; }

    .grid-products.style2 .item .product-details { position:static; opacity:1; visibility:visible; transform:none; -ms-transform:none; -webkit-transform:none; margin-top:10px; }
    .grid-products.style2 .item .button-set { position:static; opacity:1; visibility:visible; }

    .slideshow.style2 .mega-small-title, .slideshow.style2 .slideshow__subtitle,
    .slideshow.style2 .btn { display:inline-block; }
    .slideshow.style2 .mega-small-title, .slideshow.style2 .slideshow__subtitle { margin-bottom:5px; }
    .slideshow.style2 .btn { font-size:11px; padding:2px 10px; }
    .slideshow.style2 .slideshow__text-content { max-width:100%; width:85%; }

    .custom-content-section .col-lg-6 { margin-bottom:20px; }

    .productSlider .col-12, .productSlider-style1 .col-12 { padding:0 5px; }
    .custom-map-section .col-12.text-center { margin-bottom:20px; }

    .product-with-colletion-bnr .grid-products .row { margin-left:-5px; margin-right:-5px; }
    .product-with-colletion-bnr .grid-products .item { margin-bottom:20px; padding:0 5px; }
    .product-with-colletion-bnr .grid-products .item.last img { width:100%; }

    .testimonial-slider-style1.quote-wraper { padding:60px 20px; }

    .grid-products.style2 .item .overlay { opacity:0; visibility:hidden; position:static; display:none; }
    .grid-products.style2 .item .product-details { padding-bottom:10px; }

    .section-header {  margin-bottom:20px; }

    .imgBanners.style6 .row .img-banner-item.last { margin-top:0; margin-bottom:0; }
    .tab-slider-product .tab_drawer_heading { font-size:15px; }

    .hero.hero--small { height:250px; }
    .hero.hero--small .btn { padding:6px 15px; }

    .category-banner-slider .slick-arrow { width:30px; height:30px; }
    .category-banner-slider .slick-arrow:before { font-size:13px; line-height:30px; }

    .index-demo1 .collection-banners.style1 .collection-grid-item .details { width:80%; }
    .product-notification { display:none !important; }

    .mfp-close { font-size:20px !important; width:35px; height:35px; line-height:35px; }

    .collection-banners.style2 .collection-grid-item .details, .collection-banners.style0 .collection-grid-item .details { height:auto; position:relative; left:0; right:0; display:block; opacity:1; visibility:visible; -ms-transform:none; -webkit-transform:none; transform:none; }
    .collection-banners.style2 .collection-grid-item:hover .details, .collection-banners.style0 .collection-grid-item:hover .details { opacity:1; visibility:visible; }

    .product-details-img .product-thumb .slick-next,
    .product-details-img .product-thumb .slick-prev { width:25px; height:25px; padding:0; }
    .product-details-img .product-thumb .slick-next:before,
    .product-details-img .product-thumb .slick-prev:before { font-size:15px; line-height:14px; }

    .product-single-style3 .product-single .product-single__meta { margin-top:25px; }
    .product-single-style3 .product-form .row { margin:0; }
    .product-single-style3 .product-form .col-12 { padding:0; }

    .product-single-style2 .product-sticky-style { margin-bottom:0; }
    .product-single-center-mode .tabs-listing { margin-top:0; }

    .contact-details { margin-bottom:20px; padding:30px; }
    .contact-us-page .map-section__overlay { width:220px; }

    .collection-banner-grid .collection-item .img img { display:block; width:100%; }
    .featuredContent .d-flex { flex-direction:column; -webkit-flex-direction:column; -ms-flex-direction:column; }

    .store-info.style4 ul { display:block; }
    .store-info.style4 li { width:49%; display:inline-block; }
    .store-info.style4 .anm { font-size:30px; }
    .store-info.style5 li { width:49%; display:inline-block; border-bottom:0; padding:10px 20px; }
    .store-info.style5 img { max-width:90px; }
    .index-demo4 .store-features .col-12 { padding: 0; }
    .store-features .container-fluid { padding: 0 20px; } 
    .index-demo9 .store-features .slick-slide { margin: 0; }
    .index-demo9 .collection-banners .row { margin-left: 0; margin-right: 0; }

    .medical-demo .section.info-section { font-size:18px; }

    .zoomContainer { display:none; }
    #sizechart table tr th, .tabs-listing .tab-container table tr th { width: 80px; }

    .quote-wraper .slick-list, .quote-wraper .slick-slide { margin:0; }
    #threesixty { max-width: 450px; max-height: 500px; }

    .index-demo4 .slideshow .slick-dots { display:none !important; }
    .index-demo4 .slideshow .mega-small-title { font-size:15px; margin:0 0 5px; }
    .index-demo4 .slideshow .wrap-caption.left { float:none;}
    .index-demo4 .slideshow .wrap-caption { padding:20px; background-color:rgba(255,255,255,0.85); text-align: center; }
    .index-demo4 .slideshow .left .slideshow__text-content { left:0; right:0; margin:0 auto; }
    .index-demo4 .slideshow .slideshow__subtitle { display:block; }
    .index-demo4 .slideshow__text-content { width:90%; }
    .index-demo4 .slideshow .slideshow__title { font-size:20px; text-align: center; }
    .index-demo4 .slideshow .btn { display:inline-block; }
    .index-demo4 .slideshow .slideshow__subtitle { margin-bottom:10px; line-height:normal; }
    .index-demo4 .slideshow__text-content { top:60%; }

    .index-demo6 .imgBanners.style5 { display: none; }
    .featured-content.style2 .featured-content-bg1 { margin-bottom: 20px !important; }

    .minicart-bottom .proceed-to-checkout { margin-bottom: 10px; }
    .minicart-bottom .pr-0 { padding-right: 13px !important; }

    .collection-grid-item .details { position: static; -webkit-transform: none !important; transform: none !important; -ms-transform: none !important; background-color: #f7f7f7; }
    .collection-grid-item .img { overflow: hidden; }

}

@media only screen and (max-width: 575px) {	
    .index-demo1 .collection-banners .row .banner-item { margin-bottom: 20px; }
    .index-demo1 .collection-banners .row .banner-item:last-of-type { margin-bottom: 0; }
    .index-demo1 .hero .hero__inner .wrap-text { padding: 25px; }

    .wishlist-link { display:none; }
    .header-2 .header-cart { border-left: 0; padding-left: 0; }
    .search-drawer .input-box { width:68%; }

    .slideshow .wrap-caption { padding:10px 0; }
    .slideshow .slideshow__title { font-size:18px; }
    .slideshow .slideshow__subtitle { display:none; }
    .slideshow .container { width:320px; }
    .index-demo6 .container, .index-demo9 .slideshow .container { width:100%; }
    .slideshow .btn { font-size: 11px; padding: 3px 10px; }
    .sliderFull .slideshow__subtitle { display:block; }
    .sliderFull .btn { display:inline-block; }
    .sliderFull .slideshow__title { font-size:30px; }
    .sliderFull .slideshow__subtitle { margin-bottom:10px; }
    .sliderFull .slideshow__text-content.bottom { bottom:70px; }
    .slideshow-carousel.style2 .slide .details h3 { font-size:18px; }
    .index-demo8 .left .slideshow__text-content { left: 5%; }

    .collection-grid-item__title { font-size: 15px; }
    .collection-page-grid .row { margin-left: -5px; margin-right: -5px; }
    .collection-page-grid .collection-page-item { padding-left:5px; padding-right:5px; margin-bottom:10px; }
    .index-demo4 .collection-banners.style1 .banner-item,
    .index-demo4 .collection-banners.style1 .banner-item:nth-of-type(2) { padding:0; }

    .newsletter-section { padding:20px 0; }
    .newsletter-section .section-header { display:block; margin:0 0 15px 0; float:none; }
    .newsletter-section .section-header span { display:inline-block; padding-right:5px; }

    .footer-bottom .copyright { text-align:center; }

    .timermobile .saleTime span>span { margin:0; min-width:0; font-size:10px; background:#f2f2f2 }
    .timermobile .saleTime span>span span { font-size:9px; display:block; float:none }

    .saleTime.product-countdown .count-inner { margin-right:1px; margin-left:0; width:100%; }
    .saleTime.product-countdown .count-inner .time-count { font-size:18px; }

    .hero { height:200px; }
    .template-product .tabs-listing { margin-top: 20px; }
    .related-product { margin-bottom: 40px; }

    .image-banner-3 { margin-bottom:25px; }
    .imgBnrOuter .inner .ttl { line-height:20px; font-size:14px; padding:10px; }
    .imgBnrOuter .inner.topleft .ttl { left:10px; top:10px; }
    .imgBnrOuter .inner .ttl h3 { font-size:18px; }
    .imgBanners.style2 .banner-item:nth-of-type(1) { padding-left:0; margin-bottom:10px; }

    .three-column-pro .col-12:not(:last-of-type) { margin-bottom:30px; }

    .featured-content .list-items .col-12 { margin-bottom:15px; }
    .featured-content .list-items img { margin-bottom:10px; }
    .collection-box .collection-grid-item__title-wrapper { bottom:10px; }

    .categories-item { width:100%; margin-right:0; }
    .product-countdown span>span { width:24.3%; }
    .store-features .store-info .col-lg-4 { margin-bottom:20px; }

    .grid-sizer, .collection-page-item { width:100%; }
    .grid-sizer.grid-5col, .collection-banners.style4 .collection-page-item,
    .grid-sizer.grid-6col, .collection-banners.style5 .collection-page-item,
    .grid-sizer.grid-7col, .collection-banners.style6 .collection-page-item { width:50%; }
    .collection-banners.style4 .collection-page-grid .collection-page-item,
    .collection-banners.style5 .collection-page-grid .collection-page-item,
    .collection-banners.style6 .collection-page-grid .collection-page-item { padding-left:5px; padding-right:5px; margin-bottom:10px; }
    .collection-banners.style4 .collection-grid-item__title { font-size:13px !important; }

    .grid-products .item:hover .button-set.style1, .button-set.style1 { bottom:5px; }
    .button-set.style1 li .btn-icon { width:26px; height:26px; line-height:23px; }
    .button-set.style1 li .btn-icon.btn-square { line-height:23px; }
    .button-set.style2 { width:26px; }
    .button-set.style2 li .btn-icon { width:26px; height:26px; line-height:24px; }
    .button-set.style2 .btn-icon { font-size:13px; }
    .button-set .tooltip-label { display:none; }
    .shop-fullwidth-layout .filterbar { width:260px; }
    .shop-fullwidth-layout .filterbar.active .closeFilter { left:260px; }

    .list-view-item__image-wrapper { margin-right: 0; }
    .list-view-item__image-column, .list-view-item__title-column { width: 100%; display: block; }
    .list-view-item__title-column { margin-top: 15px; }

    .button-set.style4 .btn-icon { width:30px; height:30px; line-height:30px; font-size:16px; margin:0; }
    .quotes-slider-style2 blockquote { font-size:14px; line-height:26px; }

    .blog-single-page .comment__avatar { margin-left: 0; margin-right: 15px; }
    .blog-single-page .comments-list--level--1 { margin-top: 20px; padding-top: 20px; }
    .blog-single-page .comments-list--level--1 > .comments-list__item { margin-left: 30px; }

    .grouped-product-list td.grouped-product-list-item__thumb img { max-width:40px; }
    .product-grouped-layout td.grouped-product-list-item__quantity { width:100px; }

    .product-buttons .btn { font-size:15px; height:33px; width:30px; }
    .product-buttons .btn i { line-height:28px; }

    .newsletter-section { padding:20px 10px; }

    .imgBanners.style2 .banner-item,
    .imgBanners.style2 .banner-item:nth-of-type(2) { padding:0; }
    .imgBanners.style2 .banner-item { margin-bottom:10px; }

    .slideshow .slick-dots { bottom:10px; }
    .mini-product-list { margin-bottom:30px; }
    .hero .hero__inner .wrap-text { padding:15px; }

    .section-header { margin-bottom:25px; }

    .slideshow.style2 .slideshow__title { font-size:22px; margin-bottom:5px; }
    .slideshow.style2 .slideshow__subtitle { font-size:11px; line-height:16px; }

    .button-set.style3 li .btn-icon { margin:1px; font-size:14px; }

    .imgBanners.style7 .row .img-banner-item { margin-bottom:10px; }
    .imgBanners.style6 .row .img-banner-item.last { margin-top:10px; }

    .blog--list-view .article { margin-bottom:20px; }
    .blog--list-view.no-border .article { margin-bottom: 0px; }
    .blog--grid-load-more .article { padding-bottom:20px; }
    .blog-list-view .article_featured-image img { margin-bottom:15px; }
    .article_featured-image { min-height: inherit; }

    .index-demo3 .slideshow .slideshow__title { font-size:18px; }
    .index-demo3 .slideshow-wrapper.slideshow .wrap-caption { width:auto; padding:10px 15px; }
    .index-demo3 .slideshow .slideshow__title { margin:0 0 5px; }
    .slideshow .center .slideshow__text-content { left: 10px; }
    .index-demo3 .slideshow .btn { font-size: 11px; }
    .index-demo3 .right .slideshow__text-content { right: auto; }

    .index-demo4 .slideshow .slideshow__subtitle { display: none !important; }
    .index-demo5 .banner-item { margin-bottom: 20px; }
    .index-demo6 #productTabs li a { font-size: 14px; padding: 5px 10px; }

    .hero .saleTime .time-count { font-size: 20px; line-height: 25px; }

    .featuredContentStyle3 h3 { font-size: 23px; }
    .featuredContentStyle3.last { margin-bottom: 30px; }

    .grid-products.style2 .row { margin-left: 0; margin-right: 0; }
    .index-demo4 .featured-grid .grid-products.style2 .row .col-12 { padding-left: 10px; padding-right: 10px; }

    .section-header.style1 { text-align:left; margin-bottom:15px; flex-direction:column; -webkit-flex-direction:column; -ms-flex-direction:column; align-items:flex-start !important; -webkit-align-items:flex-start !important; -ms--webkit-align-items:flex-start !important; }
    .section-header-left { flex:0 0 100%; -webkit-flex:0 0 100%; -ms-flex:0 0 100%; padding-right:0; margin-bottom:10px; }
    .section-header-right { text-align:left; }
    .section-header-right .btn-large { padding:10px 15px; }

    .tabs-listing .tab-container table tr th, 
    .tabs-listing .tab-container table tr td { font-size: 13px; }

    #quickview_popup #quickView { margin-bottom: 15px; }
    #pro-addtocart-popup .modal-dialog { margin-left: auto; margin-right: auto; }
    .index-demo9 .footer-3.footer .simple-text { font-size: 14px; }

    .collection-banners .row .banner-item { padding-left: 0; padding-right: 0; }
    .collection-banners .row { margin-left: 0px; margin-right: 0; }

}

@media only screen and (max-width: 480px) {
    .logo img { max-width:130px; }
    .header-6 .logo img, .header-7 .logo img { max-width: 110px; }
    .topheader .phone-no a span { display:none; }
    .iconset { padding:0; }
    .header-2 .header-cart { border-left:0; }
    .top-header p, .top-header a, .top-header select, .top-header .fa, .top-header span.selected-currency, .language-dd { font-size:11px; letter-spacing:0; }

    .mobile-icons .btn--link { padding-right:5px; }
    .container-fluid, .home15-funiture-top .container-fluid, .home15-funiture-header .container-fluid { padding: 0 15px; }

    #settingsBox { right:0; }
    .search-drawer .searchField { display:block; }
    .search-drawer .search-category { display:block; padding-right:0; border-bottom:1px solid #dedede; }
    .search-drawer .input-box { width:100%; }
    .search-drawer .search-category select { padding:0; }

    #header-cart { width:282px; }
    .cart tfoot td .btn { white-space: nowrap; padding: 8px 10px; }

    .slideshow .slideshow__title { font-size:16px; margin-bottom: 5px; }
    .collection-banners .row .banner-item { margin-bottom:15px; }

    .section-header h2 { font-size: 20px; }

    .latest-blog .wrap-blog .article__grid-image, .latest-blog .wrap-blog .article__grid-meta { display:block; }
    .latest-blog .wrap-blog .article__grid-image { text-align:center; }
    .latest-blog .wrap-blog .article__grid-meta { width:100%; }
    .latest-blog .wrap-blog .article__grid-image img { width:100%; margin-bottom:20px; }
    .latest-blog .wrap-blog .wrap-blog-inner { padding:0; }

    .sidebar-product .related-product .grid__item { width:100%; }
    .sidebar-product .related-product .grid__item:nth-child(2n+1) { clear:left; }

    .hero .text-large .mega-title { font-size:26px; }
    .hero .text-large .mega-subtitle { font-size:15px; }
    .hero .saleTime .count-inner { min-width: 63px; }

    .slideshow .slick-dots li button { width: 10px; height: 10px; }
    .collection-box .container-fluid { padding:0 15px; }
    .product-form__item--submit .btn { font-size:13px; }
    #footer { margin-top:20px; }

    .hero .text-small .mega-title { font-size:20px; }
    .hero .hero__inner span, .hero .text-small .mega-subtitle { font-size:13px; line-height:normal; }
    .hero .btn { font-size:12px; }

    .slideshow.style2 .slideshow__title { font-size:18px; margin-bottom:3px; }
    .slideshow.style2 .mega-small-title { display:none; }
    .slideshow.style2 .wrap-caption { padding:10px; }

    .blog-post-slider-style1 .blogpost-item { margin:0 0 10px; }

    .hero.hero--small { height:150px; }
    .hero.hero--small .hero__inner { padding:15px 0; }
    .hero.hero--small .hero__inner .wrap-text { max-width:210px; }
    .hero.hero--small .btn { font-size:12px; padding:5px 15px; }
    .index-demo18 .hero .mega-title { font-size:20px; }

    .index-demo1 .section-header h2 { font-size:20px; }
    .index-demo4 .hero--large { height:250px; }

    .index-demo3 .imgBanners.style2 .img-banner-item .title { font-size:16px; }
    .index-demo3 .imgBanners.style2 .details p { margin-bottom:5px; font-size:12px; }
    .index-demo3 .imgBanners.style2 .w-50 { width:90% !important; }

    .imgBanners.style3 .details .tt-small { margin-bottom:3px; }

    .grid-products.style2 .item .button-set li .btn-icon { margin:1px; }
    .grid-products .btn-addto-cart { padding-left: 8px; padding-right: 8px; }

    .home-blog-post .slick-prev { left:0; }
    .home-blog-post .slick-next { right:0; }

    .grid-products .slick-slider .item, .grid-products.slick-slider .item { padding:0; }
    .stickyCart .product-form__cart-submit { padding:0 15px; }

    .productSlider .slick-prev, .collection-box .collection-grid .slick-prev, .productPageSlider .slick-prev { left:0; }
    .productSlider .slick-next, .collection-box .collection-grid .slick-next, .productPageSlider .slick-next { right:0; }

    .store-info.style5 li { width:100%; display:block; }

    .infolinks { flex-direction:column; -webkit-flex-direction:column; -ms-flex-direction:column; margin-bottom:0; }
    .infolinks + .infolinks { margin-bottom:10px; } 
    .infolinks a { margin-bottom:5px !important; display:block; }
    .infolinks .wishlist { text-align: center; }
    .infolinks > * { padding-right: 0 !important; padding-left: 0 !important; }
    .product-form + .infolinks { margin-bottom: 15px; }

    .product-detail-container .prInfoRow { flex-direction: column; }
    .product-detail-container .prInfoRow > div { margin-right: 0; margin-top: 5px; }

    #threesixty { max-width: 310px; max-height: 403px; }
    #pro-addtocart-popup .button-action .btn { margin-bottom: 10px; }

    .index-demo7 .categories-section.style2 .item-title { font-size: 14px; }
    .index-demo9 .grid-products .item .btn-icon.btn-addto-cart span { display: none; }

}



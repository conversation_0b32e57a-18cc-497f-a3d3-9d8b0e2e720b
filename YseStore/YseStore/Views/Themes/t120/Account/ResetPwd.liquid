<div class="offcanvas-wrapper">
    <!--Body Container-->
    <!-- Start Page Title -->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>{{ "user.forgot.enterPWD"|translate}}</h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/">{{ "web.global.home"|translate}}</a></li>
                    <li class="separator">&nbsp;</li>
                    <li>{{ "user.forgot.enterPWD"|translate}}</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- End Page Title -->
    <div class="container">
        <div class="row">
            <!--Main Content-->
            <div class="col-12 col-sm-12 col-md-12 col-lg-6 box offset-lg-3">
                <div class="mb-4 mt-lg-5">
                    <form method="post"
                          hx-post="/Account/OnResetPwd" id="resetPwd-form"
                          hx-target="#resetpwd-result"
                          hx-swap="afterbegin"
                          hx-on::after-request="if(event.detail.successful){ resetPwdSubmitCallback(event.detail.xhr.responseText) }"
                          accept-charset="UTF-8" class="contact-form">
                        <h3>{{ "user.forgot.enterPWD"|translate}}</h3>
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                                <div class="form-group">
                                    <label for="CustomerPassword">{{ "user.global.password"|translate}} <span class="required">*</span></label>
                                    <input type="password" class="form-control" name="password" placeholder="{{ "user.forgot.newPWD"|translate}}" id="CustomerPassword"
                                           onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                                           hx-on:htmx:validation:validate="if(this.value == '') {
                                this.setCustomValidity('Please enter your new password')
                                   $(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}" />
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                                <div class="form-group">
                                    <label for="confirmPwd">{{ "user.global.password"|translate}} <span class="required">*</span></label>

                                    <input type="password" class="form-control" name="confirmPwd" placeholder="{{ "user.account.reg_err_PWDConfirm"|translate}}" id="confirmPwd"
                                           onkeyup="this.setCustomValidity('')"
                                           hx-on:htmx:validation:validate="if(this.value == '') {
                                              this.setCustomValidity('Please Confirm your new password')
                                                 $(this).addClass('is-invalid')
                                          }" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="text-left col-12 col-sm-12 col-md-12 col-lg-12">
                                <input type="hidden" name="uuid" value="{{Model.UUID}}" />

                                <input type="submit" class="btn mb-3 btn-primary" value="Password Reset"
                                       hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'"
                                       hx-disabled-elt="this"
                                       hx-indicator="#spinner">
                                <span id="spinner" class="htmx-indicator">
                                    <i class="icon an an-spinner an-spinner-l"></i>
                                </span>
                                <p class="mb-4">
                                    <a href="/account/signIn">« {{ "user.global.sign_in"|translate}}</a>
                                </p>
                            </div>
                        </div>
                    </form>
                    <div id="resetpwd-result"></div>
                </div>
            </div>
            <!--End Main Content-->
        </div>

    </div><!--End Body Container-->
</div>
<script>
    function resetPwdSubmitCallback(responseText) {
        const notification = document.getElementById('resetpwd-result');
        notification.classList.remove('hidden');
        const response = JSON.parse(responseText);
        if (response.status == true) {

            window.location.href = "/Account/FinishResetPwd";
            //notification.innerHTML = `
            //    <div class="alert alert-${response.status ? 'success' : 'danger'}">
            //      ${response.msg}
            //    </div>
            //  `;
            //document.getElementById('resetPwd-form').reset();
        }
        // 3秒后自动隐藏
        setTimeout(() => {
            notification.classList.add('hidden');
        }, 3000);
    }
</script>
{% layout '' %}
<div id="response" hx-target="this" hx-swap="outerHTML">
    {% if ViewData["ErrCount"]>0 %}
    <div class="alert alert-danger">
        <ul>
            {% for errItem in ViewData["Errors"] %}
            <li>{{ errItem }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <div class="form-group error" hx-target="this" hx-swap="outerHTML">
        <label>Email Address</label>
        <input type="email" class="form-control is-invalid" name="Email" value="{{Model.Email}}" placeholder="Your Email">
    </div>
    <div class="form-group">
        <label>Password</label>
        <input type="password" class="form-control is-invalid" name="Password" value="" placeholder="Your Password">
    </div>
    <div class="d-flex justify-content-between mb-4">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" value="true" name="RememberMe" id="remember">
            <label class="form-check-label" for="remember">
                Remember Me
            </label>

        </div>
        <a href="/Account/FindPwd" class="forgot-pass">Forgot Password?</a>
    </div>
    <input type="hidden" name="JumpUrl" value="{{Model.jumpUrl}}" />
    <div class="d-flex align-items-center">
        <button type="submit" hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'" class="theme-btn"><i class="far fa-sign-in"></i> Login</button>
    </div>
</div>
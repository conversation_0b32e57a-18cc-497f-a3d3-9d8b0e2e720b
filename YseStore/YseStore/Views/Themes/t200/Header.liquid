{% layout '' %}
<script src="/assets/js/common.js"></script>
<script>
    {{ Model.Js | raw }}
</script>
<!--Marquee Text-->
<div id="head_activities"></div>
<!--<div class="topbar-slider clearfix" style=" background: var(--theme-color); padding: 10px 0;">
    <div class="container-fluid">
        <div class="marquee-text">
            <div class="top-info-bar d-flex">
                <div class="flex-item center"><a href="#;"><span class="flash-icon">⚡</span>T130P – Limited Quantity Remaining! Order Now >></a></div>
                <div class="flex-item center"><a href="#;"><b><span class="flash-icon">🎧</span>Tour Guide System - Get Free Demo Kit >></a></div>
            </div>
        </div>
    </div>
</div>-->
<!--End Marquee Text-->
<!--Header-->
<div class="topheader">
    <div class="container-fluid">
        <div class="row">
            <div class="col-10 col-sm-8 col-md-5 col-lg-4">
                <p class="email"><a href=""><i style="font-size:20px;" class="icon an an-envelope"></i></a></p>
                <p class="phone-no mx-4"><a href=""><i style="font-size:18px;" class="an an-phone"></i></a></p>
                <p class="phone-no"><a style="color: var(--theme-color);font-weight: bold;"
                                       href="/pages/conviertase-en-distribuidor">Become a Dealer</a></p>
            </div>
            <div class="col-sm-4 col-md-5 col-lg-4 d-none d-md-block d-lg-block">
            </div>
            <div class="col-2 col-sm-4 col-md-2 col-lg-4 text-right">
            </div>
        </div>
    </div>
</div>
<header class="header d-flex align-items-center header-7 header-sticky" id="main-header">
    <div class="container-fluid">
        <div class="row">
            <!--Mobile Icons-->
            <div class="col-4 col-sm-4 col-md-4 d-block d-lg-none mobile-icons">
                <!--Mobile Toggle-->
                <button type="button" class="btn--link site-header__menu js-mobile-nav-toggle mobile-nav--open">
                    <i class="icon an an-times"></i>
                    <i class="an an-bars"></i>
                </button>
                <!--End Mobile Toggle-->
                <!--Search-->
                <div class="site-search iconset" id="mobile-search">
                    <i class="icon an an-search"></i>
                </div>
                <!--End Search-->
            </div>
            <!--Mobile Icons-->
            <!--Desktop Logo-->
            <div class="logo col-4 col-sm-4 col-md-4 col-lg-3 align-self-center">
                <a href="/">
                    <img src="{{ static_path }}/assets/images/logo/retekess-logo.png" alt="Retekess" title="Retekess"/>
                </a>
            </div>
            <!--End Desktop Logo-->
            <div class="col-1 col-sm-1 col-md-1 col-lg-6 align-self-center d-menu-col">
                <!--Desktop Menu - 通过HTMX加载-->
                <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav#AccessibleNav">
                    <!-- 导航菜单将通过HTMX加载 -->
                </div>
                <!--End Desktop Menu-->
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-lg-3 align-self-center icons-col text-right">
                <!--Search-->
                <div class="site-search iconset" id="desktop-search">
                    <i class="icon an an-search"></i>
                </div>
                <div class="search-drawer">
                    <div class="container">
                        <div class="block block-search">
                            <div class="block block-content">
                                <div class="searchField">
                                    <div class="input-box">
                                        <input type="text" name="q" value=""
                                               placeholder="{{ "blog.global.searchBtn" | translate }}..."
                                               class="input-text">
                                        <button type="submit" title="{{ "blog.global.searchBtn" | translate }}"
                                                class="action search" disabled=""><i class="icon an an-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--End Search-->
                <!--Setting Dropdown-->
                <div class="setting-link iconset" id="user-settings">
                    <i class="icon an an-cog"></i>
                </div>
                <div id="settingsBox">
                    <div class="customer-links">
                        {% if IsLogined == "true" %}
                            <!-- 已登录 -->
                            <div class="dropdown-menu" style="display:block;position:relative;border:none;">
                                <a class="dropdown-item"
                                   href="/Account/MyProfile">{{ "user.account.indexTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyOrders">{{ "user.account.orderTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyInbox">{{ "user.account.inboxTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyAddress">{{ "user.account.addressTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyCoupon">{{ "user.account.couponTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyWishList">{{ "user.account.favoriteTitle" | translate }}</a>
                                <!--<a class="dropdown-item"
                                   href="/Account/MyReview">{{ "user.account.reviewTitle" | translate }}</a>-->
                                <a class="dropdown-item"
                                   href="/account/SignOut">{{ "user.account.logOut" | translate }}</a>
                            </div>
                        {% else %}
                            <!-- 未登录 -->
                            <p><a href="/account/signin"
                                  class="btn theme-btn">{{ "user.global.sign_in" | translate }}</a></p>
                            <p class="text-center"><a href="/account/signup"
                                                      class="register">{{ "user.register.register_title" | translate }}</a>
                            </p>
                        {% endif %}

                    </div>

                    <div hx-get="/home/<USER>" hx-trigger="load" id="language-switch-container"></div>

                </div>
                <!--End Setting Dropdown-->
                <!--Wishlist-->
                <div class="wishlist-link iconset" id="wishlist-icon">
                    <a href="/Account/MyWishList">
                        <i class="icon an an-heart-o"></i>
                        <span class="wishlist-count">0</span>
                    </a>
                </div>
                <!--End Wishlist-->
                <!--Minicart Dropdown-->
                <div class="header-cart iconset" id="shopping-cart">
                    <a href="/cart" class="site-header__cart btn-minicart">
                        <div class="icon-in">
                            <i class="icon an an-shopping-cart"></i>
                            <span class="site-cart-count">0</span>
                        </div>
                    </a>
                </div>
                <!--End Minicart Dropdown-->
            </div>
        </div>
    </div>
</header>
<!--End Header-->
<!--Mobile Menu - 通过HTMX加载-->
<div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select=".mobile-nav-wrapper">
    <!-- 移动端导航菜单将通过HTMX加载 -->
</div>
<!--End Mobile Menu-->
<!--MiniCart Drawer-->
<!--{% assign miniCart = '/Themes/' | append: theme | append: '/Order/MiniCartDrawer' %}
    {% include miniCart -%}-->
<!--End MiniCart Drawer-->
<input type="hidden" name="operateactivity" id="operateactivity" value="{{ Model.OperateData }}"/>
<script src="/assets/js/operateactivity.js"></script>

<!-- 可视化页面配置处理脚本 -->
<script>
    // 安全的JSON解析函数
    function safeJsonParse(jsonString) {
        try {
            if (!jsonString || jsonString === 'null' || jsonString === '""' || jsonString === '') {
                return null;
            }
            return JSON.parse(jsonString);
        } catch (e) {
            console.log('JSON解析失败:', e, jsonString);
            return null;
        }
    }

    // 处理可视化页面配置
    function initVisualPageConfig() {
        try {
            // 获取可视化页面数据
            var visualPageData = null;

            {% if Model.VisualPageData %}
                try {
                    visualPageData = {{ Model.VisualPageData | json }};
                    console.log('可视化页面数据解析成功:', visualPageData);
                } catch (e) {
                    console.log('JSON解析失败:', e);
                }
            {% else %}
                console.log('Model.VisualPageData 为空');
            {% endif %}

            if (visualPageData && visualPageData.PluginsByType && visualPageData.PluginsByType.header) {
                var headerPlugins = visualPageData.PluginsByType.header;
                console.log('找到header插件:', headerPlugins);

                // 遍历header插件配置
                headerPlugins.forEach(function(plugin) {
                    console.log('处理插件:', plugin);
                    if (plugin.Settings) {
                        try {
                            var settings = JSON.parse(plugin.Settings);
                            console.log('解析到的header配置:', settings);
                            applyHeaderSettings(settings);
                        } catch (e) {
                            console.log('解析header插件配置失败:', e, plugin.Settings);
                        }
                    } else {
                        console.log('插件没有Settings配置');
                    }
                });
            } else {
                console.log('未找到header插件配置，应用默认设置');
                // 如果没有配置，应用默认显示设置
                applyHeaderSettings({
                    Search: "1",
                    User: "1",
                    ShoppingCart: "1",
                    LanguageSwitch: "1",
                    HeaderFixedPc: "0",
                    HeaderFixedMobile: "0"
                });
            }
        } catch (e) {
            console.log('初始化可视化页面配置失败:', e);
            // 出错时应用默认设置
            applyHeaderSettings({
                Search: "1",
                User: "1",
                ShoppingCart: "1",
                LanguageSwitch: "1",
                HeaderFixedPc: "0",
                HeaderFixedMobile: "0"
            });
        }
    }

    // 应用header配置设置
    function applyHeaderSettings(settings) {
        // 保存当前设置
        currentHeaderSettings = settings;
        // 搜索框控制
        if (settings.Search === "0") {
            // 隐藏搜索框
            var desktopSearch = document.getElementById('desktop-search');
            var mobileSearch = document.getElementById('mobile-search');
            if (desktopSearch) desktopSearch.style.display = 'none';
            if (mobileSearch) mobileSearch.style.display = 'none';
        } else if (settings.Search === "1") {
            // 显示搜索框
            var desktopSearch = document.getElementById('desktop-search');
            var mobileSearch = document.getElementById('mobile-search');
            if (desktopSearch) desktopSearch.style.display = 'block';
            if (mobileSearch) mobileSearch.style.display = 'block';

            // 设置搜索框占位符
            if (settings.SearchPlaceholder) {
                var searchInput = document.querySelector('.search-drawer input[name="q"]');
                if (searchInput) {
                    searchInput.placeholder = settings.SearchPlaceholder;
                }
            }
        }

        // 用户图标控制
        if (settings.User === "0") {
            // 隐藏用户设置图标
            var userSettings = document.getElementById('user-settings');
            if (userSettings) userSettings.style.display = 'none';
        } else if (settings.User === "1") {
            // 显示用户设置图标
            var userSettings = document.getElementById('user-settings');
            if (userSettings) userSettings.style.display = 'block';
        }

        // 购物车控制
        if (settings.ShoppingCart === "0") {
            // 隐藏购物车图标
            var shoppingCart = document.getElementById('shopping-cart');
            if (shoppingCart) shoppingCart.style.display = 'none';
        } else if (settings.ShoppingCart === "1") {
            // 显示购物车图标
            var shoppingCart = document.getElementById('shopping-cart');
            if (shoppingCart) shoppingCart.style.display = 'block';
        }

        // 心愿单控制（如果配置中有Wishlist字段）
        if (settings.Wishlist === "0") {
            // 隐藏心愿单图标
            var wishlistIcon = document.getElementById('wishlist-icon');
            if (wishlistIcon) wishlistIcon.style.display = 'none';
        } else if (settings.Wishlist === "1") {
            // 显示心愿单图标
            var wishlistIcon = document.getElementById('wishlist-icon');
            if (wishlistIcon) wishlistIcon.style.display = 'block';
        }

        // 语言切换控制
        if (settings.LanguageSwitch === "0") {
            // 隐藏语言切换
            var languageSwitch = document.getElementById('language-switch-container');
            if (languageSwitch) languageSwitch.style.display = 'none';
        } else if (settings.LanguageSwitch === "1") {
            // 显示语言切换
            var languageSwitch = document.getElementById('language-switch-container');
            if (languageSwitch) languageSwitch.style.display = 'block';
        }

        // 固定头部控制
        var header = document.getElementById('main-header');
        if (header) {
            // 移除之前的固定头部类
            header.classList.remove('header_fixed_top');

            // PC端固定头部控制
            if (settings.HeaderFixedPc === "1" && window.innerWidth > 768) {
                header.classList.add('header_fixed_top');
                // 延迟调用固定头部功能，确保jQuery已加载
                setTimeout(function() {
                    if (typeof $.fn.visualHeaderFixed === 'function') {
                        $(header).visualHeaderFixed(0);
                    }
                }, 100);
            }

            // 移动端固定头部控制
            if (settings.HeaderFixedMobile === "1" && window.innerWidth <= 768) {
                header.classList.add('header_fixed_top');
                // 延迟调用固定头部功能，确保jQuery已加载
                setTimeout(function() {
                    if (typeof $.fn.visualHeaderFixed === 'function') {
                        $(header).visualHeaderFixed(1);
                    }
                }, 100);
            }
        }

        console.log('Header配置已应用:', settings);
    }

    // 存储当前的header配置，用于窗口大小变化时重新应用
    var currentHeaderSettings = null;

    // 窗口大小变化时重新应用固定头部设置
    function handleWindowResize() {
        if (currentHeaderSettings) {
            var header = document.getElementById('main-header');
            if (header) {
                // 移除之前的固定头部类
                header.classList.remove('header_fixed_top');

                // 重新应用固定头部设置
                if (currentHeaderSettings.HeaderFixedPc === "1" && window.innerWidth > 768) {
                    header.classList.add('header_fixed_top');
                    setTimeout(function() {
                        if (typeof $.fn.visualHeaderFixed === 'function') {
                            $(header).visualHeaderFixed(0);
                        }
                    }, 100);
                }

                if (currentHeaderSettings.HeaderFixedMobile === "1" && window.innerWidth <= 768) {
                    header.classList.add('header_fixed_top');
                    setTimeout(function() {
                        if (typeof $.fn.visualHeaderFixed === 'function') {
                            $(header).visualHeaderFixed(1);
                        }
                    }, 100);
                }
            }
        }
    }

    // 页面加载完成后初始化配置
    document.addEventListener('DOMContentLoaded', function() {
        initVisualPageConfig();

        // 监听窗口大小变化
        window.addEventListener('resize', handleWindowResize);
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function() {
        initVisualPageConfig();
    });
</script>

<!-- 全局搜索功能脚本 -->
<script>
    // t200主题全局搜索功能实现
    function initGlobalSearchT200() {
        // 获取搜索框和搜索按钮
        const searchInput = document.querySelector('.search-drawer input[name="q"]');
        const searchButton = document.querySelector('.search-drawer .action.search');

        if (!searchInput || !searchButton) {
            console.log('t200搜索元素未找到');
            return;
        }

        // 搜索函数
        function performSearch() {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            // 构建搜索URL - 始终跳转到/shop页面
            let url = '/collections';
            let params = [];

            // 添加keyword参数
            params.push(`keyword=${encodeURIComponent(keyword)}`);

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            // 关闭搜索抽屉
            document.querySelector('.search-drawer').classList.remove('search-drawer-open');
            document.querySelector('.mask-overlay')?.remove();

            // 跳转到搜索结果页面
            window.location.href = url;
        }

        // 移除按钮的disabled属性
        searchButton.removeAttribute('disabled');

        // 绑定搜索按钮点击事件
        searchButton.addEventListener('click', function (e) {
            e.preventDefault();
            performSearch();
        });

        // 绑定回车键事件
        searchInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                performSearch();
            }
        });

    }

    // 页面加载完成后初始化搜索功能
    document.addEventListener('DOMContentLoaded', function () {
        initGlobalSearchT200();
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function () {
        initGlobalSearchT200();
    });

    $(function () {

        //获取用户配置
        GetUserConf();

    })

    //获取用户配置
    function GetUserConf() {
        $.ajax({
            url: '/Account/GetUserConf',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {

                $(".wishlist-count").text(data.wishlistCount);
                $(".site-cart-count").text(data.cartCount);
                $(".cart-count").text(data.cartCount);

            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    }

</script>
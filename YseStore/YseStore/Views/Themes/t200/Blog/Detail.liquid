<!--<PERSON> Header-->
<div class="page-header">
    <div class="page-title"><h1>{{ "blog.global.blog"|translate}} {{ "web.global.detail"|translate}}</h1></div>
</div>
<!--End Page Header-->
<div class="container pb-5 blog-single-page">
    <!-- 调试信息 -->
    {% comment %}<div class="alert alert-info">{% endcomment %}
    {% comment %}<p>Model 是否为 null: {% if Model == null %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}{% if Model != null %}{% endcomment %}
    {% comment %}<p>Model.Blog 是否为 null: {% if Model.Blog == null %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}<p>Model.Content 是否为 null: {% if Model.Content == null %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}{% if Model.Content != null %}{% endcomment %}
    {% comment %}<p>Model.Content.Content 是否为 null 或空: {% if Model.Content.Content == null or Model.Content.Content == "" %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}<p>Model.Content.Content 长度: {{ Model.Content.Content | size }}</p>{% endcomment %}
    {% comment %}{% endif %}{% endcomment %}
    {% comment %}<p>Model 对象属性: {{ Model | json }}</p>{% endcomment %}
    {% comment %}{% endif %}{% endcomment %}
    {% comment %}</div>{% endcomment %}
    <div class="row flex-column-reverse flex-md-row">
        <!--Main Content-->
        <div class="col-12 col-sm-12 col-md-9 col-lg-9 main-col">
            <div class="article">
                <!-- Article Image -->
                <div class="article_featured-image">
                    {% if Model.Blog.PicPath %}
                        <img class="blur-up ls-is-cached lazyloaded blog-detail-image" data-src="{{ Model.Blog.PicPath }}"
                             src="{{ Model.Blog.PicPath }}" alt="{{ Model.Blog.Title }}">
                    {% else %}
                        <img class="blur-up ls-is-cached lazyloaded blog-detail-image"
                             data-src="{{ static_path }}/assets/images/blog/1400x400.jpg"
                             src="{{ static_path }}/assets/images/blog/1400x400.jpg" alt="{{ Model.Blog.Title }}">
                    {% endif %}
                </div>
                <h1><a href="/Blog/Detail/{{ Model.Blog.AId }}">{{ Model.Blog.Title }}</a></h1>
                <ul class="publish-detail">
                    <li><i class="an an-user" aria-hidden="true"></i>  {{ Model.Blog.Author }}</li>
                    <li><i class="icon an an-calendar-check"></i> <span>{{ Model.Blog.StandardAccTime }}</span></li>
                    <li><i class="icon an an-comments"></i> <a href="#"> {{ Model.Blog.Comments }} {{ "blog.global.comments"|translate}}</a></li>
                </ul>
                <hr>
                <div class="rte">
                    {% if Model.Content != null and Model.Content.Content != null %}
                        {{ Model.Content.Content | raw }}
                    {% else %}
                        <p>No content available</p>
                    {% endif %}
                </div>
                <hr>
                <div class="social-sharing">
                    <a href="#" class="btn-link btn--share share-facebook" title="Share on Facebook">
                        <i class="icon an an-facebook-f" aria-hidden="true"></i> <span class="share-title"
                                                                                       aria-hidden="true">{{ "user.account.DIST_how_to_share"|translate}}</span>
                    </a>
                    <a href="#" class="btn-link btn--share share-twitter" title="Tweet on Twitter">
                        <i class="icon an an-twitter" aria-hidden="true"></i> <span class="share-title"
                                                                                    aria-hidden="true">{{ "web.global.twitterStr"|translate}}</span>
                    </a>
                    <a href="#" title="Share on google+" class="btn-link btn--share">
                        <i class="icon an an-google-plus" aria-hidden="true"></i> <span class="share-title"
                                                                                        aria-hidden="true">{{ "web.global.googleStr"|translate}}+</span>
                    </a>
                    <a href="#" class="btn-link btn--share share-pinterest" title="Pin on Pinterest">
                        <i class="icon an an-pinterest-p" aria-hidden="true"></i> <span class="share-title"
                                                                                        aria-hidden="true">{{ "web.global.pinterestStr"|translate}}</span>
                    </a>
                    <a href="#" class="btn-link btn--share share-pinterest" title="Share by Email">
                        <i class="icon an an-envelope" aria-hidden="true"></i> <span class="share-title"
                                                                                     aria-hidden="true">{{ "web.footer.email"|translate}}</span>
                    </a>
                </div>
                <div class="blog-nav clearfix">
                    <div class="nav-prev">
                        <a href="#" title="Previous"><i class="icon an an-long-arrow-alt-left"></i>
                            <span>{{ "web.global.previous"|translate}}</span></a>
                    </div>
                    <div class="nav-next">
                        <a href="#" title="Next"><span>{{ "web.global.next"|translate}}</span> <i class="icon an an-long-arrow-alt-right"></i></a>
                    </div>
                </div>
                <hr>
                <div class="blog-comment">
                    <h2>{{ "blog.global.comments"|translate}} ({{ Model.Blog.Comments }})</h2>
                    <ol class="comments-list comments-list--level--0">
                        {% for review in Model.Blog.Reviews %}
                            <li class="comments-list__item">
                                <div class="comment">
                                    <div class="comment__avatar"><img
                                                src="{{ static_path }}/assets/images/avatar-img.jpg" alt=""></div>
                                    <div class="comment__content">
                                        <div class="comment__header">
                                            <div class="comment__author">{{ review.Name }}</div>
                                            <div class="comment__reply">
                                                <button type="button" class="btn btn-xs btn-light theme-btn"
                                                        onclick="replyToComment({{ review.RId }}, '{{ review.Name }}', null)">
                                                    {{ "user.account.reply_btn"|translate}}
                                                </button>
                                            </div>
                                        </div>
                                        <!-- 评论内容 -->
                                        <div class="comment__text">{{ review.Content }}</div>
                                        <div class="comment__date">
                                            {% assign timestamp = review.AccTime %}
                                            {% assign date = timestamp | date: "%B %d, %Y" %}
                                            {{ date }}
                                        </div>
                                    </div>
                                </div>
                                {% if review.Replies.size > 0 %}
                                    <div class="comment-list__children">
                                        <ol class="comments-list comments-list--level--1">
                                            {% for reply in review.Replies %}
                                                <li class="comments-list__item">
                                                    <div class="comment">
                                                        <div class="comment__avatar"><img
                                                                    src="{{ static_path }}/assets/images/avatar-img{% if reply.IsAdmin %}1{% else %}2{% endif %}.jpg"
                                                                    alt=""></div>
                                                        <div class="comment__content">
                                                            <div class="comment__header">
                                                                <div class="comment__author">{{ reply.ReplyName }}{% if reply.IsAdmin %}
                                                                        <span class="admin-badge">(Admin)</span>{% endif %}
                                                                </div>
                                                                <div class="comment__reply">
                                                                    <button type="button"
                                                                            class="btn btn-xs btn-light theme-btn"
                                                                            onclick="replyToComment({{ review.RId }}, '{{ reply.ReplyName }}', {{ reply.ReplyId }})">
                                                                        {{ "user.account.reply_btn"|translate}}
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="comment__text">
                                                                {% if reply.ReplyToName %}
                                                                    <span class="reply-to">@{{ reply.ReplyToName }}</span>
                                                                {% endif %}
                                                                {{ reply.Content }}
                                                            </div>
                                                            <div class="comment__date">
                                                                {% assign timestamp = reply.ReplyTime %}
                                                                {% assign date = timestamp | date: "%B %d, %Y" %}
                                                                {{ date }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            {% endfor %}
                                        </ol>
                                    </div>
                                {% endif %}
                            </li>
                        {% endfor %}

                        {% if Model.Blog.Reviews.size == 0 %}
                            <li class="comments-list__item">
                                <div class="comment">
                                    <div class="comment__content">
                                        <div class="comment__text">{{ "products.goods.no_review_data"|translate}} . {{ "blog.global.leaveAReply"|translate}} !</div>
                                    </div>
                                </div>
                            </li>
                        {% endif %}
                    </ol>
                </div>
                <div class="formFeilds contact-form form-vertical">
                    <form id="comment-form" class="comment-form">
                        <h2>{{ "blog.global.leaveAReply"|translate}}</h2>
                        <!-- 回复信息提示区 -->
                        <div id="reply-info" class="alert alert-info mb-3" style="display: none;">
                            <p>{{ "user.account.reply_btn"|translate}}: <span id="reply-to-name"></span></p>
                            <button type="button" class="btn btn-sm btn-outline-secondary theme-btn" onclick="cancelReply()">
                                {{ "web.global.cancel"|translate}} {{ "user.account.reply_btn"|translate}}
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-3">
                                <div class="form-group">
                                    <input type="text" id="comment-name" name="name" placeholder="{{ "products.goods.write_your_name"|translate}}" value=""
                                           required="">
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-3">
                                <div class="form-group">
                                    <input type="email" id="comment-email" name="email" placeholder="{{ "web.global.newsletter_your_email"|translate}}"
                                           value="">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12 mb-3">
                                <div class="form-group">
                                    <textarea required="" rows="10" id="comment-content" name="content"
                                              placeholder="{{ "products.goods.write_your_review"|translate}}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                                <button type="button" id="comment-submit-btn" class="btn theme-btn"
                                        onclick="submitComment()">{{ "products.goods.writeReview"|translate}}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        {% assign blogsidebar = '/Themes/' | append: theme | append: '/Blog/BlogSidebar' %}
        {% include blogsidebar -%}
    </div>
</div>

<!-- 添加样式 -->
<style>
    .reply-to {
        color: #0066cc;
        font-weight: bold;
        margin-right: 5px;
    }

    .admin-badge {
        background-color: #4CAF50;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        margin-left: 5px;
    }
</style>

<!-- 阿里云验证码SDK -->
<script src="/js/AliyunCaptcha.js"></script>
<!-- 引入验证码脚本 -->
<script src="/businessJs/aliyun-captcha-core.js"></script>
<!-- 现代化消息提示框 -->
<script src="/js/Pop-ups/frame-message.js"></script>

<!-- 评论和回复功能脚本 -->
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化验证码（但不显示）
        initCaptcha();
    });

    // 初始化验证码
    function initCaptcha() {
        // 确保YseCaptcha已加载
        if (typeof YseCaptcha === 'undefined') {
            console.error('YseCaptcha not loaded');
            return;
        }
        // 初始化验证码
        YseCaptcha.init({
            onSuccess: function () {
                // 验证成功后处理表单提交
                processFormSubmission();
            },
            onClose: function() {
                // 用户关闭或取消验证码时恢复按钮状态
                resetSubmitButton();
            }
        });
    }
    
    // 重置提交按钮状态
    function resetSubmitButton() {
        const submitBtn = document.getElementById('comment-submit-btn');
        submitBtn.innerHTML = 'Send Message';
        submitBtn.disabled = false;
    }

    // 提交评论函数
    function submitComment() {
        // 获取表单数据
        const name = document.getElementById('comment-name').value;
        const content = document.getElementById('comment-content').value;

        // 验证表单数据
        if (!name.trim()) {
            customize_pop.warning('Please enter your name', null, null, {showIcon: false});
            return;
        }

        if (!content.trim()) {
            customize_pop.warning('Please enter your comment', null, null, {showIcon: false});
            return;
        }

        // 显示提交中的状态
        const submitBtn = document.getElementById('comment-submit-btn');
        submitBtn.innerHTML = 'Verifying...';
        submitBtn.disabled = true;

        // 触发验证码验证
        if (typeof YseCaptcha !== 'undefined') {
            YseCaptcha.verify();
        } else {
            customize_pop.error('Verification service not loaded, please refresh the page', null, null, {showIcon: false});
            resetSubmitButton();
        }
    }

    // 处理表单提交（验证码验证成功后）
    function processFormSubmission() {
        // 获取表单数据
        const name = document.getElementById('comment-name').value;
        const email = document.getElementById('comment-email').value;
        const content = document.getElementById('comment-content').value;
        const captchaVerifyParam = YseCaptcha.getVerifyParam();
        const blogId = {{ Model.BlogId }};

        // 显示提交中的状态
        const submitBtn = document.getElementById('comment-submit-btn');
        submitBtn.innerHTML = 'Submitting...';

        // 检查是否在回复评论
        const commentForm = document.getElementById('comment-form');
        const replyToId = commentForm.dataset.replyTo;

        if (replyToId) {
            // 提交回复
            const replyData = {
                name: name,
                content: content,
                isAdmin: false,
                replyToId: parseInt(commentForm.dataset.replyToId || "0"),
                replyToName: commentForm.dataset.replyToName || "",
                captchaVerifyParam: captchaVerifyParam
            };

            // 发送回复请求
            fetch(`/api/blog/comment/${replyToId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(replyData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Reply submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新回复
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit reply, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting reply:', error);
                    customize_pop.error('Failed to submit reply, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        } else {
            // 提交评论
            const commentData = {
                blogId: blogId,
                name: name,
                email: email,
                content: content,
                captchaVerifyParam: captchaVerifyParam
            };

            // 发送评论请求
            fetch('/api/blog/comment/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(commentData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Comment submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新评论
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit comment, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting comment:', error);
                    customize_pop.error('Failed to submit comment, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        }
    }

    // 回复评论函数
    function replyToComment(reviewId, name, replyId) {
        // 滚动到评论表单
        const commentForm = document.getElementById('comment-form');
        commentForm.scrollIntoView({behavior: 'smooth'});

        // 显示回复提示
        const replyInfo = document.getElementById('reply-info');
        const replyToName = document.getElementById('reply-to-name');
        replyInfo.style.display = 'block';
        replyToName.textContent = name;

        // 聚焦到表单
        document.getElementById('comment-name').focus();

        // 存储正在回复的评论ID
        commentForm.dataset.replyTo = reviewId;

        // 存储回复目标ID和名称（如果有）
        commentForm.dataset.replyToId = replyId ? replyId : 0;
        commentForm.dataset.replyToName = name || "";
    }

    // 取消回复函数
    function cancelReply() {
        // 隐藏回复提示
        document.getElementById('reply-info').style.display = 'none';

        // 清除回复ID和回复目标信息
        const commentForm = document.getElementById('comment-form');
        delete commentForm.dataset.replyTo;
        commentForm.dataset.replyToId = "0";
        commentForm.dataset.replyToName = "";
    }

    // 重置表单函数
    function resetForm() {
        document.getElementById('comment-name').value = '';
        document.getElementById('comment-email').value = '';
        document.getElementById('comment-content').value = '';

        // 重置验证码
        YseCaptcha.reset();

        // 取消回复模式
        cancelReply();
    }
</script>

<!-- 导入图片处理助手 -->
<script src="/businessJs/imageUrlHelper.js"></script>
<script>
// 页面加载完成后处理所有图片URL
document.addEventListener('DOMContentLoaded', function() {
    // 处理博客详情页面中的主图
    const blogDetailImages = document.querySelectorAll('.blog-detail-image');
    blogDetailImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为博客详情页主图使用大图尺寸
            img.src = ImageUrlHelper.getLargeUrl(img.src);
        }
        if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
            img.dataset.src = ImageUrlHelper.getLargeUrl(img.dataset.src);
        }
    });

    // 处理博客内容中的图片
    const contentImages = document.querySelectorAll('.rte img');
    contentImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为内容图片使用中等尺寸
            img.src = ImageUrlHelper.getMediumUrl(img.src);
        }
    });

    // 处理博客侧边栏中的最新博客图片
    const sidebarBlogImages = document.querySelectorAll('.sidebar-blog-image');
    sidebarBlogImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为侧边栏小图使用缩略图尺寸
            img.src = ImageUrlHelper.getThumbnailUrl(img.src);
        }
        if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
            img.dataset.src = ImageUrlHelper.getThumbnailUrl(img.dataset.src);
        }
    });
});
</script>

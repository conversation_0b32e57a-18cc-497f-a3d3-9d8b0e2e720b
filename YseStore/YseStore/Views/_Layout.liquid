<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content>
    <meta name="keywords" content>

    <title></title>
    <script src="/assets/js/htmx.min.js" defer="defer"></script>
    <link rel="stylesheet" href="/css/global.css" />

    <link href="/assets/font/global/iconfont.css" rel="stylesheet"> 

    {% assign seo= '/Themes/'| append: theme | append:'/Seo' %}
    {% include seo %}

    {% assign staticheader= '/Themes/'| append: theme | append:'/Static_header' %}
    {% include staticheader -%}

    {% rendersection scripts %}
    {{HeadCode|raw}}
    <script type="text/javascript">document.domain = '{{WebSiteDomain}}';</script>
    <script type="text/javascript">$(document).ready(function(){if (window.frames.name) {let customEvent = new CustomEvent('pageReady' , {detail : {iframeUrl : '/',DraftsId : '',PagesId : '12764',Module : 'index',Action : 'index',}});let customInterval = 0;let customEventFunc = () => {parent.window.dispatchEvent(customEvent);};customInterval = setInterval(customEventFunc, 1000);window.addEventListener('pageReadySuccess', function (event) {clearInterval(customInterval);});}})</script>
</head>

<body class="{{bodycss}}" id="{{bodyid}}">
    {% assign body= '/Themes/'| append: theme | append:'/BodyContent' %}
    {% include body%}
 
    {% rendersection staticfooter %}
 
    {{BodyCode|raw}}
  
</body>
</html>


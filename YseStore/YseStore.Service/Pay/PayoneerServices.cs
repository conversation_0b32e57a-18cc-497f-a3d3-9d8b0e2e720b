using Aop.Api.Domain;
using Entitys;
using Flurl.Http;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Const;
using YseStore.IService.Order;
using YseStore.IService.Pay;
using YseStore.Model;
using YseStore.Model.VM.Payment;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Service.Order;
using Product = YseStore.Model.VM.Payment.Payoneer.Product;

namespace YseStore.Service.Pay
{
    /// <summary>
    /// Payoneer支付服务实现类
    /// </summary>
    public class PayoneerServices : BaseServices<payment>, IPayoneerServices
    {



        private string paypal_mode = AppSettingsConstVars.payment_mode;
        private readonly IServiceProvider _serviceProvider;
        private readonly IService.Pay.IPaymentService _paymentsServices;
        private readonly IConfigService _configService;
        private readonly ILogger<PayoneerServices> _logger;
        private readonly IOrderListService _orderListService;
        private readonly IOrderProductsService _orderProductsService;
        private readonly ICurrencyService _currencyService;
        private readonly IPaymethodFeeService _paymethodFeeServices;
        private readonly ICountryService _countryService;

        public PayoneerServices(
            IServiceProvider serviceProvider,
            IService.Pay.IPaymentService paymentsServices,
            IConfigService configService,
            ILogger<PayoneerServices> logger,
            IOrderListService orderListService,
            IOrderProductsService orderProductsService,
            ICurrencyService currencyService,
            IPaymethodFeeService paymethodFeeServices, ICountryService countryService
        )
        {
            _serviceProvider = serviceProvider;
            _paymentsServices = paymentsServices;
            _configService = configService;
            _logger = logger;
            _orderListService = orderListService;
            _orderProductsService = orderProductsService;
            _currencyService = currencyService;
            _paymethodFeeServices = paymethodFeeServices;
            _countryService = countryService;
        }


        /// <summary>
        /// 获取Payoneer支付方式
        /// </summary>
        /// <returns></returns>
        public async Task<payment> GetPayoneerPayment()
        {

            //获取Payoneer配置
            var payoneerPayment = await _paymentsServices.GetUsedPaymentAsync("Payoneer");

            return payoneerPayment;

        }

        /// <summary>
        /// 获取Payoneer配置
        /// merchantCode  apiToken  storeCode 
        /// </summary>
        /// <param name="payment"></param>
        /// <returns></returns>
        public ValueTuple<string, string, string> GetPayoneerConfig(payment payment)
        {
            if (payment == null)
            {
                return ("", "", "");
            }

            var attribute = payment.Attribute.JsonToObj<Dictionary<string, string>>();
            if (attribute == null || !attribute.ContainsKey("merchantCode") || !attribute.ContainsKey("apiToken") || !attribute.ContainsKey("storeCode"))
            {
                return ("", "", "");
            }


            return (attribute["merchantCode"], attribute["apiToken"], attribute["storeCode"]);

        }


        /// <summary>
        ///     发起支付
        /// </summary>
        /// <param name="entity">实体数据</param>
        /// <returns></returns>
        public async Task<PayoneerApiCallback> PubPay(orders order, List<orders_products_list> productlist)
        {
            var jm = new PayoneerApiCallback();

            try
            {

                string PayoneerWebSite = GlobalConstVars.payoneer_snadbox_url;
                if (paypal_mode == "live")
                {
                    PayoneerWebSite = GlobalConstVars.payoneer_url;
                }

                string merchantCode = "";
                string paymentToken = "";
                string division = "";
                //获取Paypal配置
                var payoneerPayment = await GetPayoneerPayment();
                if (payoneerPayment == null)
                {
                    jm.Status = false;
                    jm.Message = "Payoneer支付方式未配置";
                    return jm;
                }

                //判断 金额是否符合PayPal支付设置  //币种，国家，

                var payoneerAttribute = GetPayoneerConfig(payoneerPayment);
                if (string.IsNullOrEmpty(payoneerAttribute.Item1) || string.IsNullOrEmpty(payoneerAttribute.Item2) || string.IsNullOrEmpty(payoneerAttribute.Item3))
                {
                    jm.Status = false;
                    jm.Message = "Payoneer支付方式配置不完整";
                    return jm;
                }

                merchantCode = payoneerAttribute.Item1;
                paymentToken = payoneerAttribute.Item2;
                division = payoneerAttribute.Item3;

                var orderCurrency = await _currencyService.GetCurrency(order.Currency);

                //获取前台币种
                var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

                //获取国家二字码
                var shipCountryCode = await _countryService.GetCountryAsync(order.ShippingCId ?? 0);
                if (shipCountryCode == null)
                {
                    jm.Status = false;
                    jm.Message = "国家不存";
                    return jm;
                }

                var billCountryCode = await _countryService.GetCountryAsync(order.BillCId ?? 0);
                if (shipCountryCode == null)
                {
                    jm.Status = false;
                    jm.Message = "国家不存";
                    return jm;
                }

                #region 如果汇率计算出来的金额跟合计金额不一致，修改shipping



                //汇率换算后的合计total
                //var subTotal = _orderListService.GetActualPayment(order, 0);


                //转换后的运费
                var currencyShipPrice = _currencyService.ShowPriceFormat(order.ShippingPrice.Value, orderCurrency, defaultCurrency);

                // 获取订单各个价格数据
                var priceData = await _orderListService.OrdersDetailPrice(order, 1, 0, false, true, new ReplaceDataModel { shippingPrice = currencyShipPrice.Item1 });

                //运费
                //decimal shipFee = priceData.ShippingPrice;

                //if (subTotal != priceData.TotalPrice)
                //{
                //    //修改运费金额
                //    shipFee = shipFee - (subTotal - priceData.TotalPrice);
                //}
                #endregion


                PayoneerModel Payoneer = new PayoneerModel();

                Payoneer.transactionId = order.OId;
                Payoneer.integration = "HOSTED";
                Payoneer.operationType = "CHARGE";
                Payoneer.division = division;
                Payoneer.country = shipCountryCode.Acronym;
                Payoneer.customer = new Model.VM.Payment.Payoneer.Customer
                {
                    number = order.UserId.ToString(),
                    name = new CustomerName
                    {
                        firstName = order.ShippingFirstName,
                        lastName = order.ShippingFirstName,
                    },
                    email = order.Email,
                    addresses = new Addresses
                    {
                        billing = new Billing
                        {
                            state = order.BillState,
                            street = order.BillAddressLine1,
                            city = order.BillCity,
                            zip = order.BillZipCode,
                            country = billCountryCode.Acronym,
                            name = new CustomerName
                            {
                                firstName = order.BillFirstName,
                                lastName = order.BillLastName,
                            },
                        },
                        shipping = new Model.VM.Payment.Payoneer.Shipping
                        {
                            state = order.ShippingState,
                            street = order.ShippingAddressLine1,
                            city = order.ShippingCity,
                            zip = order.ShippingZipCode,
                            country = shipCountryCode.Acronym,
                            name = new CustomerName
                            {
                                firstName = order.ShippingFirstName,
                                lastName = order.ShippingLastName,
                            },
                        },
                        useBillingAsShippingAddress = true,

                    },


                };
                if (!string.IsNullOrEmpty(order.ShippingPhoneNumber))
                {
                    Payoneer.customer.phones = new Phones
                    {
                        mobile = new Number
                        {
                            unstructuredNumber = order.ShippingPhoneNumber,
                        },
                    };
                }

                Payoneer.payment = new Payment
                {
                    amount = priceData.TotalPrice,
                    currency = order.Currency,
                    reference = order.OId,
                    //plannedShippingDate=new DateTime()
                };
                Payoneer.style = new Style
                {
                    language = "en_US",
                    hostedVersion = "v5",
                };

                Payoneer.products = new List<Product>();

                if (productlist != null && productlist.Count > 0)
                {
                    foreach (var product in productlist)
                    {
                        //判断产品名称长度
                        string productName = product.Name;
                        if (productName.Length >= 130)
                        {
                            productName = productName.Substring(0, 129);
                        }

                        Product goods = new Product
                        {
                            code = product.SKU,
                            name = productName,
                            quantity = product.Qty.Value,
                            amount = _currencyService.GetConvertedPrice(product.Price.Value, orderCurrency, defaultCurrency),
                            currency = order.Currency,
                            //productDescriptionUrl =  product.ProductUrlkey,
                        };
                        Payoneer.products.Add(goods);
                    }
                }

                //回调地址
                string PayoneerCallbackUrl = AppSettingsConstVars.WebSiteUrl + "/api/payments/PayoneerCallback";
                string PayoneerNotificationUrl = AppSettingsConstVars.WebSiteUrl + "/api/payments/PayoneerNotification";

                string ncuuid = AppSettingsConstVars.WebSiteUUID;

                Payoneer.callback = new Callback
                {
                    returnUrl = PayoneerCallbackUrl,
                    cancelUrl = PayoneerCallbackUrl,
                    notificationUrl = PayoneerNotificationUrl,
                    notificationHeaders = new Notificationheader[] {

                                new Notificationheader {name ="ncuuid",value=ncuuid},
                                new Notificationheader {name ="PFOrderId",value=order.OId},
                                new Notificationheader {name ="Currency",value=order.Currency},
                                new Notificationheader {name ="Amount",value=priceData.TotalPrice.ToString("F2")}
                            }
                };


                string payResp = await PayoneerWebSite
                .AllowHttpStatus("4xx,5xx,2xx")
                    .AppendPathSegment("api/lists")
                    .WithOAuthBearerToken("basic")
                    .WithBasicAuth(merchantCode, paymentToken)
                    .PostJsonAsync(Payoneer)
                    .ReceiveString();

                _logger.LogInformation("payoneer - 发起支付" + payResp);

                //RedisService.Instance.Set<T_BillPayments>($"PayoneerOrder_{payInfo.OrderNo}", payInfo, 3600 * 24 * 10);
                if (payResp.IsNullOrEmpty())
                {
                    jm.Status = false;
                    jm.Message = "payoneer支付失败";
                    return jm;
                }

                var result = payResp.JsonToObj<PayoneerResp>();

                string redirect = result.redirect?.url;
                if (redirect.IsNullOrEmpty())
                {
                    jm.Status = false;
                }
                else
                {
                    jm.Status = true;
                }
                jm.redirect = redirect;
                jm.PayoneerResp = result;

                return jm;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                jm.Status = false;
                jm.Message = ex.Message;
                return jm;
            }

        }


        /// <summary>
        /// 封装回调数据
        /// </summary>
        /// <param name="callBack"></param>
        /// <returns></returns>
        public async Task<ShopPayResult> Payoneercallback(PayoneerCallBack callBack)
        {
            var r = await Task.Run(() =>
            {
                try
                {

                    //发送给商城的数据
                    var shopResult = new ShopPayResult
                    {
                        OrderInfo = callBack.ToJson(),
                        TradeNo = callBack.longId,
                        OrderNo = callBack.transactionId,
                        PayMethod = callBack.PaymentMethod,
                        RiskInfo = callBack.resultCode,
                        PaymentStatus = callBack.interactionReason == "OK",

                    };

                    if (!string.IsNullOrEmpty(callBack.amount) && !string.IsNullOrEmpty(callBack.currency))//支付完回调
                    {

                        //pr.OrderAmount = callBack.amount;
                        //pr.OrderCurrency = callBack.currency;

                        shopResult.OrderTotal = callBack.amount;
                        shopResult.FeeCurrency = callBack.currency;

                    }
                    else //取消支付
                    {
                        shopResult.ErrorInfo = callBack.ToJson();
                        shopResult.PaymentStatus = false;

                    }

                    return shopResult;

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);

                    return new ShopPayResult
                    {
                        PaymentStatus = false,
                        ErrorInfo = ex.Message
                    };
                }
            });

            return r;


        }

        /// <summary>
        /// 发送退款信息
        /// </summary>
        /// <returns></returns>
        public async Task<V_ERP_OrderRefund> SendPayOut()
        {
            var r = await Task.Run(() =>
            {
                try
                {
                    //var req = System.Net.HttpWebRequest; HttpRequest.QueryString;

                    V_ERP_OrderRefund r = new V_ERP_OrderRefund();




                    return r;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);

                    return new V_ERP_OrderRefund();
                }

            });

            return r;

        }


        /// <summary>
        /// 计算手续费系数
        /// 币种/金额
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public async Task<ValueTuple<string, string>> GetPayoneerFee(orders order)
        {
            //获取payoneer 手续费系数
            var payTypeList = await _paymethodFeeServices.GetPaymentFeeCache(GlobalConstVars.Payoneer);


            var pkList = payTypeList.Where(it => it.Type.ToLower() == GlobalConstVars.Payoneer.ToLower()).ToList();
            if (pkList.Count > 0)
            {
                List<string> currencyList = pkList.Select(it => it.CurrencyCode).ToList();
                if (currencyList.Contains(order.Currency))
                {
                    var orderCurrency = await _currencyService.GetCurrency(order.Currency);

                    //获取前台币种
                    var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

                    //转换后的运费
                    var currencyShipPrice = _currencyService.ShowPriceFormat(order.ShippingPrice.Value, orderCurrency, defaultCurrency);


                    // 获取订单各个价格数据
                    var priceData = await _orderListService.OrdersDetailPrice(order, 1, 0, false, true, new ReplaceDataModel { shippingPrice = currencyShipPrice.Item1 });


                    //获取当前币种的系数
                    var payTypeItem = payTypeList.Where(it => it.CurrencyCode == order.Currency).FirstOrDefault();
                    if (payTypeItem != null)
                    {
                        decimal stAmount = priceData.TotalPrice;
                        decimal ptAmount = payTypeItem.Number.Value;
                        //100USD*0.031+0.3USD+(100USD-(100USD*0.031+0.3USD))*0.005

                        // pr.FeeCurrency = order.settlementCurrency;
                        string Fee = Math.Round(stAmount * 0.031m + ptAmount + (stAmount - (stAmount * 0.031m + ptAmount)) * 0.005m, 2)
                            .ToString("F2");

                        return new ValueTuple<string, string>(order.Currency, Fee);
                    }
                }
                else
                {
                    //根据网站本币币种系数计算
                    var payTypeItem = payTypeList.Where(it => it.CurrencyCode == order.ManageCurrency).FirstOrDefault();
                    if (payTypeItem != null)
                    {
                        // 获取后台订单价格数据
                        var price = await _orderListService.CalculateActualPayment(order.OrderId, 1);

                        decimal stAmount = price;
                        decimal ptAmount = payTypeItem.Number.Value;

                        //pr.FeeCurrency = order.OrderCurrency;
                        string Fee = Math.Round(stAmount * 0.031m + ptAmount + (stAmount - (stAmount * 0.031m + ptAmount)) * 0.005m, 2)
                            .ToString("F2");
                        return new ValueTuple<string, string>(order.ManageCurrency, Fee);
                    }
                }

            }

            return new("", "0");
        }


        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public async Task<WebApiCallBack<RefundResp>> Refunds(orders order, orders_refund_info refund)
        {

            string chargeId = order.PaymentId;

            WebApiCallBack<RefundResp> jm = new WebApiCallBack<RefundResp>();

            try
            {

                string PayoneerWebSite = GlobalConstVars.payoneer_snadbox_url;
                if (paypal_mode == "live")
                {
                    PayoneerWebSite = GlobalConstVars.payoneer_url;
                }

                string merchantCode = "";
                string paymentToken = "";
                string division = "";
                //获取Paypal配置
                var payoneerPayment = await GetPayoneerPayment();
                if (payoneerPayment == null)
                {
                    jm.status = false;
                    jm.msg = "Payoneer支付方式未配置";
                    return jm;
                }

                //判断 金额是否符合PayPal支付设置  //币种，国家，

                var payoneerAttribute = GetPayoneerConfig(payoneerPayment);
                if (string.IsNullOrEmpty(payoneerAttribute.Item1) || string.IsNullOrEmpty(payoneerAttribute.Item2) || string.IsNullOrEmpty(payoneerAttribute.Item3))
                {
                    jm.status = false;
                    jm.msg = "Payoneer支付方式配置不完整";
                    return jm;
                }

                merchantCode = payoneerAttribute.Item1;
                paymentToken = payoneerAttribute.Item2;
                division = payoneerAttribute.Item3;


                System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                PayoneerRefundModel Payoneer = new PayoneerRefundModel();

                Payoneer.transactionId = order.OId;

                Payoneer.payment = new Payment
                {
                    amount = refund.Amount,//退款金额
                    currency = refund.Currency,//退款币种
                    reference = order.OId,
                };

                Payoneer.products = new List<Product>();


                string partPath = $"api/charges/{chargeId}/payout";

                var payResp = await (PayoneerWebSite + partPath)
                         .AllowHttpStatus("4xx,5xx,2xx")
                          .WithOAuthBearerToken("basic")
                    .WithBasicAuth(merchantCode, paymentToken)
                               .WithHeader("Content-Type", "application/json")
                    .PostJsonAsync(Payoneer)
                     .ReceiveJson<RefundResp>();
                _logger.LogInformation("payoneer 退款" + payResp.ToJson());

                if (payResp != null)
                {
                    //var payResp = await PayoneerWebSite
                    //     .AllowHttpStatus("4xx,5xx,2xx")
                    //     .AppendPathSegment(partPath)
                    //    .WithOAuthBearerToken("basic")
                    //    .WithBasicAuth(merchantCode, paymentToken)
                    //               .WithHeader("Content-Type", "application/json")
                    //    .PostJsonAsync(Payoneer)
                    //        .ReceiveJson();

                    if (payResp.status.code == "paid_out")//退款成功
                    {
                        jm.status = true;
                    }
                    else
                    {
                        jm.status = false;
                    }
                }
                else
                {
                    jm.status = false;
                }
                jm.data = payResp;
                return jm;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                jm.status = false;
                jm.msg = ex.Message;
                return jm;
            }
        }



    }
}

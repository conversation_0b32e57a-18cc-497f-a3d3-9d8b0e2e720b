using YseStore.IService.Set;
using YseStore.Model.RequestModels.Set;
using YseStore.Model.Response.Set;
using YseStore.Repo;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using Newtonsoft.Json;
using SqlSugar;
using YseStore.Common.Cache;
using YseStore.Model;

namespace YseStore.Service.Set
{
    /// <summary>
    /// 配送地区服务实现
    /// </summary>
    public class ShippingAreaService : BaseServices<shipping_area>, IShippingAreaService
    {
        private readonly ISqlSugarClient _db;
        private readonly ICaching _caching;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="baseDal">数据访问层</param>
        /// <param name="caching">缓存服务</param>
        public ShippingAreaService(IBaseRepository<shipping_area> baseDal, ICaching caching) : base(baseDal)
        {
            _db = Db;
            _caching = caching;
        }

        /// <summary>
        /// 根据ID获取配送地区信息
        /// </summary>
        /// <param name="id">地区ID</param>
        /// <returns>配送地区信息</returns>
        public async Task<object> GetShippingAreaByIdAsync(int id)
        {
            return await Db.Queryable<shipping_area>()
                .Where(a => a.AId == id)
                .FirstAsync();
        }

        /// <summary>
        /// 获取所有配送地区列表
        /// </summary>
        /// <returns>配送地区列表</returns>
        public async Task<List<object>> GetAllShippingAreasAsync()
        {
            var areas = await Db.Queryable<shipping_area>()
                .ToListAsync();
            return areas.Cast<object>().ToList();
        }

        /// <summary>
        /// 添加配送地区
        /// </summary>
        /// <param name="request">配送地区请求模型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddShippingAreaAsync(ShippingAreaRequest request)
        {
            var area = new shipping_area
            {
                AreaName = request.AreaName,
                WeightUnit = request.WeightUnit,
                Brief = request.Brief,
                IsFreeShipping = !string.IsNullOrEmpty(request.IsFreeShipping) && request.IsFreeShipping == "1",
                FreeType = JsonConvert.SerializeObject(request.FreeType),
                FreeShippingType = request.FreeShippingType,
                FreeShippingPrice = !string.IsNullOrEmpty(request.FreeShippingPrice)
                    ? decimal.Parse(request.FreeShippingPrice)
                    : 0,
                FreeShippingWeight = !string.IsNullOrEmpty(request.FreeShippingWeight)
                    ? float.Parse(request.FreeShippingWeight)
                    : 0,
                FreeShippingQty = !string.IsNullOrEmpty(request.FreeShippingQty)
                    ? int.Parse(request.FreeShippingQty)
                    : 0,
                SId = !string.IsNullOrEmpty(request.SId) ? ushort.Parse(request.SId) : (ushort)0,
                DeliveryRange = request.DeliveryRange,
                AffixPrice = request.AffixPrice,
                IsSupportDelivery = request.IsSupportDelivery == "1"
            };

            // 开启事务
            try
            {
                Db.Ado.BeginTran();

                // 插入配送地区
                var result = await Db.Insertable(area).ExecuteReturnEntityAsync();

                // 处理价格列表
                if (result != null)
                {
                    // 原有逻辑，处理其他配送方式的价格
                    var priceItem = request.PriceList;

                    if (!string.IsNullOrEmpty(request.FixedPrice))
                    {
                        // 当 request.FixedPrice 有值时，使用固定价格
                        var fixedPriceData = $"{{\"fixedPrice\":{request.FixedPrice}}}";

                        var priceData = new shipping_price
                        {
                            AId = (int)result.AId,
                            Calculation = "fixed",
                            Data = fixedPriceData
                        };

                        await Db.Insertable(priceData).ExecuteCommandAsync();
                    }
                    else if (priceItem.Data.Length > 0)
                    {
                        // 循环处理所有价格信息
                        var priceDataList = new List<shipping_price>();

                        for (int i = 0; i < priceItem.Data.Length; i++)
                        {
                            var priceData = new shipping_price
                            {
                                AId = (int)result.AId,
                                Calculation = i < priceItem.Calculation.Length ? priceItem.Calculation[i] : "",
                                Data = ProcessPriceData(priceItem.Data[i])
                            };

                            // 如果有重量区域，设置起始和结束重量
                            if (priceItem.WeightArea != null && i < priceItem.WeightArea.Length)
                            {
                                // 尝试解析WeightArea中的JSON字符串
                                try
                                {
                                    // 规范化输入字符串，预处理可能的格式差异
                                    string weightAreaString = priceItem.WeightArea[i].Trim();

                                    // 如果是双层数组格式 [[1,2]]，提取内部数组
                                    if (weightAreaString.StartsWith("[[") && weightAreaString.EndsWith("]]"))
                                    {
                                        // 从双层数组中提取第一个内部数组
                                        JArray outerArray = JArray.Parse(weightAreaString);
                                        if (outerArray.Count > 0 && outerArray[0] is JArray)
                                        {
                                            var innerArray = (JArray)outerArray[0];
                                            if (innerArray.Count >= 2)
                                            {
                                                decimal startWeight = innerArray[0].Value<decimal>();
                                                decimal endWeight = innerArray[1].Value<decimal>();

                                                priceData.StartWeight = startWeight;
                                                priceData.EndWeight = endWeight;
                                                // 保存记录值（原始值）
                                                priceData.StartSaveWeight = startWeight;
                                                priceData.EndSaveWeight = endWeight;
                                            }
                                        }
                                    }
                                    // 如果是单层数组格式 [1,2]
                                    else if (weightAreaString.StartsWith("[") && weightAreaString.EndsWith("]"))
                                    {
                                        JArray weightArray = JArray.Parse(weightAreaString);
                                        if (weightArray.Count >= 2)
                                        {
                                            decimal startWeight = weightArray[0].Value<decimal>();
                                            decimal endWeight = weightArray[1].Value<decimal>();

                                            priceData.StartWeight = startWeight;
                                            priceData.EndWeight = endWeight;
                                            // 保存记录值（原始值）
                                            priceData.StartSaveWeight = startWeight;
                                            priceData.EndSaveWeight = endWeight;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"解析重量区域失败: {ex.Message}, 原始值: {priceItem.WeightArea[i]}");
                                }
                            }

                            priceDataList.Add(priceData);
                        }

                        // 批量插入所有价格数据
                        if (priceDataList.Count > 0)
                        {
                            await Db.Insertable(priceDataList).ExecuteCommandAsync();
                        }
                    }
                }


                // 处理CId和StatesSId，保存到shipping_country表
                if (request.CId != null && request.CId.Count > 0 && result != null)
                {
                    var countryDataList = new List<shipping_country>();

                    foreach (var cid in request.CId)
                    {
                        if (string.IsNullOrEmpty(cid))
                            continue;

                        // 获取DeliveryData
                        string deliveryData = null;
                        try
                        {
                            // 修复DeliveryData格式问题 - 避免与null直接比较
                            bool hasDeliveryData = false;

                            // 检查DeliveryData是否有效
                            if (request.DeliveryData is JsonElement jsonElement)
                            {
                                hasDeliveryData = jsonElement.ValueKind != JsonValueKind.Null
                                                  && jsonElement.ValueKind != JsonValueKind.Undefined;
                            }
                            else if (request.DeliveryData is JToken jToken)
                            {
                                hasDeliveryData = !jToken.Type.Equals(JTokenType.Null);
                            }
                            else
                            {
                                hasDeliveryData = request.DeliveryData != null;
                            }

                            if (hasDeliveryData)
                            {
                                string cidKey = $"_{cid}";
                                object dataValue = null;

                                // 从request.DeliveryData中提取对应cid的数据
                                if (request.DeliveryData is JsonElement elementData &&
                                    elementData.ValueKind == JsonValueKind.Object &&
                                    elementData.TryGetProperty(cidKey, out JsonElement cidElement))
                                {
                                    dataValue = JsonConvert.DeserializeObject(cidElement.ToString());
                                }
                                else if (request.DeliveryData is JObject jObject &&
                                         jObject.TryGetValue(cidKey, out JToken cidToken))
                                {
                                    dataValue = cidToken.ToObject<object>();
                                }

                                // 如果成功获取到数据，将其转换为正确的格式
                                if (dataValue != null)
                                {
                                    // 转换为正确格式的数组
                                    var jsonArray = new JArray();

                                    // 根据不同类型构建不同的格式
                                    if (dataValue is JObject obj)
                                    {
                                        // 处理单个对象的情况
                                        // 查找对象包含的关键属性
                                        bool processed = false;

                                        if (obj["CodeStart"] != null)
                                        {
                                            // 处理CodeStart格式
                                            var item = new JObject();
                                            item["CodeStart"] = obj["CodeStart"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        if (obj["Specify"] != null)
                                        {
                                            // 处理Specify格式
                                            var item = new JObject();
                                            item["Specify"] = obj["Specify"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        if (obj["NumberRange"] != null)
                                        {
                                            // 处理NumberRange格式
                                            var item = new JObject();
                                            item["NumberRange"] = obj["NumberRange"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        // 如果没有找到已知格式，尝试遍历所有属性
                                        if (!processed)
                                        {
                                            foreach (var prop in obj.Properties())
                                            {
                                                if (prop.Value is JObject propObj)
                                                {
                                                    // 对每个属性创建单独的对象
                                                    if (propObj["CodeStart"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["CodeStart"] = propObj["CodeStart"];
                                                        jsonArray.Add(item);
                                                    }
                                                    else if (propObj["Specify"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["Specify"] = propObj["Specify"];
                                                        jsonArray.Add(item);
                                                    }
                                                    else if (propObj["NumberRange"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["NumberRange"] = propObj["NumberRange"];
                                                        jsonArray.Add(item);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else if (dataValue is JArray array)
                                    {
                                        // 已经是数组格式，检查每个元素
                                        foreach (var item in array)
                                        {
                                            if (item is JObject itemObj)
                                            {
                                                if (itemObj["CodeStart"] != null ||
                                                    itemObj["Specify"] != null ||
                                                    itemObj["NumberRange"] != null)
                                                {
                                                    // 已经是正确格式的对象，直接添加
                                                    jsonArray.Add(item);
                                                }
                                            }
                                        }
                                    }

                                    // 如果结果为空，使用原始值
                                    if (jsonArray.Count == 0)
                                    {
                                        Console.WriteLine("无法识别DeliveryData格式，使用原始值");
                                        deliveryData = JsonConvert.SerializeObject(dataValue);
                                    }
                                    else
                                    {
                                        // 转换为字符串
                                        deliveryData = jsonArray.ToString(Formatting.None);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"解析DeliveryData失败: {ex.Message}");
                        }

                        // 当DeliveryRange=zipCode时，直接添加记录，不处理StatesSId
                        if (request.DeliveryRange == "zipCode")
                        {
                            countryDataList.Add(new shipping_country
                            {
                                SId = !string.IsNullOrEmpty(request.SId) ? ushort.Parse(request.SId) : (ushort)0,
                                AId = (ushort)result.AId,
                                CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                StatesSId = 0,
                                DeliveryRange = request.DeliveryRange,
                                DeliveryData = deliveryData
                            });
                        }
                        else
                        {
                            // 获取对应的StatesSId列表
                            var statesSIdArray = GetStatesSIdArray(request, cid);
                            if (statesSIdArray != null && statesSIdArray.Count > 0)
                            {
                                // 批量收集所有需要插入的数据
                                foreach (var statesSId in statesSIdArray)
                                {
                                    if (string.IsNullOrEmpty(statesSId))
                                        continue;

                                    countryDataList.Add(new shipping_country
                                    {
                                        SId = !string.IsNullOrEmpty(request.SId)
                                            ? ushort.Parse(request.SId)
                                            : (ushort)0,
                                        AId = (ushort)result.AId,
                                        CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                        StatesSId =
                                            !string.IsNullOrEmpty(statesSId) ? short.Parse(statesSId) : (short)0,
                                        DeliveryRange = request.DeliveryRange,
                                        DeliveryData = deliveryData
                                    });
                                }
                            }
                            else
                            {
                                // 如果没有对应的StatesSId，则使用默认值0
                                countryDataList.Add(new shipping_country
                                {
                                    SId = !string.IsNullOrEmpty(request.SId) ? ushort.Parse(request.SId) : (ushort)0,
                                    AId = (ushort)result.AId,
                                    CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                    StatesSId = 0,
                                    DeliveryRange = request.DeliveryRange,
                                    DeliveryData = deliveryData
                                });
                            }
                        }
                    }

                    // 批量插入所有数据
                    if (countryDataList.Count > 0)
                    {
                        await Db.Insertable(countryDataList).ExecuteCommandAsync();
                    }
                }

                Db.Ado.CommitTran();
                return result != null;
            }
            catch (Exception ex)
            {
                Db.Ado.RollbackTran();
                // 记录日志
                Console.WriteLine($"添加配送地区失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新配送地区
        /// </summary>
        /// <param name="request">配送地区请求模型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateShippingAreaAsync(ShippingAreaRequest request)
        {
            if (string.IsNullOrEmpty(request.SId))
                return false;

            var area = await Db.Queryable<shipping_area>()
                .FirstAsync(a => a.SId == ushort.Parse(request.SId) && a.AId == ushort.Parse(request.AId));

            if (area == null)
                return false;

            area.AreaName = request.AreaName;
            area.WeightUnit = request.WeightUnit;
            area.FreeShippingType = request.FreeShippingType;
            area.Brief = request.Brief;
            area.IsFreeShipping = !string.IsNullOrEmpty(request.IsFreeShipping) && request.IsFreeShipping == "1";
            area.FreeType = JsonConvert.SerializeObject(request.FreeType);
            area.FreeShippingPrice = !string.IsNullOrEmpty(request.FreeShippingPrice)
                ? decimal.Parse(request.FreeShippingPrice)
                : 0;
            area.FreeShippingWeight = !string.IsNullOrEmpty(request.FreeShippingWeight)
                ? float.Parse(request.FreeShippingWeight)
                : 0;
            area.FreeShippingQty =
                !string.IsNullOrEmpty(request.FreeShippingQty) ? int.Parse(request.FreeShippingQty) : 0;
            area.DeliveryRange = request.DeliveryRange;
            area.AffixPrice = request.AffixPrice;
            area.IsSupportDelivery = request.IsSupportDelivery == "1";

            try
            {
                Db.Ado.BeginTran();

                // 更新配送地区
                var updateResult = await Db.Updateable(area).ExecuteCommandHasChangeAsync();

                // 处理价格列表
                if (updateResult)
                {
                    // 先删除该地区的所有价格
                    await Db.Deleteable<shipping_price>().Where(p => p.AId == area.AId).ExecuteCommandAsync();

                    // 重新添加价格
                    var priceItem = request.PriceList;

                    if (!string.IsNullOrEmpty(request.FixedPrice))
                    {
                        // 当 request.FixedPrice 有值时，使用固定价格
                        var fixedPriceData = $"{{\"fixedPrice\":{request.FixedPrice}}}";

                        var priceData = new shipping_price
                        {
                            AId = (int)area.AId,
                            Calculation = "fixed",
                            Data = fixedPriceData
                        };

                        await Db.Insertable(priceData).ExecuteCommandAsync();
                    }
                    else if (priceItem.Data.Length > 0)
                    {
                        // 循环处理所有价格信息
                        var priceDataList = new List<shipping_price>();

                        for (int i = 0; i < priceItem.Data.Length; i++)
                        {
                            var priceData = new shipping_price
                            {
                                AId = (int)area.AId,
                                Calculation = i < priceItem.Calculation.Length ? priceItem.Calculation[i] : "",
                                Data = ProcessPriceData(priceItem.Data[i])
                            };

                            // 如果有重量区域，设置起始和结束重量
                            if (priceItem.WeightArea != null && i < priceItem.WeightArea.Length)
                            {
                                // 尝试解析WeightArea中的JSON字符串
                                try
                                {
                                    // 规范化输入字符串，预处理可能的格式差异
                                    string weightAreaString = priceItem.WeightArea[i].Trim();

                                    // 如果是双层数组格式 [[1,2]]，提取内部数组
                                    if (weightAreaString.StartsWith("[[") && weightAreaString.EndsWith("]]"))
                                    {
                                        // 从双层数组中提取第一个内部数组
                                        JArray outerArray = JArray.Parse(weightAreaString);
                                        if (outerArray.Count > 0 && outerArray[0] is JArray)
                                        {
                                            var innerArray = (JArray)outerArray[0];
                                            if (innerArray.Count >= 2)
                                            {
                                                decimal startWeight = innerArray[0].Value<decimal>();
                                                decimal endWeight = innerArray[1].Value<decimal>();

                                                priceData.StartWeight = startWeight;
                                                priceData.EndWeight = endWeight;
                                                // 保存记录值（原始值）
                                                priceData.StartSaveWeight = startWeight;
                                                priceData.EndSaveWeight = endWeight;
                                            }
                                        }
                                    }
                                    // 如果是单层数组格式 [1,2]
                                    else if (weightAreaString.StartsWith("[") && weightAreaString.EndsWith("]"))
                                    {
                                        JArray weightArray = JArray.Parse(weightAreaString);
                                        if (weightArray.Count >= 2)
                                        {
                                            decimal startWeight = weightArray[0].Value<decimal>();
                                            decimal endWeight = weightArray[1].Value<decimal>();

                                            priceData.StartWeight = startWeight;
                                            priceData.EndWeight = endWeight;
                                            // 保存记录值（原始值）
                                            priceData.StartSaveWeight = startWeight;
                                            priceData.EndSaveWeight = endWeight;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"解析重量区域失败: {ex.Message}, 原始值: {priceItem.WeightArea[i]}");
                                }
                            }

                            priceDataList.Add(priceData);
                        }

                        // 批量插入所有价格数据
                        if (priceDataList.Count > 0)
                        {
                            await Db.Insertable(priceDataList).ExecuteCommandAsync();
                        }
                    }
                }


                // 处理CId和StatesSId，更新shipping_country表
                if (request.CId != null && request.CId.Count > 0 && updateResult)
                {
                    // 先删除该地区的所有国家数据
                    await Db.Deleteable<shipping_country>().Where(c => c.AId == area.AId).ExecuteCommandAsync();

                    // 批量收集所有需要插入的数据
                    var countryDataList = new List<shipping_country>();

                    // 重新添加国家数据
                    foreach (var cid in request.CId)
                    {
                        if (string.IsNullOrEmpty(cid))
                            continue;

                        // 获取DeliveryData
                        string deliveryData = null;
                        try
                        {
                            // 修复DeliveryData格式问题 - 避免与null直接比较
                            bool hasDeliveryData = false;

                            // 检查DeliveryData是否有效
                            if (request.DeliveryData is JsonElement jsonElement)
                            {
                                hasDeliveryData = jsonElement.ValueKind != JsonValueKind.Null
                                                  && jsonElement.ValueKind != JsonValueKind.Undefined;
                            }
                            else if (request.DeliveryData is JToken jToken)
                            {
                                hasDeliveryData = !jToken.Type.Equals(JTokenType.Null);
                            }
                            else
                            {
                                hasDeliveryData = request.DeliveryData != null;
                            }

                            if (hasDeliveryData)
                            {
                                string cidKey = $"_{cid}";
                                object dataValue = null;

                                // 从request.DeliveryData中提取对应cid的数据
                                if (request.DeliveryData is JsonElement elementData &&
                                    elementData.ValueKind == JsonValueKind.Object &&
                                    elementData.TryGetProperty(cidKey, out JsonElement cidElement))
                                {
                                    dataValue = JsonConvert.DeserializeObject(cidElement.ToString());
                                }
                                else if (request.DeliveryData is JObject jObject &&
                                         jObject.TryGetValue(cidKey, out JToken cidToken))
                                {
                                    dataValue = cidToken.ToObject<object>();
                                }

                                // 如果成功获取到数据，将其转换为正确的格式
                                if (dataValue != null)
                                {
                                    // 转换为正确格式的数组
                                    var jsonArray = new JArray();

                                    // 根据不同类型构建不同的格式
                                    if (dataValue is JObject obj)
                                    {
                                        // 处理单个对象的情况
                                        // 查找对象包含的关键属性
                                        bool processed = false;

                                        if (obj["CodeStart"] != null)
                                        {
                                            // 处理CodeStart格式
                                            var item = new JObject();
                                            item["CodeStart"] = obj["CodeStart"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        if (obj["Specify"] != null)
                                        {
                                            // 处理Specify格式
                                            var item = new JObject();
                                            item["Specify"] = obj["Specify"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        if (obj["NumberRange"] != null)
                                        {
                                            // 处理NumberRange格式
                                            var item = new JObject();
                                            item["NumberRange"] = obj["NumberRange"];
                                            jsonArray.Add(item);
                                            processed = true;
                                        }

                                        // 如果没有找到已知格式，尝试遍历所有属性
                                        if (!processed)
                                        {
                                            foreach (var prop in obj.Properties())
                                            {
                                                if (prop.Value is JObject propObj)
                                                {
                                                    // 对每个属性创建单独的对象
                                                    if (propObj["CodeStart"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["CodeStart"] = propObj["CodeStart"];
                                                        jsonArray.Add(item);
                                                    }
                                                    else if (propObj["Specify"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["Specify"] = propObj["Specify"];
                                                        jsonArray.Add(item);
                                                    }
                                                    else if (propObj["NumberRange"] != null)
                                                    {
                                                        var item = new JObject();
                                                        item["NumberRange"] = propObj["NumberRange"];
                                                        jsonArray.Add(item);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else if (dataValue is JArray array)
                                    {
                                        // 已经是数组格式，检查每个元素
                                        foreach (var item in array)
                                        {
                                            if (item is JObject itemObj)
                                            {
                                                if (itemObj["CodeStart"] != null ||
                                                    itemObj["Specify"] != null ||
                                                    itemObj["NumberRange"] != null)
                                                {
                                                    // 已经是正确格式的对象，直接添加
                                                    jsonArray.Add(item);
                                                }
                                            }
                                        }
                                    }

                                    // 如果结果为空，使用原始值
                                    if (jsonArray.Count == 0)
                                    {
                                        Console.WriteLine("无法识别DeliveryData格式，使用原始值");
                                        deliveryData = JsonConvert.SerializeObject(dataValue);
                                    }
                                    else
                                    {
                                        // 转换为字符串
                                        deliveryData = jsonArray.ToString(Formatting.None);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"解析DeliveryData失败: {ex.Message}");
                        }

                        // 当DeliveryRange=zipCode时，直接添加记录，不处理StatesSId
                        if (request.DeliveryRange == "zipCode")
                        {
                            countryDataList.Add(new shipping_country
                            {
                                SId = !string.IsNullOrEmpty(request.SId) ? ushort.Parse(request.SId) : (ushort)0,
                                AId = area.AId,
                                CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                StatesSId = 0,
                                DeliveryRange = request.DeliveryRange,
                                DeliveryData = deliveryData
                            });
                        }
                        else
                        {
                            // 获取对应的StatesSId列表
                            var statesSIdArray = GetStatesSIdArray(request, cid);
                            if (statesSIdArray != null && statesSIdArray.Count > 0)
                            {
                                // 收集每个StatesSId的数据
                                foreach (var statesSId in statesSIdArray)
                                {
                                    if (string.IsNullOrEmpty(statesSId))
                                        continue;

                                    countryDataList.Add(new shipping_country
                                    {
                                        SId = !string.IsNullOrEmpty(request.SId)
                                            ? ushort.Parse(request.SId)
                                            : (ushort)0,
                                        AId = area.AId,
                                        CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                        StatesSId =
                                            !string.IsNullOrEmpty(statesSId) ? short.Parse(statesSId) : (short)0,
                                        DeliveryRange = request.DeliveryRange,
                                        DeliveryData = deliveryData
                                    });
                                }
                            }
                            else
                            {
                                // 如果没有对应的StatesSId，则使用默认值0
                                countryDataList.Add(new shipping_country
                                {
                                    SId = !string.IsNullOrEmpty(request.SId) ? ushort.Parse(request.SId) : (ushort)0,
                                    AId = area.AId,
                                    CId = !string.IsNullOrEmpty(cid) ? ushort.Parse(cid) : (ushort)0,
                                    StatesSId = 0,
                                    DeliveryRange = request.DeliveryRange,
                                    DeliveryData = deliveryData
                                });
                            }
                        }
                    }

                    // 批量插入所有数据
                    if (countryDataList.Count > 0)
                    {
                        await Db.Insertable(countryDataList).ExecuteCommandAsync();
                    }
                }

                Db.Ado.CommitTran();
                return updateResult;
            }
            catch (Exception ex)
            {
                Db.Ado.RollbackTran();
                // 记录日志
                Console.WriteLine($"更新配送地区失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除配送地区
        /// </summary>
        /// <param name="id">地区ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteShippingAreaAsync(int id)
        {
            try
            {
                Db.Ado.BeginTran();

                // 删除关联的价格数据
                await Db.Deleteable<shipping_price>().Where(p => p.AId == id).ExecuteCommandAsync();

                // 删除关联的国家数据
                await Db.Deleteable<shipping_country>().Where(c => c.AId == id).ExecuteCommandAsync();

                // 删除配送地区
                var result = await Db.Deleteable<shipping_area>()
                    .Where(a => a.AId == id)
                    .ExecuteCommandHasChangeAsync();

                Db.Ado.CommitTran();
                return result;
            }
            catch (Exception ex)
            {
                Db.Ado.RollbackTran();
                // 记录日志
                Console.WriteLine($"删除配送地区失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 处理价格数据格式，将数组格式的JSON字符串转换为对象格式
        /// </summary>
        /// <param name="dataString">原始数据字符串</param>
        /// <returns>处理后的数据字符串</returns>
        private string ProcessPriceData(string dataString)
        {
            if (string.IsNullOrEmpty(dataString))
                return dataString;

            try
            {
                // 去除首尾空格
                dataString = dataString.Trim();

                // 检查是否为数组格式的JSON字符串
                if (dataString.StartsWith("[") && dataString.EndsWith("]"))
                {
                    // 解析为JArray
                    JArray jsonArray = JArray.Parse(dataString);

                    // 如果数组不为空，取第一个元素
                    if (jsonArray.Count > 0)
                    {
                        var firstElement = jsonArray[0];

                        // 如果第一个元素是对象，返回其JSON字符串
                        if (firstElement is JObject)
                        {
                            return firstElement.ToString(Formatting.None);
                        }
                    }
                }

                // 如果不是数组格式或者是对象格式，直接返回原始数据
                return dataString;
            }
            catch (Exception ex)
            {
                // 解析失败时，记录错误并返回原始数据
                Console.WriteLine($"处理价格数据失败: {ex.Message}, 原始数据: {dataString}");
                return dataString;
            }
        }

        /// <summary>
        /// 获取指定CId对应的StatesSId数组
        /// </summary>
        /// <param name="request">请求数据</param>
        /// <param name="cid">国家/地区ID</param>
        /// <returns>StatesSId数组</returns>
        private List<string> GetStatesSIdArray(ShippingAreaRequest request, string cid)
        {
            // 首先检查基本条件
            try
            {
                // 检查StatesSId是否有效，避免使用!=null直接比较
                bool hasStatesSId = false;

                // 判断StatesSId是否为空字符串
                if (request.StatesSId is string strStateValue)
                {
                    hasStatesSId = !string.IsNullOrEmpty(strStateValue);
                }
                // 判断StatesSId是否为有效的JsonElement
                else if (request.StatesSId is System.Text.Json.JsonElement jsonElement)
                {
                    hasStatesSId = jsonElement.ValueKind != System.Text.Json.JsonValueKind.Null
                                   && jsonElement.ValueKind != System.Text.Json.JsonValueKind.Undefined;
                }
                // 判断StatesSId是否为有效的JToken
                else if (request.StatesSId is Newtonsoft.Json.Linq.JToken jToken)
                {
                    hasStatesSId = !jToken.Type.Equals(Newtonsoft.Json.Linq.JTokenType.Null)
                                   && !jToken.Type.Equals(Newtonsoft.Json.Linq.JTokenType.Undefined);
                }
                // 其他一般对象类型判断
                else
                {
                    hasStatesSId = request.StatesSId != null;
                }

                // 如果StatesSId无效，直接返回空列表
                if (!hasStatesSId)
                {
                    return new List<string>();
                }

                // 将dynamic对象转换为字符串
                string jsonString = JsonConvert.SerializeObject(request.StatesSId);

                // 使用Newtonsoft.Json解析
                JObject jsonObj = JObject.Parse(jsonString);

                // 尝试获取对应的CId值
                if (jsonObj.TryGetValue(cid, out JToken value))
                {
                    // 如果值为null或不是数组类型，返回空列表
                    if (value == null || value.Type == JTokenType.Null)
                        return new List<string>();

                    // 如果是数组类型
                    if (value.Type == JTokenType.Array)
                    {
                        JArray array = (JArray)value;

                        // 过滤掉null值
                        var result = new List<string>();
                        foreach (var item in array)
                        {
                            if (item != null && item.Type != JTokenType.Null)
                            {
                                string itemValue = item.ToString();
                                if (!string.IsNullOrEmpty(itemValue))
                                {
                                    result.Add(itemValue);
                                }
                            }
                        }

                        return result;
                    }
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                // 捕获并记录错误，但不中断程序流程
                Console.WriteLine($"获取StatesSId数组失败: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取配送地区关联的国家/地区数据
        /// </summary>
        /// <param name="areaId">地区ID</param>
        /// <returns>国家/地区数据列表</returns>
        public async Task<List<shipping_country>> GetShippingCountriesByAreaIdAsync(int areaId)
        {
            return await Db.Queryable<shipping_country>()
                .Where(c => c.AId == areaId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取配送地区的价格信息
        /// </summary>
        /// <param name="areaId">地区ID</param>
        /// <returns>价格信息</returns>
        public async Task<shipping_price> GetShippingPriceByAreaIdAsync(int areaId)
        {
            return await Db.Queryable<shipping_price>()
                .Where(a => a.AId == areaId)
                .FirstAsync();
        }

        /// <summary>
        /// 获取配送地区的所有价格节点列表
        /// </summary>
        /// <param name="areaId">地区ID</param>
        /// <returns>价格节点列表</returns>
        public async Task<List<shipping_price>> GetPriceListByAreaIdAsync(int areaId)
        {
            return await Db.Queryable<shipping_price>()
                .Where(a => a.AId == areaId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有配送区域（按大洲分组）
        /// </summary>
        /// <returns>配送区域信息</returns>
        public async Task<ContinentShippingAreaResponse> GetShippingAreaAsync()
        {
            // 定义缓存键
            string cacheKey = Consts.KEY_ShippingArea;

            // 检查缓存
            if (await _caching.ExistsAsync(cacheKey))
            {
                try
                {
                    // 从缓存获取数据
                    var cachedData = await _caching.GetAsync<ContinentShippingAreaResponse>(cacheKey);
                    if (cachedData != null)
                    {
                        return cachedData;
                    }
                }
                catch (Exception ex)
                {
                    // 缓存数据解析失败，继续执行查询
                    Console.WriteLine($"缓存数据解析失败: {ex.Message}");
                }
            }

            // 缓存不存在或获取失败，执行原始查询逻辑
            var response = new ContinentShippingAreaResponse();

            // 定义七大洲及其名称
            var continents = new Dictionary<int, string>
            {
                { 1, "亚洲" },
                { 2, "欧洲" },
                { 3, "非洲" },
                { 4, "北美洲" },
                { 5, "南美洲" },
                { 6, "大洋洲" },
                { 7, "南极洲" }
            };

            // 查询所有国家
            var countries = await _db.Queryable<country>()
                .Where(c => c.IsUsed == true)
                .OrderBy(c => c.CId)
                .ToListAsync();

            // 查询所有州/省
            var states = await _db.Queryable<country_states>()
                .ToListAsync();

            // 按大洲分组
            foreach (var continent in continents)
            {
                var continentResponse = new ContinentResponse
                {
                    ContinentId = continent.Key,
                    ContinentName = continent.Value,
                    Countries = new List<CountryResponse>()
                };

                // 获取该大洲的国家
                var continentCountries = countries
                    .Where(c => c.Continent == continent.Key)
                    .ToList();

                foreach (var country in continentCountries)
                {
                    var countryResponse = new CountryResponse
                    {
                        CId = country.CId,
                        Country = country.Country,
                        Acronym = country.Acronym.ToLower(),
                        FlagPath = country.FlagPath,
                        HasStates = country.HasState ?? false,
                        States = new List<StateResponse>()
                    };

                    // 获取该国家的州/省
                    if (countryResponse.HasStates)
                    {
                        var countryStates = states
                            .Where(s => s.CId == country.CId)
                            .OrderBy(s => s.MyOrder)
                            .ToList();

                        foreach (var state in countryStates)
                        {
                            countryResponse.States.Add(new StateResponse
                            {
                                SId = state.SId,
                                StateName = state.States
                            });
                        }
                    }

                    continentResponse.Countries.Add(countryResponse);
                }

                response.Continents.Add(continentResponse);
            }

            // 将结果存入缓存
            await _caching.SetAsync(cacheKey, response);

            return response;
        }
    }
}
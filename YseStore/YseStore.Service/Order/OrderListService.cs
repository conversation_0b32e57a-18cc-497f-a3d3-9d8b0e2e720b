using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService.Order;
using YseStore.Model.Enums;
using YseStore.Model;
using System.Diagnostics;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;
using Entitys;
using YseStore.Common;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using StackExchange.Profiling.Internal;
using System.Collections;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Newtonsoft.Json;
using YseStore.Model.Response;
using StackExchange.Redis;
using MiniExcelLibs.Csv;
using System.Data;
using System.Globalization;
using YseStore.Model.Dto;
using Newtonsoft.Json.Linq;
using System.Threading;
using System.Text.RegularExpressions;
using Azure;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Reflection.Emit;
using System.Linq.Expressions;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml.Style;
using SkiaSharp;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.Model.RequestModels.Set;
using System.Drawing;
using Microsoft.Identity.Client;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Database;
using YseStore.Service.HelpsOrder;
using YseStore.Model.VM.Order;
using YseStore.Model.VM.Payment.Paypal;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using YseStore.Model.VM.Payment;
using System.ComponentModel;
using YseStore.IService;
using YseStore.Service.HelpsUser;
using Flurl.Util;
using System.Security.Cryptography;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Drawing.Printing;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.IService.HelpsOrder;
using I18Next.Net.Internal;
using YseStore.Model.Response.Order;
using System.IO;
using static Npgsql.Replication.PgOutput.Messages.RelationMessage;
using Parlot.Fluent;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using log4net.Layout;
using YseStore.IService.Pay;
using YseStore.Service.Pay;
using System.Xml.Linq;
using Microsoft.Extensions.DependencyInjection;
using NetTaste;
using SixLabors.ImageSharp;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService.Email;
using YseStore.Service.Email;



//using MailKit.Search;

namespace YseStore.Service.Order
{
    public class OrderListService : BaseServices<orders>, IOrderListService
    {
        private readonly ILogger<OrderListService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly IHelpsCartService _helpsCartService;
        public IHelpsUserService _helpsUserService;
        public IHelpsManageService _helpsManageService;
        public IHelpsAsiaflyService _helpsAsiaflyService;
        public ICurrencyService _currencyService;
        public IHelpsCategoryService _helpsCategoryService;
        public IHelpsProductsService _helpsProductsService;
        public IHelpsWriteOrderLogService _helpsWriteOrderLogService;
        private readonly IServiceProvider _serviceProvider;

        //private readonly IPayoneerServices _payoneerServices;
        //private readonly IPayPalServices _payPalServices;
        public OrderListService(ILogger<OrderListService> logger, IConfiguration configuration, ISqlSugarClient db,
            IHelpsCartService helpsCartService, IHelpsUserService helpsUserService,
            IHelpsManageService helpsManageService, ICurrencyService currencyService, IHelpsAsiaflyService helpsAsiaflyService,
            IHelpsCategoryService helpsCategoryService, IHelpsProductsService helpsProductsService,
            IHelpsWriteOrderLogService helpsWriteOrderLogService, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _helpsCartService = helpsCartService;
            _helpsUserService = helpsUserService;
            _helpsManageService = helpsManageService;
            _helpsAsiaflyService = helpsAsiaflyService;
            _currencyService = currencyService;
            _helpsCategoryService = helpsCategoryService;
            _helpsProductsService = helpsProductsService;
            _helpsWriteOrderLogService = helpsWriteOrderLogService;
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 获取订单列表
        /// </summary>
        /// <param name="keywords"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="PaymentStatus"></param>
        /// <param name="ShippingStatus"></param>
        /// <param name="OrderTime"></param>
        /// <param name="PaymentTime"></param>
        /// <param name="PId"></param>
        /// <param name="SId"></param>
        /// <param name="TId"></param>
        /// <param name="CreateType"></param>
        /// <param name="StoreSource"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedList<OrderResponse>> QueryAsync(
            string keywords = "", string OrderStatus = "", string PaymentStatus = "", string ShippingStatus = "", string OrderTime = "", string PaymentTime = "",
                string PId = "", string SId = "", string TId = "", string CreateType = "", string StoreSource = "", string MenuSort = "",
            int pageNum = 1, int pageSize = 50)
        {
            try
            {

                //RefAsync<int> total = 0;
                //var orderList = await db.Queryable<orders>()
                //    .LeftJoin<user>((o, u) => o.UserId == u.UserId)
                //    .LeftJoin<orders_products_list>((o, u, p) => o.OrderId == p.OrderId)
                //    .LeftJoin<orders_package>((o, u, p, op) => o.OrderId == op.OrderId)
                //    .LeftJoin<orders_tags>((o, u, p, op, ot) => SqlFunc.Contains(ot.Tags, "|" + o.OrderId + "|"))

                //    .WhereIF(keywords != "", (o, u, p) => o.OId.Contains(keywords) || p.SKU.Contains(keywords))
                //    .WhereIF(keywords != "", (o, u, p) => o.OId.Contains(keywords) || p.SKU.Contains(keywords))
                //    .Select((o, u, p) => new OrderResponse()
                //    {
                //        OrderId = o.OrderId,
                //        OId = o.OId,
                //        RefererName = o.RefererName,
                //        UserId = o.UserId,
                //        ProductPrice = o.ProductPrice,
                //        ShippingPrice = o.ShippingPrice,
                //        PayAdditionalFee = o.PayAdditionalFee,
                //        Currency = o.Currency,
                //        ManageCurrency = o.ManageCurrency,
                //        Rate = o.Rate,
                //        ShippingCountry = o.ShippingCountry,
                //        PaymentMethod = o.PaymentMethod,
                //        users = new user() { Email = u.Email, FirstName = u.FirstName, LastName = u.LastName },
                //        countrys = new country() { Acronym = "", Country = o.ShippingCountry },
                //        IP = o.IP,
                //        OrderStatus = o.OrderStatus,
                //        PaymentStatus = o.PaymentStatus,
                //        ShippingStatus = o.ShippingStatus,
                //        OrderTime = o.OrderTime,

                //    }).ToPageListAsync(pageNum, pageSize, total);

                //var pList = orderList.ToPagedList(pageNum, pageSize, total);
                //return pList;
                var query = GetUserListByTypeThree(keywords, OrderStatus, PaymentStatus, ShippingStatus, OrderTime, PaymentTime,
                PId, SId, TId, CreateType, StoreSource, MenuSort);
                var totalCount = await query.CountAsync(); // 先获取总数
                if (!string.IsNullOrEmpty(MenuSort))
                {
                    if (MenuSort == "1a")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "1d")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Desc);
                    }
                    else if (MenuSort == "2a")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "2d")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Desc);
                    }
                }
                else
                {
                    query = query.OrderBy(o => o.OrderId, OrderByType.Desc);
                }

                // 执行分页查询
                var paged = await query
                    .Skip((pageNum - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var grouped = paged
                    .Select(o => (OrderResponse)o)
                    .ToList();
                var userIds = grouped.Select(p => p.UserId).ToList();
                var orderIds = grouped.Select(p => p.OrderId).ToList();
                var oIds = grouped.Select(p => p.OId).ToList();

                var userList = await db.Queryable<user>()
                     .Where(p => userIds.Contains(p.UserId))
                     .Select(p => new user
                     {
                         UserId = p.UserId,
                         Email = p.Email,
                         FirstName = p.FirstName,
                         LastName = p.LastName,
                         ConsumptionTime = p.ConsumptionTime,
                         Locked = p.Locked,
                         IsRegistered = p.IsRegistered,
                         Status = p.Status,
                         ReviewStatus = p.ReviewStatus,
                         RefererId = p.RefererId,
                         OrdersCount = p.OrdersCount,
                         PaymentOrderCount = p.PaymentOrderCount
                     })
                   .ToListAsync();
                var ordersProductsList = await db.Queryable<orders_products_list>()
                     .Where(p => orderIds.Contains(Convert.ToInt32(p.OrderId)))
                     .Select(p => new orders_products_list
                     {
                         OrderId = p.OrderId,
                         Qty = p.Qty

                     })
                   .ToListAsync();

                //// 批量获取订单产品
                //var productCounts = await db.Queryable<orders_products_list>()
                //    .Where(p => orderIds.Contains(Convert.ToInt32(p.OrderId)))
                //    .GroupBy(p => p.OrderId)
                //    .Select(g => new {
                //        OrderId = g.OrderId,
                //        TotalQty = SqlFunc.AggregateSum(g.Qty)
                //    })
                //    .ToListAsync();


                var countryList = await db.Queryable<country>()
                    .Select(x => new country
                    {
                        Acronym = x.Acronym,
                        Country = x.Country
                    })
                   .ToListAsync();

                var currencyList = await _currencyService.GetAllCurrencyCache();
                //var currencyList = db.Queryable<currency>().ToList();
                var userLabelCollection = await db.Queryable<user_label_collection>()
                        .Where(p => userIds.Contains(p.UserId))
                      .ToListAsync();

                var userMessageList = await db.Queryable<user_message>()
                    .Where(x => x.Module == "orders" && x.UserType == "user" && oIds.Contains(x.Subject))
                    .Select(x => new user_message
                    {
                        Subject = x.Subject,
                        MId = x.MId
                    })
                    .ToListAsync();
                foreach (var p in grouped)
                {
                    p.users = userList.Where(x => x.UserId == p.UserId).FirstOrDefault();
                    p.userLabelList = userLabelCollection.Where(x => x.UserId == p.UserId).ToList();
                    //var productsList = ordersProductsList.Where(x => x.OrderId == p.OrderId).ToList();
                    //p.Orders_Products_List = productsList;

                    p.ProductCount = ordersProductsList.Where(x => x.OrderId == p.OrderId).Sum(x => x.Qty) ?? 0;
                    //decimal OrderSum = Math.Round(((p.ProductPrice + p.ShippingPrice - p.PointsPrice) + ((p.ProductPrice + p.ShippingPrice - p.PointsPrice) * p.PayAdditionalFee)).Value, 2, MidpointRounding.AwayFromZero);
                    //p.OrderSum = CalculateActualPayment(p.OrderId);
                    p.OrderSymbol = currencyList.FirstOrDefault(c => c.Currency == p.ManageCurrency)?.Symbol;

                    //p.OrderSymbol = (await _currencyService.GetCurrency(p.ManageCurrency))?.Symbol;
                    p.countrys = countryList.Where(x => x.Country == p.ShippingCountry).FirstOrDefault();

                    var userMessageFirst = userMessageList.Where(x => x.Subject == p.OId).OrderByDescending(x => x.MId).FirstOrDefault();
                    if (userMessageFirst == null)
                    {
                        p.userMessageIsRead = 1;
                    }
                    else
                    {
                        p.userMessageIsRead = Convert.ToInt32(userMessageFirst.IsRead);
                    }


                }

                var resss = new PagedList<OrderResponse>(grouped, pageNum - 1, pageSize, totalCount);
                return resss;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询订单列表失败");
                return new PagedList<OrderResponse>(new List<OrderResponse>(), pageNum, pageSize, 0);
            }
        }


        /// <summary>
        /// 获取订单总价
        /// </summary>
        /// <param name="data"></param>
        /// <param name="isManage"></param>
        /// <returns></returns>
        public async Task<decimal> CalculateActualPayment(int OrderId, int isManage = 1)
        {
            try
            {


                //var data = new orders()
                //{
                //    OrderId = res.OrderId,
                //    OId = res.OId,
                //    RefererName = res.RefererName,
                //    UserId = res.UserId,
                //    ProductPrice = res.ProductPrice,
                //    ShippingPrice = res.ShippingPrice,
                //    PayAdditionalFee = res.PayAdditionalFee,
                //    Currency = res.Currency,
                //    ManageCurrency = res.ManageCurrency,
                //    ShippingCountry = res.ShippingCountry,
                //    PaymentMethod = res.PaymentMethod,
                //    PayTime =DateTimeHelper.ConvertToUnixTimestamp(res.PayTime),
                //    OrderTime = res.OrderTime,
                //    Rate = res.Rate,
                //    OrderStatus = res.OrderStatus,
                //    PaymentStatus = res.PaymentStatus,
                //    ShippingStatus = res.ShippingStatus,
                //    IP = res.IP,
                //    PointsPrice = res.PointsPrice

                //};


                var data = db.Queryable<orders>().Where(x => x.OrderId == OrderId)

                    .First();

                // 1. 获取订单总价（需实现HelpsOrders的等效方法）
                decimal ordersPrice = await OrdersPrice(data, 1, isManage, false, true);
                // 2. 获取订单退款信息
                var refundRows = db.Queryable<orders_refund_info>()
                    .Where(r => r.OrderId == data.OrderId)
                    .ToList();

                // 3. 计算成功退款总额
                decimal refundedTotalPrice = refundRows
                    .Where(r => r.Status == "success")
                    .Sum(r => r.Amount);

                // 4. 货币转换处理
                if (isManage == 1)
                {
                    refundedTotalPrice = _helpsCartService.CurrencyFloatPrice(
                       _helpsCartService.CeilPrice(refundedTotalPrice / (data.Rate > 0 ? data.Rate : 1)),
                        data.ManageCurrency
                    );
                }
                else
                {
                    refundedTotalPrice = _helpsCartService.CurrencyFloatPrice(
                        refundedTotalPrice,
                        data.Currency
                    );
                }

                // 5. 计算实际支付金额
                decimal totalPrice = ordersPrice - refundedTotalPrice;
                return totalPrice < 0 ? 0 : totalPrice;
            }
            catch (Exception ex)
            {

                throw;
            }
        }



        /// <summary>
        /// 根据搜索条件获取用户列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="UserType"></param>
        /// <param name="Tags"></param>
        /// <param name="OrderNumber"></param>
        /// <param name="OrderPrice"></param>
        /// <param name="RegTime"></param>
        /// <param name="IsNewsletter"></param>
        /// <param name="IsTaxExempt"></param>
        /// <param name="ReviewStatus"></param>
        /// <returns></returns>
        public ISugarQueryable<orders> GetUserListByTypeThree(string keywords = "", string OrderStatus = "", string PaymentStatus = "", string ShippingStatus = "", string OrderTime = "", string PaymentTime = "",
                string PId = "", string SId = "", string TId = "", string CreateType = "", string StoreSource = "", string MenuSort = "")
        {

            //Keyword=&OrderStatus=finished,voided&PaymentStatus=unpaid,pending&ShippingStatus=unshipped,partial&
            //    OrderTime=2025-04-15+00:00:00/2025-04-29+23:59:59&PaymentTime=2025-04-16+00:00:00/2025-04-29+23:59:59&
            //    PId=1,9&SId=34,35&TId=pre_order,1&CreateType=system,custom&StoreSource=shoptest.yisaier.net,test.retevis.fr
            // 参数预处理
            var OrderStatusList = new List<string>();
            List<int> OrderStatusIntList = new List<int>();
            var PaymentStatusList = new List<string>();
            var ShippingStatusList = new List<string>();
            var PIdList = new List<int>();
            var SIdList = new List<int>();
            var TIdList = new List<string>();
            var CreateTypeList = new List<string>();
            //var StoreSourceList = new List<string>();
            //订单状态
            if (!string.IsNullOrWhiteSpace(OrderStatus))
            {
                OrderStatusList = OrderStatus.Split(',').ToList();
                foreach (var description in OrderStatusList)
                {
                    var enumValue = EnumExtensions.FromDescription<OrderStatusEnum>(description);
                    OrderStatusIntList.Add((int)enumValue);
                }
            }
            //付款状态
            if (!string.IsNullOrWhiteSpace(PaymentStatus))
            {
                PaymentStatusList = PaymentStatus.Split(',').ToList();
            }
            //发货状态
            if (!string.IsNullOrWhiteSpace(ShippingStatus))
            {
                ShippingStatusList = ShippingStatus.Split(',').ToList();
            }
            //付款方式PId=1,9
            if (!string.IsNullOrWhiteSpace(PId))
            {
                PIdList = PId.Split(',').Select(int.Parse).ToList();
            }
            //物流方式SId=34,35----找不到
            if (!string.IsNullOrWhiteSpace(SId))
            {
                SIdList = SId.Split(',').Select(int.Parse).ToList();
            }
            //标签TId=pre_order,1----找不到
            if (!string.IsNullOrWhiteSpace(TId))
            {
                TIdList = TId.Split(',').ToList();
            }
            //订单类型CreateType=system,custom
            if (!string.IsNullOrWhiteSpace(CreateType))
            {
                CreateTypeList = CreateType.Split(',').ToList();
            }
            //店铺来源StoreSource=shoptest.yisaier.net,test.retevis.fr----找不到
            //if (!string.IsNullOrWhiteSpace(StoreSource))
            //{
            //    StoreSourceList = StoreSource.Split(',').ToList();
            //}
            // 时间范围处理--创建时间
            int startOrderDate = 0, endOrderDate = 1;
            if (!string.IsNullOrWhiteSpace(OrderTime))
            {
                var timeRange = OrderTime.Split('/');
                if (timeRange.Length >= 2)
                {
                    startOrderDate = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(timeRange[0]));
                    endOrderDate = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(timeRange[1]));
                }
            }
            // 时间范围处理--付款时间
            int startPaymentDate = 0, endPaymentDate = 1;
            if (!string.IsNullOrWhiteSpace(PaymentTime))
            {
                var timeRange = PaymentTime.Split('/');
                if (timeRange.Length >= 2)
                {
                    startPaymentDate = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(timeRange[0]));
                    endPaymentDate = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(timeRange[1]));
                }
            }

            #region
            //var orderNumbers = new List<int>();
            //var priceRange = new List<decimal>();
            //if (!string.IsNullOrWhiteSpace(Tags))
            //{
            //    TagsList = Tags.Split(',').ToList();
            //}
            //if (!string.IsNullOrWhiteSpace(OrderNumber))
            //{
            //    orderNumbers = OrderNumber.Split(',').Select(int.Parse).ToList();
            //}
            //if (!string.IsNullOrWhiteSpace(OrderPrice))
            //{
            //    priceRange = OrderPrice.Split(',').Select(decimal.Parse).ToList();
            //}
            #endregion
            //物流方式
            var packageOrderId = db.Queryable<orders_package>()
                .Where(p => SIdList.Contains(Convert.ToInt32(p.ShippingMethodSId)))
                .Select(p => p.OrderId).ToList();
            //搜索订单标签
            var ordersTags = db.Queryable<orders_tags>().Where(p => p.Name.Contains(keywords))
                .Select(p => p.Tags).ToList();
            // 主查询构建
            var query = db.Queryable<orders>()
                .LeftJoin<orders_products_list>((o, opl) => o.OrderId == opl.OrderId)
                .LeftJoin<orders_package>((o, opl, op) => o.OrderId == op.OrderId)
               .WhereIF(!string.IsNullOrWhiteSpace(keywords), (o, opl, op) => o.OId.Contains(keywords) || opl.SKU.Contains(keywords) || opl.Name.Contains(keywords)
               || ordersTags.Contains(o.OrderId.ToString()) || op.TrackingNumber.Contains(keywords))
               .WhereIF(!string.IsNullOrWhiteSpace(OrderStatus), o => OrderStatusIntList.Contains(o.OrderStatus))
               .WhereIF(!string.IsNullOrWhiteSpace(PaymentStatus), o => PaymentStatusList.Contains(o.PaymentStatus))
               .WhereIF(!string.IsNullOrWhiteSpace(ShippingStatus), o => ShippingStatusList.Contains(o.ShippingStatus))
               .WhereIF(!string.IsNullOrWhiteSpace(CreateType), o => CreateTypeList.Contains(o.Type))
               .WhereIF(!string.IsNullOrWhiteSpace(PId), o => PIdList.Contains(Convert.ToInt32(o.PId)))

               .WhereIF(!string.IsNullOrWhiteSpace(OrderTime), o => o.OrderTime >= startOrderDate && o.OrderTime <= endOrderDate)
               .WhereIF(!string.IsNullOrWhiteSpace(PaymentTime), o => o.PayTime >= startPaymentDate && o.PayTime <= endPaymentDate)

               .WhereIF(!string.IsNullOrWhiteSpace(SId), o => packageOrderId.Contains(o.OrderId))
            .Select(o => o);

            // 标签过滤
            if (!string.IsNullOrEmpty(TId))
            {
                query = query
                .LeftJoin<orders_tags>((o, ot) =>
                    SqlFunc.Contains(ot.Tags, "|" + o.OrderId + "|")
                ).Where((o, ot) => TIdList.Contains(ot.Name))
                .Select(o => o);

            }



            var res = query.Distinct()
                .Select(o => new orders
                {
                    OrderId = o.OrderId,
                    OId = o.OId,
                    RefererName = o.RefererName,
                    UserId = o.UserId,
                    ProductPrice = o.ProductPrice,
                    ShippingPrice = o.ShippingPrice,
                    PayAdditionalFee = o.PayAdditionalFee,
                    Currency = o.Currency,
                    ManageCurrency = o.ManageCurrency,
                    Rate = o.Rate,
                    ShippingCountry = o.ShippingCountry,
                    PaymentMethod = o.PaymentMethod,
                    PayTime = o.PayTime,
                    OrderTime = o.OrderTime,
                    OrderStatus = o.OrderStatus,
                    PaymentStatus = o.PaymentStatus,
                    ShippingStatus = o.ShippingStatus,
                    IP = o.IP,
                    PointsPrice = o.PointsPrice
                });

            //.OrderByDescending(o => o.OrderId).ToListAsync();
            return res;
        }



        /// <summary>
        /// 根据userId获取用户信息
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<user> OrdersGetOrderUserInfo(int UserId)
        {

            var res = await db.Queryable<user>()
                .Where(p => p.UserId == UserId)
                .FirstAsync();
            return res;
        }
        /// <summary>
        /// 根据userId获取订单信息
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<List<orders>> GetOrderByUserId(int UserId)
        {

            var orderslist = await db.Queryable<orders>()
                .Where(p => p.UserId == UserId)
                .ToListAsync();
            return orderslist;

        }
        /// <summary>
        /// 根据userId获取用户标签
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<List<user_label_collection>> Getuser_label_collectionByUserId(int UserId)
        {

            var userLabelCollection = await db.Queryable<user_label_collection>()
                         .Where(p => p.UserId == UserId)
                       .ToListAsync();
            return userLabelCollection;
        }
        /// <summary>
        /// 获取订单标签
        /// </summary>
        /// <returns></returns>
        public async Task<List<orders_tags>> GetOrders_Tags()
        {
            List<orders_tags> orders_TagsList = new List<orders_tags>();
            try
            {
                orders_TagsList = await db.Queryable<orders_tags>()
             .GroupBy(it => new { it.Name })
             .Select(g => new orders_tags
             {
                 Name = g.Name,
                 TId = SqlFunc.AggregateMax(g.TId)
             })
             .ToListAsync();
            }
            catch (Exception e)
            {
                return new List<orders_tags>();
                throw;
            }

            return orders_TagsList;

        }
        /// <summary>
        /// 获取订单标签数组
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserTagDtoResponse>> GetFormattedOrdersTags()
        {
            var query = await db.Queryable<orders_tags>()
                .GroupBy(it => it.Name)
                .Select(it => new UserTagDtoResponse
                {
                    Name = it.Name,
                    Value = it.Name,
                    Type = "orders_tags"
                }).ToListAsync();

            return query;
        }
        /// <summary>
        /// 获取物流方式标签
        /// </summary>
        /// <returns></returns>
        public async Task<List<shipping>> GetShipping()
        {
            try
            {
                List<shipping> shipping = new List<shipping>();
                shipping = await db.Queryable<shipping>()
                        .Where(p => p.IsUsed == 1)
                 .ToListAsync();
                return shipping;
            }
            catch (Exception e)
            {
                return new List<shipping>();
                throw;
            }
        }




        /// <summary>
        /// 根据订单号获取用户Id
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<int> GetUserIdByOrderIdAsync(int OrderId)
        {
            var orders = await db.Queryable<orders>()
                  .Where(o => o.OrderId == OrderId)
                  .FirstAsync();
            return orders.UserId ?? 0;
        }
        /// <summary>
        /// 根据订单号获取订单详情
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
		public async Task<orders> GetOrderDetailByOrderIdAsync(int OrderId)
        {
            var orders = await db.Queryable<orders>()
                  .Where(o => o.OrderId == OrderId)
                  .FirstAsync();
            return orders;
        }
        /// <summary>
        /// 根据订单号获取删除的订单产品
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<List<orders_products_delete_list>> Getorders_products_delete_list(int OrderId)
        {
            var ret = await db.Queryable<orders_products_delete_list>()
                   .Where(x => x.OrderId == OrderId)
                   .ToListAsync();
            return ret;

        }


        /// <summary>
        /// 根据订单号获取订单日志列表
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<List<orders_log>> GetordersLogListByOrderId(int OrderId)
        {
            var ordersLogList = db.Queryable<orders_log>()
                  .Where(o => o.OrderId == OrderId)
                  .ToList();
            return ordersLogList;
        }
        /// <summary>
        /// 根据订单id删除产品相关数据
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<bool> OrderdelByOId(int OrderId)
        {
            try
            {
                // 获取OId（假设Orders表有OId字段）
                var oId = db.Queryable<orders>()
                            .Where(o => o.OrderId == OrderId)
                            .Select(o => o.OId)
                            .First();

                // 执行级联删除
                db.Deleteable<orders>().Where(o => o.OrderId == OrderId).ExecuteCommand();
                db.Deleteable<orders_products_list>().Where(p => p.OrderId == OrderId).ExecuteCommand();
                db.Deleteable<orders_package>().Where(p => p.OrderId == OrderId).ExecuteCommand();
                db.Deleteable<orders_payment_info>().Where(p => p.OrderId == OrderId).ExecuteCommand();
                db.Deleteable<orders_log>().Where(l => l.OrderId == OrderId).ExecuteCommand();

                // 记录操作日志
                _helpsManageService.OperationLog("删除订单", $"删除订单ID：{oId}", "Order", "Delete");
                return true;
            }
            catch (Exception e)
            {
                return false;
                throw;
            }

        }




        /// <summary>
        /// 根据订单号获取订单日志列表
        /// </summary>
        /// <param name="OId"></param>
        /// <returns></returns>
        public async Task<MessageOrdersResponse> GetordersLogListByOId(string OId)
        {
            var orderFirst = db.Queryable<orders>()
                .Where(x => x.OId == OId)
                .First();
            var userMessageFirst = db.Queryable<user_message>()
                .Where(x => x.Module == "orders" && x.Subject == OId)
                .First();
            if (userMessageFirst == null)
            {
                userMessageFirst = new user_message();
                userMessageFirst.UserId = Convert.ToInt32(orderFirst.UserId);
                userMessageFirst.Module = "orders";
                userMessageFirst.Type = false;
                userMessageFirst.CusEmail = "";
                userMessageFirst.Subject = OId;
                userMessageFirst.Content = "No#" + OId;
                userMessageFirst.PicPath = "";
                userMessageFirst.IsRead = true;
                userMessageFirst.IsReply = false;
                userMessageFirst.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.ParentId = 0;
                userMessageFirst.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.UserType = "manager";
                userMessageFirst.VideoPath = "";
                db.Insertable(userMessageFirst).ExecuteReturnIdentity();
            }

            var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "orders" && x.Subject == OId)
                  .OrderBy(x => x.MId, OrderByType.Asc)
                  .ToList();
            db.Updateable(mesList)
              .ReSetValue(it => { it.IsRead = true; })
              .ExecuteCommand();
            var grouped = mesList
                   .Select(x => (UserMessageMessageOrdersResponse)x)
                   .ToList();
            var res = new MessageOrdersResponse()
            {
                OId = OId,
                MId = mesList.FirstOrDefault()?.MId.ToString(),
                Email = orderFirst.Email,
                Reply = grouped
            };
            return res;
        }
        /// <summary>
        /// 订单消息-管理员回复
        /// </summary>
        /// <param name="Message"></param>
        /// <param name="MsgPicPath"></param>
        /// <param name="MsgVideoPath"></param>
        /// <param name="MId"></param>
        /// <returns></returns>
        public async Task<UserMessageMessageOrdersResponse> AddordersLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId)
        {
            var userMessageFirst = db.Queryable<user_message>()
                .Where(x => x.Module == "orders" && x.MId == MId)
                .First();
            var orderFirst = db.Queryable<orders>()
                .Where(x => x.OId == userMessageFirst.Subject)
                .First();
            var id = 0;
            if (userMessageFirst != null)
            {
                userMessageFirst = new user_message();
                userMessageFirst.UserId = Convert.ToInt32(orderFirst.UserId);
                userMessageFirst.Module = "orders";
                userMessageFirst.Type = false;
                userMessageFirst.CusEmail = "";
                userMessageFirst.Subject = orderFirst.OId;
                userMessageFirst.Content = Message;
                userMessageFirst.PicPath = MsgPicPath;
                userMessageFirst.IsRead = false;
                userMessageFirst.IsReply = false;
                userMessageFirst.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.ParentId = MId;
                userMessageFirst.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.UserType = "manager";
                userMessageFirst.VideoPath = MsgVideoPath;
                id = db.Insertable(userMessageFirst).ExecuteReturnIdentity();
            }

            //Message "111"
            //MsgPicPath  "https://static.retekess.com/u_file/20250609/20250609142423_1493.jpg"
            //MsgVideoPath    ""
            //fileCover   "/manage/web/shop/images/set/default_cover.jpg"
            //MId "57"

            //          {
            //              "UserId": 35,
            //"Module": "orders",
            //"UserType": "manager",
            //"Subject": "4410844",
            //"Content": "2222",
            //"PicPath": "",
            //"IsRead": 0,
            //"AccTime": 1749465537,
            //"EditTime": 1749465537,
            //"ParentId": 60,
            //"VideoPath": "",
            //"Time": "2025-06-09 18:38:57",
            //"VideoPathCover": ""

            //  }

            if (id > 0)
            {
                var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "orders" && x.MId == id)
                  .OrderBy(x => x.MId, OrderByType.Desc)
                  .ToList();
                var res = mesList
                       .Select(x => (UserMessageMessageOrdersResponse)x)
                       .First();

                return res;
            }
            else
            {
                return new UserMessageMessageOrdersResponse();
            }
        }


        public async Task<(object, int)> OrdersTrack(string number, string carrier)
        {



            // 查询包裹信息
            var packageRow = db.Queryable<orders_package>()
                .Where(p => p.TrackingNumber == number)
                //.Select(p => new { p.OrderId, p.TrackingNumber, p.Carrier, p.ShippingMethodSId })
                .OrderBy(p => p.WId, OrderByType.Desc)
                .First();

            if (packageRow == null)
            {
                return ("No tracking data found", 0);
            }

            // 检查是否为Asiafly物流
            var isAsiafly = db.Queryable<shipping>()
                    .Where(s => s.ApiType == "asiafly" && s.SId == packageRow.ShippingMethodSId)
                    .Count();

            var trackDataAry = new List<Dictionary<string, object>>();

            if (isAsiafly > 0)
            {
                var result = GetTrack(number);
                if (result.ContainsKey("errorMsg"))
                {
                    return (result["errorMsg"], 0);
                }

                var trackResponses = result.GetValueOrDefault("trackResponses") as List<Dictionary<string, object>> ?? new List<Dictionary<string, object>>();
                foreach (var row in trackResponses)
                {
                    trackDataAry.Add(new Dictionary<string, object>
                    {
                        { "z", row.GetValueOrDefault("Content") },
                        { "a", row.GetValueOrDefault("Date") }
                    });
                }

                var response = new
                {
                    msg = new[] {
                        new {
                            number = number,
                            track = trackDataAry
                        }
                    },
                    ret = 1
                };

                return (new
                {
                    number = number,
                    track = trackDataAry
                }, 1);
            }

            //if (!trackDataAry.Any())
            //{
            //    var trackService = new Track17Service
            //    {
            //        PackageCarrier = packageRow.Carrier,
            //        TrackingNumber = number,
            //        ResCarrier = carrier
            //    };

            //    var response = trackService.GetTrack();
            //    return (new { msg = response.msg, ret = response.ret });
            //}

            return (trackDataAry, 1);


        }
        public class Track17Service
        {
            public string PackageCarrier { get; set; }
            public string TrackingNumber { get; set; }
            public string ResCarrier { get; set; }

        }
        public static Dictionary<string, object> GetTrack(string businessNo)
        {
            using (var client = new HttpClient())
            {
                var url = "your_service_url/shippings/expand";
                //var token = GetToken();

                //var requestData = new
                //{
                //    Token = token,
                //    Method = "asiafly",
                //    Route = "track",
                //    Data = new { BusinessNo = businessNo }
                //};

                //var json = JsonConvert.SerializeObject(requestData);
                //var content = new StringContent(json, Encoding.UTF8, "application/json");

                //var response = client.PostAsync(url, content).Result;
                //var result = response.Content.ReadAsStringAsync().Result;

                return JsonConvert.DeserializeObject<Dictionary<string, object>>("");
            }
        }




        /// <summary>
        /// 根据订单id返回产品总价相关数据
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns> 产品数量-产品价格-积分价格-运费-手续费-订单总价格-货币符号-支付方式</returns>
        public async Task<(int, decimal, decimal, decimal, decimal, decimal, string, string, decimal, decimal, decimal)> GetOrdersAmount(int orderid)
        {
            var OrdersModel = await db.Queryable<orders>()
                 .Where(it => it.OrderId == orderid)
                 .FirstAsync();

            var replaceData = new ReplaceDataModel
            {
                shippingPrice = -1
            };
            /// <param name="data">订单数据</param>
            /// <param name="method">显示格式 0 符号+价格，1 价格</param>
            /// <param name="isManage">后台货币 0 不计算 1 计算</param>
            /// <param name="isRefund">是否计算减去退款金额 true-计算 false-不计算</param>
            /// <param name="isDelete">是否计算减去已删除金额 true-计算 false-不计算</param>
            /// <param name="replaceData">替换参数 可传入:● shippingPrice 运费● lineItems 商品数据</param>
            var priceData = await OrdersDetailPrice(OrdersModel, 1, 1, false, true, replaceData);
            /**
    * 订单各种价格格式显示
    * @return array				订单各种价格
    * 								● ProductPrice 产品价格
    * 								● DiscountPrice 订单折扣金额
    * 								● CouponPrice 优惠券折扣金额
    * 								PointsPrice 积分
    * 								● ShippingPrice 运费
    * 								● FeePrice 手续费
    * 								● TaxPrice 税费
    * 								● TotalPrice 订单总价格
*/
            //ProductPrice: _helpsCartService.CurrencyFloatPrice(productPrice, currency),
            //        DiscountPrice: _helpsCartService.CurrencyFloatPrice(discountPrice),
            //        CouponPrice: _helpsCartService.CurrencyFloatPrice(couponPrice),
            //        PointsPrice: _helpsCartService.CurrencyFloatPrice(pointsPrice),
            //        ShippingPrice: _helpsCartService.CurrencyFloatPrice(shippingPrice),
            //        FeePrice: _helpsCartService.CurrencyFloatPrice(feePrice),
            //        TaxPrice: _helpsCartService.CurrencyFloatPrice(taxPrice),
            //        TotalPrice: _helpsCartService.CurrencyFloatPrice(totalPrice)
            var currencyRow = await _currencyService.GetAllCurrencyCache();
            var currencyList = currencyRow.Where(c => c.Currency == OrdersModel.ManageCurrency).First();

            // 产品总数
            var TotalQty = 0;
            var ordersProductsList = db.Queryable<orders_products_list>().Where(opl => opl.OrderId == OrdersModel.OrderId).ToList();
            if (ordersProductsList != null)
            {
                foreach (var item in ordersProductsList)
                {
                    TotalQty += item.Qty ?? 0;
                }
            }
            int ProductCount = TotalQty;

            string OrderSymbol = currencyList.Symbol;
            string paymentMethod = OrdersModel.PaymentMethod;
            // 产品数量-产品价格-积分价格-运费-手续费-订单总价格-货币符号-支付方式

            return (ProductCount, priceData.ProductPrice, priceData.PointsPrice, priceData.ShippingPrice, priceData.FeePrice,
                priceData.TotalPrice, OrderSymbol, paymentMethod, priceData.DiscountPrice, priceData.CouponPrice, priceData.TaxPrice);
        }
        /// <summary>
        /// 根据订单id返回退款信息---应该不知一条数据--修改了
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<decimal> GetOrderRefundByOrderIdAsync(int OrderId)
        {
            var ret = db.Queryable<orders_refund_info>()
                  .Where(o => o.OrderId == OrderId && o.Status == "success" && o.Amount > 0)
                   .Sum(r => (decimal?)r.Amount) ?? 0;
            return ret;
        }
        /// <summary>
        /// 根据订单id返回退款列表
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<List<orders_refund_info>> GetOrderRefundByOrderIdList(int OrderId)
        {
            var orders = await db.Queryable<orders_refund_info>()
                  .Where(o => o.OrderId == OrderId && o.Status == "success")
                  .ToListAsync();
            return orders;
        }
        /// <summary>
        /// 获取已经退款的运费
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<decimal> getrefundedShippingAmount(int OrderId)
        {

            var refundedPrice = 0m;
            refundedPrice = db.Queryable<orders_refund_info>()
                    .Where(r => r.OrderId == OrderId &&
                               r.Status == "success" &&
                               r.ShippingAmount > 0)
                    .Sum(r => (decimal?)r.ShippingAmount) ?? 0;


            return refundedPrice;


        }

        /// <summary>
        /// 根据userid获取共个订单，次付款
        /// </summary>
        /// <param name="userid"></param>
        /// <returns></returns>
        public async Task<(int, int)> GetOrdersPayment(int userid)
        {
            var OrdersModel = await db.Queryable<orders>()
                 .Where(it => it.UserId == userid)
                 .ToListAsync();
            var OrdersCount = OrdersModel.Count;
            var OrderStatusModel = await db.Queryable<orders>()
                 .Where(it => it.UserId == userid && it.OrderStatus == (int)OrderStatusEnum.已付款)
                 .ToListAsync();
            var PaymentCount = OrderStatusModel.Count;
            return (OrdersCount, PaymentCount);
        }

        /// <summary>
        /// 根据orderid获取包裹信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_package>> GetPackageAsync(int orderid)
        {
            var res = await db.Queryable<orders_package>()
                .Where(op => op.OrderId == orderid)
                .ToListAsync();
            return res;
        }





        /// <summary>
        /// 退款详情中根据orderid获取包裹信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public List<orders_package> ProcessOrders(int orderid)
        {
            // 获取所有原始数据
            var allOrders = db.Queryable<orders_package>()
                .Where(x => x.OrderId == orderid && x.Status == 0 && x.ProInfo != "[]")
                .ToList();

            // 按状态分组（1和2合并为一组）
            var groups = allOrders.GroupBy(o => o.Status).ToList();

            List<orders_package> result = new List<orders_package>();

            foreach (var group in groups)
            {
                // 合并ProInfo字段
                var mergedProInfo = new Dictionary<string, string>();
                foreach (var order in group)
                {
                    if (!string.IsNullOrEmpty(order.ProInfo))
                    {
                        var proDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(order.ProInfo);
                        foreach (var kvp in proDict)
                        {
                            mergedProInfo[kvp.Key] = kvp.Value;
                        }
                    }
                }

                // 获取WId最大的记录
                var maxOrder = group.OrderByDescending(o => o.WId).FirstOrDefault();

                // 创建新对象并复制最大记录的字段
                var newOrder = new orders_package
                {
                    WId = maxOrder.WId,
                    OrderId = maxOrder.OrderId,
                    TempOrderId = maxOrder.TempOrderId,
                    ParentId = maxOrder.ParentId,
                    Main = maxOrder.Main,
                    ShippingExpress = maxOrder.ShippingExpress,
                    ShippingMethodSId = maxOrder.ShippingMethodSId,
                    ShippingInsurance = maxOrder.ShippingInsurance,
                    ShippingInsurancePrice = maxOrder.ShippingInsurancePrice,
                    ShippingPrice = maxOrder.ShippingPrice,
                    ProInfo = JsonConvert.SerializeObject(mergedProInfo),
                    Weight = maxOrder.Weight,
                    OvId = maxOrder.OvId,
                    Warehouse = maxOrder.Warehouse,
                    GoodsType = maxOrder.GoodsType,
                    Status = group.Key,  // 使用分组键作为状态
                    TrackingNumber = maxOrder.TrackingNumber,
                    Carrier = maxOrder.Carrier,
                    ApiShippingExpress = maxOrder.ApiShippingExpress,
                    ApiShippingId = maxOrder.ApiShippingId,
                    ApiShippingPrice = maxOrder.ApiShippingPrice,
                    ApiShippingLabel = maxOrder.ApiShippingLabel,
                    ApiShippingExt = maxOrder.ApiShippingExt,
                    ShippingTime = maxOrder.ShippingTime,
                    Remarks = maxOrder.Remarks,
                    allVirtual = maxOrder.allVirtual
                };

                result.Add(newOrder);
            }

            return result;
        }







        /// <summary>
        /// 根据wid获取包裹信息
        /// </summary>
        /// <param name="WId"></param>
        /// <returns></returns>
        public async Task<orders_package> GetPackageByWIdAsync(int WId)
        {
            var res = await db.Queryable<orders_package>()
                .Where(op => op.WId == WId)
                .FirstAsync();
            return res;
        }
        /// <summary>
        /// 根据orderid获取订单下的产品
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_products_list>> GetProducts_Lists(int orderid)
        {
            var res = await db.Queryable<orders_products_list>()
                .Where(opl => opl.OrderId == orderid)
                .ToListAsync();
            return res;
        }
        /// <summary>
        /// 根据orderid获取订单日志
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_log>> GetOrders_Logs(int orderid)
        {
            var res = await db.Queryable<orders_log>()
                .Where(opl => opl.OrderId == orderid)
                .OrderByDescending(opl => opl.AccTime)
                .ToListAsync();
            return res;
        }
        /// <summary>
        /// 根据orderid获取订单备注日志
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_remark_log>> GetOrders_Remark_Logs(int orderid)
        {
            var res = await db.Queryable<orders_remark_log>()
                .Where(opl => opl.OrderId == orderid)
                .OrderByDescending(opl => opl.AccTime)
                .ToListAsync();
            return res;
        }
        /// <summary>
        /// 根据orderid获取订单标签
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_tags>> GetOrders_Tags(int orderid)
        {
            var res = await db.Queryable<orders_tags>()
           .Where(it => SqlFunc.Contains(it.Tags, "|" + orderid + "|"))
           .ToListAsync();
            return res;
        }
        /// <summary>
        /// 根据orderid获取订单标签--全部的标签，除了已经标记的
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_tags>> GetOrders_TagsBy(int orderid)
        {

            var res = await db.Queryable<orders_tags>()
     .Where(it => SqlFunc.Contains(it.Tags, "|" + orderid + "|"))
     .Select(it => it.Name)
     .ToListAsync();
            var TagsList = db.Queryable<orders_tags>()
                .Where(x => !res.Contains(x.Name))

                .ToList();
            return TagsList;


        }


        /// <summary>
        /// 获取订单导出菜单
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetOrder_ExportMenuList()
        {
            var order_ExportMenu = await db.Queryable<config>()
           .Where(c => c.GroupId == "orders_export" && c.Variable == "Menu")
           .FirstAsync();
            var menuItems = JsonConvert.DeserializeObject<Dictionary<string, string>>(order_ExportMenu.Value);
            return menuItems;
        }
        /// <summary>
        /// 修改订单导出菜单
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        public async Task<bool> UpdetOrder_ExportAry(string Value)
        {
            var user_ExportMenu = await db.Queryable<config>()
         .Where(c => c.GroupId == "orders_export" && c.Variable == "Menu")
         .FirstAsync();
            user_ExportMenu.Value = Value;
            var b = await db.Updateable(user_ExportMenu).UpdateColumns(it => new { it.Value }).WhereColumns(it => new { it.GroupId, it.Variable }).ExecuteCommandHasChangeAsync();
            return b;
        }
        /// <summary>
        /// 订单状态修改为已完成
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> OrdersStatusSuccess(string id)
        {
            List<int> orderIds = id?.Split("-").Select(int.Parse).ToList() ?? new List<int>();

            if (!orderIds.Any()) return false;

            // 批量更新订单状态
            var affectedRows = await db.Updateable<orders>()
                .SetColumns(it => new orders { OrderStatus = 6 })  // 设置新状态值
                .Where(it => orderIds.Contains(it.OrderId))        // 更新条件
                .ExecuteCommandAsync();

            return affectedRows > 0;
        }



        /// <summary>
        /// 获取订单导出菜单String
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetOrder_ExportMenuStr()
        {
            var user_ExportMenu = await db.Queryable<config>()
           .Where(c => c.GroupId == "orders_export" && c.Variable == "Menu")
           .FirstAsync();
            return user_ExportMenu.Value;
        }
        /// <summary>
        /// 获取订单导出数据
        /// </summary>
        /// <param name="Type"></param>
        /// <param name="Email"></param>
        /// <param name="CurrencyUnit"></param>
        /// <param name="keyword"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="PaymentStatus"></param>
        /// <param name="ShippingStatus"></param>
        /// <param name="OrderTime"></param>
        /// <param name="PaymentTime"></param>
        /// <param name="PId"></param>
        /// <param name="SId"></param>
        /// <param name="TId"></param>
        /// <param name="CreateType"></param>
        /// <param name="StoreSource"></param>
        /// <param name="currentPageCount"></param>
        /// <param name="currentPage"></param>
        /// <param name="id_list"></param>
        /// <param name="refunded"></param>
        /// <param name="shipped"></param>
        /// <param name="outOfStock"></param>
        /// <param name="preSale"></param>
        /// <returns></returns>
        public async Task<List<OrderExplodeResponse>> GetOrderExplode(string Type, string Email, string CurrencyUnit, string keyword, string OrderStatus, string PaymentStatus, string ShippingStatus,
                    string OrderTime, string PaymentTime, string PId, string SId, string TId, string CreateType, string StoreSource, int currentPageCount, int currentPage, string id_list,
                    string refunded, string shipped, string outOfStock, string preSale, string MenuSort)
        {




            //多余的两个选项还没处理
            List<OrderExplodeResponse> orderExplodeResponseList = new List<OrderExplodeResponse>();
            List<orders> orderList = new List<orders>();
            List<int> ints = new List<int>();
            if (Type == "0")
            {
                ints = id_list.Split("-").Select(int.Parse).ToList();
                orderList = await db.Queryable<orders>()
                    .Where(p => ints.Contains(p.OrderId))
                    .OrderByDescending(p => p.OrderId)
                    .ToListAsync();
            }
            else if (Type == "1")
            {
                var query = GetUserListByTypeThree(keyword, OrderStatus, PaymentStatus, ShippingStatus, OrderTime, PaymentTime,
                 PId, SId, TId, CreateType, StoreSource, MenuSort);
                if (!string.IsNullOrEmpty(MenuSort))
                {
                    if (MenuSort == "1a")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "1d")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Desc);
                    }
                    else if (MenuSort == "2a")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "2d")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Desc);
                    }
                }
                else
                {
                    query = query.OrderBy(o => o.OrderId, OrderByType.Desc);
                }
                orderList = await query.ToPageListAsync(currentPage, currentPageCount);
            }
            else if (Type == "2")
            {
                orderList = await db.Queryable<orders>().OrderByDescending(p => p.OrderId).ToListAsync();
            }
            else if (Type == "3")
            {
                var query = GetUserListByTypeThree(keyword, OrderStatus, PaymentStatus, ShippingStatus, OrderTime, PaymentTime,
                 PId, SId, TId, CreateType, StoreSource, MenuSort);
                if (!string.IsNullOrEmpty(MenuSort))
                {
                    if (MenuSort == "1a")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "1d")
                    {
                        query = query.OrderBy(o => o.PayTime, OrderByType.Desc);
                    }
                    else if (MenuSort == "2a")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Asc);
                    }
                    else if (MenuSort == "2d")
                    {
                        query = query.OrderBy(o => o.OrderTime, OrderByType.Desc);
                    }
                }
                else
                {
                    query = query.OrderBy(o => o.OrderId, OrderByType.Desc);
                }
                orderList = await query.ToListAsync();
            }

            #region 排除产品
            //if (refunded == "1")
            //{
            //    orderList = orderList.Where(x => x.PaymentStatus != "refunded").ToList();
            //}
            //if (shipped == "1")
            //{
            //    orderList = orderList.Where(x => x.OrderStatus != 5).ToList();
            //}
            //if (outOfStock == "1")
            //{
            //    //var OrderIdList = orderList.Select(x => x.OrderId).ToList();
            //    //var ProIdList = db.Queryable<orders_products_list>()
            //    //    .Where(x => OrderIdList.Contains(Convert.ToInt32(x.OrderId)))
            //    //    .Select(x=>x.ProId)
            //    //    .ToList();
            //    //var StockProId = db.Queryable<products_selected_attribute_combination>()
            //    //    .Where(x => x.Stock>0&& ProIdList.Contains(x.ProId))
            //    //    .Select(x=>x.ProId)
            //    //    .ToList();
            //    //var sadfsadf = db.Queryable<orders_products_list>()
            //    //    .Where(x => StockProId.Contains(x.ProId))
            //    //    .Select(x => x.OrderId).ToList();
            //    //orderList= orderList.Where(x => sadfsadf.Contains(x.OrderId)).ToList();

            //    // 提取原始订单ID列表
            //    var originalOrderIds = orderList.Select(x => x.OrderId).ToList();

            //    // 单次查询：直接获取有库存商品的订单ID
            //    var validOrderIds = db.Queryable<orders_products_list, products_selected_attribute_combination>(
            //        (opl, psac) => new JoinQueryInfos(
            //            JoinType.Inner,
            //            opl.ProId == psac.ProId && psac.Stock > 0  // 关联条件 + 库存过滤
            //        ))
            //        .Where((opl, psac) => originalOrderIds.Contains(Convert.ToInt32(opl.OrderId)))  // 仅处理原始订单
            //        .Select((opl, psac) => opl.OrderId)  // 选择订单ID
            //        .Distinct()  // 去重
            //        .ToList();

            //    // 过滤原始订单列表
            //    orderList = orderList.Where(x => validOrderIds.Contains(x.OrderId)).ToList();

            //}
            #endregion

            orderExplodeResponseList = orderList.Select(p => (OrderExplodeResponse)p).ToList();
            var orderIds = orderList.Select(p => p.OrderId).ToList();
            var userIds = orderList.Select(p => p.UserId).ToList();
            // 包裹信息
            var ordersPackageList = db.Queryable<orders_package>()
                .Where(it => orderIds.Contains(Convert.ToInt32(it.OrderId)))
                .ToList();
            //产品信息
            var ordersProductsList = db.Queryable<orders_products_list>()
                .Where(it => orderIds.Contains(Convert.ToInt32(it.OrderId)))
                .ToList();
            var ProIds = ordersProductsList.Select(x => x.ProId).ToList();

            //支付信息
            var ordersPaymentlist = db.Queryable<orders_payment_info>()
               .Where(x => orderIds.Contains(Convert.ToInt32(x.OrderId)))
               .ToList();
            //用户信息
            //var userlist = db.Queryable<user>()
            //   .Where(x => userIds.Contains(Convert.ToInt32(x.UserId)))
            //   .ToList();
            //金额单位
            var currencyList = await _currencyService.GetAllCurrencyCache();
            //var currencyList = db.Queryable<currency>().ToList();
            //订单备注  ( 此备注仅提供后台管理员查看 )
            var ordersRemarkLogList = db.Queryable<orders_remark_log>()
                .Where(x => orderIds.Contains(Convert.ToInt32(x.OrderId)))
                .ToList();
            //退款信息
            var ordersRefundInfoList = db.Queryable<orders_refund_info>()
                .Where(x => orderIds.Contains(Convert.ToInt32(x.OrderId)))
                .ToList();
            //客户备注
            var userRemarkLogList = db.Queryable<user_remark_log>()
                .Where(x => userIds.Contains(Convert.ToInt32(x.UserId)))
                .ToList();
            var productsSelectedAttributeCombinationList = db.Queryable<products_selected_attribute_combination>()
                .Where(x => ProIds.Contains(x.ProId)).ToList();

            var deleteProductsList = db.Queryable<orders_products_delete_list>()
                                   .Where(x => orderIds.Contains(Convert.ToInt32(x.OrderId)))
                                   .ToList();
            orderExplodeResponseList.ForEach(async p =>
            {


                string Currency = p.ManageCurrency;
                decimal Rate = 1;
                var OrderSymbol = "";
                var priceData = (0m, 0m, 0m, 0m, 0m, 0m, 0m, 0m);
                var paymentPrice = 0m;
                var replaceData = new ReplaceDataModel
                {
                    shippingPrice = -1
                };
                if (CurrencyUnit == "checkout")//结账货币 
                {
                    Rate = p.Rate;
                    OrderSymbol = currencyList.FirstOrDefault(c => c.Currency == p.Currency)?.Symbol;
                    Currency = p.Currency;

                    /// <param name="data">订单数据</param>
                    /// <param name="method">显示格式 0 符号+价格，1 价格</param>
                    /// <param name="isManage">后台货币 0 不计算 1 计算</param>
                    /// <param name="isRefund">是否计算减去退款金额 true-计算 false-不计算</param>
                    /// <param name="isDelete">是否计算减去已删除金额 true-计算 false-不计算</param>
                    /// <param name="replaceData">替换参数 可传入:● shippingPrice 运费● lineItems 商品数据</param>
                    priceData = await OrdersDetailPrice(orderList.Where(x => x.OrderId == p.OrderId).FirstOrDefault(), 1, 0, true, true, replaceData);

                    //paymentPrice = await actualPayment(orderList.Where(x => x.OrderId == p.OrderId).FirstOrDefault());
                    //paymentPrice = _helpsCartService.CurrencyFloatPrice(paymentPrice, p.Currency);
                }
                else
                {
                    Rate = 1;
                    OrderSymbol = currencyList.FirstOrDefault(c => c.Currency == p.ManageCurrency)?.Symbol;
                    Currency = p.ManageCurrency;
                    priceData = await OrdersDetailPrice(orderList.Where(x => x.OrderId == p.OrderId).FirstOrDefault(), 1, 1, true, true, replaceData);
                    //实际付款
                    paymentPrice = await actualPayment(orderList.Where(x => x.OrderId == p.OrderId).FirstOrDefault());
                    paymentPrice = _helpsCartService.CurrencyFloatPrice(paymentPrice, p.ManageCurrency);
                }










                // 获取包裹信息
                var orderPackage = GetOrdersPackage(p.OrderId, orderList.Where(x => x.OrderId == p.OrderId).FirstOrDefault());


                var unShippedList = new List<dynamic>();
                var ingShippedList = new List<dynamic>();
                var shippedList = new List<dynamic>();

                foreach (var parent in orderPackage.Parent)
                {
                    int wId = parent.WId;
                    if (orderPackage.Valid.TryGetValue(wId, out var validItems))
                    {
                        foreach (var item in validItems)
                        {
                            switch (item.Status)
                            {
                                case 0:// 未发货
                                    unShippedList.Add(item);
                                    break;
                                case 2:// 已打包 等待发货
                                    ingShippedList.Add(item);
                                    break;
                                case 1:// 已发货 没排除“已发货的产品”
                                    if (Convert.ToInt32(shipped) != 1)
                                    {
                                        shippedList.Add(item);
                                    }
                                    break;
                            }
                        }
                    }
                }

                var packageList = new List<dynamic>();
                packageList.AddRange(unShippedList);
                packageList.AddRange(ingShippedList);
                packageList.AddRange(shippedList);

                //排除产品
                //var refunded = Request.Form["Exclude[refunded]"];//已退款的产品
                //var shipped = Request.Form["Exclude[shipped]"];//已发货的产品
                //var outOfStock = Request.Form["Exclude[outOfStock]"];//库存<=0的产品
                //var preSale = Request.Form["Exclude[preSale]"];//预售产品
                List<ordersListExplode> ordersListExplodes = new List<ordersListExplode>();
                if (packageList != null && packageList.Count > 0)
                {
                    foreach (orders_package item in packageList)
                    {
                        var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                        //var TotalQty = dict.Values.Sum();
                        List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                        if (ordersProductsList != null)
                        {
                            var ConformProductsLists = ordersProductsList.Where(opl => productIds.Contains(opl.LId)).ToList();
                            foreach (var items in ConformProductsLists)
                            {
                                var CostPriceFirst = productsSelectedAttributeCombinationList.Where(x => x.VariantsId == items.VariantsId && x.ProId == items.ProId).FirstOrDefault();

                                if (outOfStock == "1")
                                {
                                    if ((CostPriceFirst?.Stock ?? 0) == 0)
                                    {
                                        continue;
                                    }
                                }

                                var PriceString = await _helpsCartService.IconvPriceFormat(items.Price.Value, 0, Currency, Rate);
                                var CostPriceFirstString = await _helpsCartService.IconvPriceFormat(CostPriceFirst?.CostPrice ?? 0, 0, Currency, Rate);
                                ordersListExplodes.Add(new ordersListExplode()
                                {
                                    ShippingExpress = item?.ShippingExpress ?? "",
                                    TrackingNumber = item?.TrackingNumber ?? "",
                                    Qty = items.Qty.ToString(),
                                    Status = (item?.Status == 0) ? "未处理" : (item?.Status == 1) ? "已取件" : "待取件",
                                    Carrier = item?.Carrier ?? "",

                                    Name = items?.Name,
                                    Price = PriceString,
                                    SKU = items?.SKU,
                                    Property = items?.Attr.Replace("[", "").Replace("]", "").Replace("{", "").Replace("}", ""),
                                    PropertyPrice = CostPriceFirstString

                                });

                            }
                        }

                    }
                }
                if (refunded != "1")
                {
                    var deleteProductsRow = deleteProductsList.Where(x => x.OrderId == p.OrderId).ToList();
                    if (deleteProductsRow != null && deleteProductsRow.Count > 0)
                    {
                        foreach (var item in deleteProductsRow)
                        {
                            if (shipped == "1")
                            {
                                if (item.ShippingStatus == "Shipped")
                                {
                                    continue;
                                }
                            }

                            var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                            //var TotalQty = dict.Values.Sum();
                            List<int> productIds = dict.Keys.Select(int.Parse).ToList();

                            var ordersPackageData = ordersPackageList.Where(x => x.WId == item.PackageId).FirstOrDefault();
                            if (ordersProductsList != null)
                            {
                                var ConformProductsLists = ordersProductsList.Where(opl => productIds.Contains(opl.LId)).ToList();
                                foreach (var items in ConformProductsLists)
                                {
                                    var CostPriceFirst = productsSelectedAttributeCombinationList.Where(x => x.VariantsId == items.VariantsId && x.ProId == items.ProId).FirstOrDefault();
                                    if (outOfStock == "1")
                                    {
                                        if ((CostPriceFirst?.Stock ?? 0) == 0)
                                        {
                                            continue;
                                        }
                                    }
                                    var PriceString = await _helpsCartService.IconvPriceFormat(items.Price.Value, 0, Currency, Rate);
                                    var CostPriceFirstString = await _helpsCartService.IconvPriceFormat(CostPriceFirst?.CostPrice ?? 0, 0, Currency, Rate);
                                    ordersListExplodes.Add(new ordersListExplode()
                                    {
                                        ShippingExpress = ordersPackageData?.ShippingExpress ?? "",
                                        TrackingNumber = ordersPackageData?.TrackingNumber ?? "",
                                        Qty = items.Qty.ToString(),
                                        Status = (ordersPackageData?.Status == 0) ? "未处理" : (ordersPackageData?.Status == 1) ? "已取件" : "待取件",
                                        Carrier = ordersPackageData?.Carrier ?? "",

                                        Name = items?.Name,
                                        Price = PriceString,
                                        SKU = items?.SKU,
                                        Property = items?.Attr.Replace("[", "").Replace("]", "").Replace("{", "").Replace("}", ""),
                                        PropertyPrice = CostPriceFirstString

                                    });

                                }
                            }



                        }
                    }
                }

                p.ordersListExplodes = ordersListExplodes;





                #region 第二次测试代码
                //if (ordersProductsList != null && ordersProductsList.Count > 0)
                //{
                //    List<ordersListExplode> ordersListExplodes = new List<ordersListExplode>();
                //    foreach (var item in ordersProductsList)
                //    {
                //        var ordersPackageFirst = ordersPackageList.Where(it => it.ProInfo.Contains(item.LId.ToString())).FirstOrDefault();
                //        var CostPriceFirst = productsSelectedAttributeCombinationList.Where(x => x.VariantsId == item.VariantsId && x.ProId == item.ProId).FirstOrDefault();


                //        var PriceString = await _helpsCartService.IconvPriceFormat(item.Price.Value, 0, Currency, Rate);
                //        var CostPriceFirstString = await _helpsCartService.IconvPriceFormat(CostPriceFirst?.CostPrice ?? 0, 0, Currency, Rate);
                //        if (ordersPackageFirst != null)
                //        {
                //            ordersListExplodes.Add(new ordersListExplode()
                //            {
                //                ShippingExpress = ordersPackageFirst?.ShippingExpress ?? "",
                //                TrackingNumber = ordersPackageFirst?.TrackingNumber ?? "",
                //                Qty = item.Qty.ToString(),
                //                Status = (ordersPackageFirst?.Status==1) ? "已取件" : "未处理",
                //                Carrier = ordersPackageFirst?.Carrier ?? "",

                //                Name = item?.Name,
                //                Price = PriceString,
                //                SKU = item?.SKU,
                //                Property = item?.Attr,
                //                PropertyPrice = CostPriceFirstString

                //            });
                //        }
                //        else
                //        {
                //            ordersListExplodes.Add(new ordersListExplode()
                //            {
                //                ShippingExpress = "",
                //                TrackingNumber = "",
                //                Qty = item.Qty.ToString(),
                //                Status = "",
                //                Carrier = "",

                //                Name = item?.Name,
                //                Price = PriceString,
                //                SKU = item?.SKU,
                //                Property = item?.Attr,
                //                PropertyPrice = CostPriceFirstString

                //            });
                //        }

                //    }
                //    p.ordersListExplodes = ordersListExplodes;
                //}
                #endregion

                #region 第一次测试代码
                //if (ordersPackageList != null)
                //{
                //    List<ordersListExplode> ordersListExplodes = new List<ordersListExplode>();
                //    var ordersPackageLists = ordersPackageList.Where(x => x.OrderId == p.OrderId && x.ParentId != 0 && x.ProInfo != "[]").ToList();
                //    if (ordersPackageLists != null && ordersPackageLists.Count > 0)
                //    {
                //        foreach (var item in ordersPackageLists)
                //        {
                //            JObject obj = JObject.Parse(item.ProInfo);
                //            var ordersProductsFir = ordersProductsList.Where(x => x.LId == int.Parse(obj.Properties().First().Name)).FirstOrDefault();
                //            ordersListExplodes.Add(new ordersListExplode()
                //            {
                //                ShippingExpress = item.ShippingExpress,
                //                TrackingNumber = item.TrackingNumber,
                //                Qty = obj.Properties().First().Value.ToString(),
                //                Status = item.Status.ToString(),
                //                Carrier = item.Carrier,
                //                Name = ordersProductsFir?.Name,
                //                Price = ordersProductsFir?.Price.ToString(),
                //                SKU = ordersProductsFir?.SKU,
                //                Property = ordersProductsFir?.Property,
                //                PropertyPrice = ordersProductsFir?.PropertyPrice.ToString(),

                //            });
                //        }
                //    }
                //    else
                //    {
                //        var ProInfoFirst = ordersPackageList.Where(x => x.OrderId == p.OrderId && x.ParentId == 0).FirstOrDefault();
                //        if (ProInfoFirst != null)
                //        {
                //            JObject obj = JObject.Parse(ProInfoFirst.ProInfo);
                //            foreach (JProperty prop in obj.Properties())
                //            {
                //                int key = int.Parse(prop.Name);
                //                var ordersProductsFir = ordersProductsList.Where(x => x.LId == key).FirstOrDefault();
                //                int count = (int)prop.Value;
                //                ordersListExplodes.Add(new ordersListExplode()
                //                {
                //                    ShippingExpress = ProInfoFirst.ShippingExpress,
                //                    TrackingNumber = ProInfoFirst.TrackingNumber,
                //                    Qty = count.ToString(),
                //                    Status = ProInfoFirst.Status.ToString(),
                //                    Carrier = ProInfoFirst.Carrier,
                //                    Name = ordersProductsFir.Name,
                //                    Price = ordersProductsFir.Price.ToString(),
                //                    SKU = ordersProductsFir.SKU,
                //                    Property = ordersProductsFir.Property,
                //                    PropertyPrice = ordersProductsFir.PropertyPrice.ToString(),

                //                });
                //            }
                //        }

                //    }
                //    p.ordersListExplodes = ordersListExplodes;

                //}
                #endregion

                if (ordersPaymentlist != null)
                {
                    p.BankTransactionNumber = ordersPaymentlist.Where(x => x.OrderId == p.OrderId).FirstOrDefault()?.BankTransactionNumber ?? "";
                }
                /**
                      * 订单各种价格格式显示
                      * @return array				订单各种价格
                      * 								● ProductPrice 产品价格
                      * 								● DiscountPrice 订单折扣金额
                      * 								● CouponPrice 优惠券折扣金额
                      * 								● PointsPrice 积分
                      * 								● ShippingPrice 运费
                      * 								● FeePrice 手续费
                      * 								● TaxPrice 税费
                      * 								● TotalPrice 订单总价格
                      */
                p.ProductPrice = OrderSymbol + priceData.Item1;
                p.DiscountPrice = OrderSymbol + priceData.Item2;
                p.CouponPrice = OrderSymbol + priceData.Item3;
                p.PointsPrice = OrderSymbol + priceData.Item4;
                p.Points = OrderSymbol + priceData.Item4;
                p.ShippingPrice = OrderSymbol + priceData.Item5;
                p.Commission = OrderSymbol + priceData.Item6;
                p.TaxCost = OrderSymbol + priceData.Item7;
                if (CurrencyUnit == "checkout")//结账货币 
                {

                    p.OrderSum = OrderSymbol + _helpsCartService.CurrencyFloatPrice(priceData.Item8, Currency);
                }
                else
                {
                    p.OrderSum = OrderSymbol + _helpsCartService.CurrencyFloatPrice(paymentPrice, Currency);
                }


                p.ordersRemarkLog = string.Join(",",
                    ordersRemarkLogList
                        .Where(x => x.OrderId == p.OrderId)
                        .Select(x => x.Log)
                        .Where(log => !string.IsNullOrEmpty(log))
                );
                p.ordersReason = ordersRefundInfoList.Where(x => x.OrderId == p.OrderId).FirstOrDefault()?.Reason;
                p.userRemarkLog = string.Join(",",
                    userRemarkLogList
                        .Where(x => x.UserId == p.UserId)
                        .Select(x => x.Log)
                        .Where(log => !string.IsNullOrEmpty(log))
                );



            });

            return orderExplodeResponseList;
        }


        public DataTable ExportToCsvTwo(string jsonString, List<OrderExplodeResponse> orders, string filePath)
        {
            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonString);
            var activeFields = jsonDict
                .Where(kvp => kvp.Value == "1")
                .Select(kvp => kvp.Key)
                //.OrderBy(key => key)
                .ToList();

            // 创建DataTable（需要预先知道所有可能的列）
            var dt = new DataTable();
            // 先添加所有可能的列（根据AllMenuItemsText生成）
            foreach (var fieldKey in OrderExportMenuHelper.AllMenuItemsText().Keys)
            {
                if (activeFields.Contains(fieldKey))
                {
                    dt.Columns.Add(OrderExportMenuHelper.AllMenuItemsText()[fieldKey], typeof(string));
                }
            }

            // 核心修改：双循环结构（订单+产品）
            foreach (var order in orders)
            {
                // 处理订单级字段（需要每个产品行重复）
                var orderProps = new Dictionary<string, object>();
                foreach (var fieldKey in activeFields)
                {
                    if (OrderExportMenuHelper.FieldRules.TryGetValue(fieldKey, out var properties))
                    {
                        // 优先处理订单级属性（不含ordersListExplodes前缀的）
                        var orderOnlyProps = properties.Where(p => !p.StartsWith("ordersListExplodes."));
                        foreach (var prop in orderOnlyProps)
                        {
                            orderProps[fieldKey] = GetPropertyValue(order, prop);
                        }
                    }
                }

                // 处理产品级字段（每个产品生成单独行）
                bool isFirstProduct = true; // 新增：订单首行标记
                foreach (var product in order.ordersListExplodes)
                {
                    var row = dt.NewRow();

                    // 填充订单级字段（仅在第一行填充）
                    if (isFirstProduct)
                    {
                        foreach (var kvp in orderProps)
                        {
                            var fieldKey = kvp.Key;
                            if (dt.Columns.Contains(OrderExportMenuHelper.AllMenuItemsText()[fieldKey]))
                            {
                                row[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]] = kvp.Value;
                            }
                        }
                        isFirstProduct = false; // 关闭首行标记
                    }

                    // 填充产品级字段
                    foreach (var fieldKey in activeFields)
                    {
                        if (OrderExportMenuHelper.FieldRules.TryGetValue(fieldKey, out var properties))
                        {
                            // 处理产品级属性（含ordersListExplodes前缀的）
                            var productProps = properties.Where(p => p.StartsWith("ordersListExplodes."));
                            foreach (var prop in productProps)
                            {
                                var actualProp = prop.Replace("ordersListExplodes.", "");
                                var value = GetPropertyValue(product, actualProp);

                                // 处理组合字段（如金额需要带货币符号）
                                //if (dt.Columns[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]].ColumnName.Contains("金额") &&
                                //    order.OrderSymbol != null)
                                //{
                                //    value = $"{order.OrderSymbol}{value}";
                                //}

                                row[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]] = value;
                            }
                        }
                    }

                    dt.Rows.Add(row);
                }
            }

            return dt;
        }
        // 修改后的原方法（调用新方法）
        public void UpLoadFileForLocalStorage(DataTable dt, string filePath)
        {
            var config = new CsvConfiguration
            {
                Seperator = ',',
                NewLine = Environment.NewLine,
                Culture = CultureInfo.InvariantCulture,
                StreamWriterFunc = stream => new StreamWriter(stream, Encoding.UTF8)
            };

            // 保存为CSV
            using (var stream = System.IO.File.Create(filePath))
            using (var writer = new CsvWriter(stream, dt, config, printHeader: true))
            {
                writer.SaveAs();
            }

            //// 原有保存逻辑
            //var config = new CsvConfiguration { /* 原有配置 */ };
            //using (var stream = File.Create(filePath))
            //using (var writer = new CsvWriter(stream, dt, config, true))
            //{
            //    writer.SaveAs();
            //}
        }


        public void ExportToCsv(string jsonString, List<OrderExplodeResponse> orders, string filePath)
        {
            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonString);
            var activeFields = jsonDict
                .Where(kvp => kvp.Value == "1")
                .Select(kvp => kvp.Key)
                //.OrderBy(key => key)
                .ToList();

            // 创建DataTable（需要预先知道所有可能的列）
            var dt = new DataTable();
            // 先添加所有可能的列（根据AllMenuItemsText生成）
            foreach (var fieldKey in OrderExportMenuHelper.AllMenuItemsText().Keys)
            {
                if (activeFields.Contains(fieldKey))
                {
                    dt.Columns.Add(OrderExportMenuHelper.AllMenuItemsText()[fieldKey], typeof(string));
                }
            }

            // 核心修改：双循环结构（订单+产品）
            foreach (var order in orders)
            {
                // 处理订单级字段（需要每个产品行重复）
                var orderProps = new Dictionary<string, object>();
                foreach (var fieldKey in activeFields)
                {
                    if (OrderExportMenuHelper.FieldRules.TryGetValue(fieldKey, out var properties))
                    {
                        // 优先处理订单级属性（不含ordersListExplodes前缀的）
                        var orderOnlyProps = properties.Where(p => !p.StartsWith("ordersListExplodes."));
                        foreach (var prop in orderOnlyProps)
                        {
                            orderProps[fieldKey] = GetPropertyValue(order, prop);
                        }
                    }
                }

                // 处理产品级字段（每个产品生成单独行）
                bool isFirstProduct = true; // 新增：订单首行标记
                foreach (var product in order.ordersListExplodes)
                {
                    var row = dt.NewRow();

                    // 填充订单级字段（仅在第一行填充）
                    if (isFirstProduct)
                    {
                        foreach (var kvp in orderProps)
                        {
                            var fieldKey = kvp.Key;
                            if (dt.Columns.Contains(OrderExportMenuHelper.AllMenuItemsText()[fieldKey]))
                            {
                                row[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]] = kvp.Value;
                            }
                        }
                        isFirstProduct = false; // 关闭首行标记
                    }

                    // 填充产品级字段
                    foreach (var fieldKey in activeFields)
                    {
                        if (OrderExportMenuHelper.FieldRules.TryGetValue(fieldKey, out var properties))
                        {
                            // 处理产品级属性（含ordersListExplodes前缀的）
                            var productProps = properties.Where(p => p.StartsWith("ordersListExplodes."));
                            foreach (var prop in productProps)
                            {
                                var actualProp = prop.Replace("ordersListExplodes.", "");
                                var value = GetPropertyValue(product, actualProp);

                                // 处理组合字段（如金额需要带货币符号）
                                //if (dt.Columns[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]].ColumnName.Contains("金额") &&
                                //    order.OrderSymbol != null)
                                //{
                                //    value = $"{order.OrderSymbol}{value}";
                                //}

                                row[OrderExportMenuHelper.AllMenuItemsText()[fieldKey]] = value;
                            }
                        }
                    }

                    dt.Rows.Add(row);
                }
            }

            // 配置CSV写入器
            var config = new CsvConfiguration
            {
                Seperator = ',',
                NewLine = Environment.NewLine,
                Culture = CultureInfo.InvariantCulture,
                StreamWriterFunc = stream => new StreamWriter(stream, Encoding.UTF8)
            };

            // 保存CSV文件
            using (var stream = System.IO.File.Create(filePath))
            using (var writer = new CsvWriter(stream, dt, config, printHeader: true))
            {
                writer.SaveAs();
            }
        }

        // 修改属性获取方法以支持嵌套属性
        private object GetPropertyValue(object obj, string propertyName)
        {
            var parts = propertyName.Split('.');
            object current = obj;
            foreach (var part in parts)
            {
                if (current == null) return null;
                var prop = current.GetType().GetProperty(part);
                if (prop == null) return null;
                current = prop.GetValue(current, null);
            }
            return current ?? string.Empty;
        }







        /// <summary>
        /// 保存订单导出csv数据
        /// </summary>
        /// <param name="jsonString"></param>
        /// <param name="orders"></param>
        /// <param name="filePath"></param>
        //public void ExportToCsv(string jsonString, List<OrderExplodeResponse> orders, string filePath)
        //{
        //    var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonString);
        //    var activeFields = jsonDict
        //        .Where(kvp => kvp.Value == "1")
        //        .Select(kvp => kvp.Key)
        //        .ToList();
        //    // 2. 构建DataTable结构
        //    var dt = new DataTable();
        //    //dt.Columns.Add("ID", typeof(int)); // 添加固定ID列
        //    // 动态添加活动字段列
        //    foreach (var fieldKey in activeFields)
        //    {
        //        if (OrderExportMenuHelper.AllMenuItemsText().TryGetValue(fieldKey, out var chineseName))
        //        {
        //            dt.Columns.Add(chineseName, typeof(string));
        //        }
        //    }
        //    // 3. 填充数据
        //    foreach (var orderData in orders)
        //    {
        //        var row = dt.NewRow();
        //        //row["ID"] = user.UserId;

        //        foreach (var fieldKey in activeFields)
        //        {
        //            if (OrderExportMenuHelper.AllMenuItemsText().TryGetValue(fieldKey, out var chineseName) &&
        //                OrderExportMenuHelper.FieldRules.TryGetValue(fieldKey, out var properties))
        //            {
        //                var values = properties.Select(p => GetPropertyValue(orderData, p)).ToList();
        //                row[chineseName] = CombineValues(values);
        //            }
        //        }

        //        dt.Rows.Add(row);
        //    }
        //    var config = new CsvConfiguration
        //    {
        //        Seperator = ',',
        //        NewLine = Environment.NewLine,
        //        Culture = CultureInfo.InvariantCulture,
        //        StreamWriterFunc = stream => new StreamWriter(stream, Encoding.UTF8)
        //    };

        //    // 保存为CSV
        //    using (var stream = System.IO.File.Create(filePath))
        //    using (var writer = new CsvWriter(stream, dt, config, printHeader: true))
        //    {
        //        writer.SaveAs();
        //    }

        //}

        //private object GetPropertyValue(object obj, string propertyName)
        //{
        //    return obj.GetType().GetProperty(propertyName)?.GetValue(obj, null) ?? string.Empty;
        //}

        //private string CombineValues(List<object> values)
        //{
        //    return string.Join(" ", values.Select(v => v?.ToString() ?? ""));
        //}





        /// <summary>
        /// 保存用户csv数据
        /// </summary>
        /// <param name="Name"></param>
        /// <param name="Path"></param>
        /// <returns></returns>
        public async Task<int> SaveTemporaryStorageFile(string Name, string Path)
        {
            temporary_storage_file temporary_Storage_File = new temporary_storage_file();
            temporary_Storage_File.Name = Name;
            temporary_Storage_File.Path = Path;
            temporary_Storage_File.Type = "order";
            temporary_Storage_File.AddTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            var id = db.Insertable(temporary_Storage_File).ExecuteReturnIdentity();
            return id;
        }

        /// <summary>
        /// 根据name获取用户导出文件
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<temporary_storage_file> GetOrderExportDown(string name)
        {
            var order = await db.Queryable<temporary_storage_file>().Where(p => p.Name == name).FirstAsync();
            return order;

        }
        /// <summary>
        /// 标记发货/修改
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <param name="TrackingNumber"></param>
        /// <param name="ShippingTime"></param>
        /// <param name="Remarks"></param>
        /// <param name="CarrierRes"></param>
        /// <param name="qtyList"></param>
        /// <returns></returns>
        public async Task<bool> OrdersModTrack(int WId, int OrderId, string TrackingNumber, string ShippingTime, string Remarks, string CarrierRes, SortedDictionary<string, string> qtyDict)
        {
            var ProInfoFirst = db.Queryable<orders_package>()
              .Where(x => x.WId == WId && x.OrderId == OrderId).First();
            // 过滤出值不为 "0" 的键值对
            var filteredDict = qtyDict
            .Where(kvp => kvp.Value != "0")
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            // 从字符串解析 JSON 到字典
            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, int>>(ProInfoFirst.ProInfo);

            // 将 SortedDictionary 转换为 Dictionary
            var dictFromSorted = filteredDict.ToDictionary(kvp => kvp.Key, kvp => int.Parse(kvp.Value));

            // 进行比较
            foreach (var kvp in dictFromSorted)
            {
                if (jsonDict.ContainsKey(kvp.Key))
                {
                    jsonDict[kvp.Key] -= kvp.Value; // 减去已存在的值
                    if (jsonDict[kvp.Key] <= 0)   // 如果结果为零或负数，移除该键
                    {
                        jsonDict.Remove(kvp.Key);
                    }
                }
            }
            var ShippingStatusRemove = false;
            if (jsonDict != null && jsonDict.Count > 0)
            {
                //ProInfoFirst.ProInfo = JsonConvert.SerializeObject(jsonDict);
                //var b = db.Updateable(ProInfoFirst).ExecuteCommandHasChange();
                ProInfoFirst.ProInfo = JsonConvert.SerializeObject(jsonDict);
                var id = db.Insertable(ProInfoFirst).ExecuteReturnIdentity();
                ShippingStatusRemove = true;
            }

            var orderss = await db.Queryable<orders>()
                .Where(x => x.OrderId == OrderId).FirstAsync();
            orderss.OrderStatus = 5;
            if (ShippingStatusRemove)
            {
                orderss.ShippingStatus = ShippingStatusEnum.部分发货.GetDescription();
            }
            else
            {
                orderss.ShippingStatus = ShippingStatusEnum.已发货.GetDescription();
            }


            await db.Updateable(orderss).ExecuteCommandHasChangeAsync();
            ProInfoFirst.ProInfo = JsonConvert.SerializeObject(dictFromSorted);
            ProInfoFirst.TrackingNumber = TrackingNumber;
            ProInfoFirst.Carrier = CarrierRes;
            ProInfoFirst.ShippingTime = DateTimeHelper.ConvertToUnixTimestamp(Convert.ToDateTime(ShippingTime));
            ProInfoFirst.Remarks = Remarks;
            ProInfoFirst.Status = 1;

            var b = await db.Updateable(ProInfoFirst).ExecuteCommandHasChangeAsync();



            return b;
        }

        /// <summary>
        /// 取消发货
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="WId"></param>
        /// <returns></returns>
        public async Task<bool> OrdersCancelDelivery(int OrderId, int WId)
        {
            var ProInfoFirst = db.Queryable<orders_package>()
              .Where(x => x.WId == WId && x.OrderId == OrderId).First();
            ProInfoFirst.Status = 0;
            var b = await db.Updateable(ProInfoFirst).ExecuteCommandHasChangeAsync();
            return b;
        }
        /// <summary>
        /// 发送订单备注
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="Log"></param>
        /// <returns></returns>
        public async Task<orders_remark_log> OrdersRemarkLog(int OrderId, string Log)
        {

            orders_remark_log orders_Remark_Log = new orders_remark_log();
            orders_Remark_Log.OrderId = OrderId;
            orders_Remark_Log.Log = Log;
            orders_Remark_Log.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            orders_Remark_Log.UserId = 0;
            orders_Remark_Log.UserName = "";

            var id = db.Insertable(orders_Remark_Log).ExecuteReturnIdentity();
            return orders_Remark_Log;
        }
        /// <summary>
        /// 通过OrderId更新订单表中的标签id
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="tagsNameList"></param>
        /// <returns></returns>
        public async Task<bool> UpdateOrders_tags(int OrderId, List<string> tagsNameList)
        {

            List<orders_tags> user_Label_Collection = new List<orders_tags>();

            int stopIndex = tagsNameList.FindIndex(tagName => tagName == "截至-·11");
            if (stopIndex != -1)
            {
                tagsNameList = tagsNameList.Take(stopIndex).ToList();
            }
            if (tagsNameList != null && tagsNameList.Count > 0)
            {
                var lists = db.Queryable<orders_tags>().ToList();
                foreach (var item in lists)
                {
                    string processedTags = DeleteProcessTags(item.Tags, OrderId, item.Name, tagsNameList);
                    if (string.IsNullOrEmpty(processedTags))
                    {
                        // 删除该行  
                        db.Deleteable<orders_tags>(item).ExecuteCommand();
                    }
                    string processedTagslist = ProcessTags(item.Tags, OrderId, item.Name, tagsNameList);
                    if (!string.IsNullOrEmpty(processedTagslist))
                    {
                        // 更新Tags字段  
                        item.Tags = processedTagslist;
                        db.Updateable(item).ExecuteCommand();
                    }
                }

                foreach (var item in tagsNameList)
                {
                    var orders_tagsCount = db.Queryable<orders_tags>()
                        .Where(x => SqlFunc.Contains(x.Tags, "|" + OrderId + "|") && x.Name == item)
                        .ToList().Count;
                    //根据订单id和标签（没有数据）
                    if (orders_tagsCount == 0)
                    {
                        //根据标签获取数据
                        var orders_tags = db.Queryable<orders_tags>()
                       .Where(x => x.Name == item)
                       .First();
                        //里面没有数据
                        if (orders_tags == null)
                        {
                            //新增
                            InsertOrdersTags(OrderId, item);
                        }
                    }

                }

            }
            return true;

        }

        private void InsertOrdersTags(int orderId, string tag)
        {
            var tags = new orders_tags()
            {
                Tags = $"|{orderId}|",
                Name = tag,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
            };
            db.Insertable(tags).ExecuteCommand();
        }



        private static string ProcessTags(string tags, int OrderId, string Name, List<string> tagsNameList)
        {
            if (string.IsNullOrEmpty(tags))
            {
                return null;
            }
            string pattern = $@"\|{OrderId}\|";
            // 替换所有|47|为空  
            string result = Regex.Replace(tags, pattern, "");
            // 去掉开头和结尾的|  
            result = result.Trim('|');
            // 重新用|包裹  
            if (string.IsNullOrEmpty(result))
            {
                return null;
            }
            var tagsCount = tagsNameList.Where(x => x.Contains(Name) && tags.Contains(OrderId.ToString())).ToList().Count;
            if (tagsCount > 0)
            {
                return null;
            }
            var tagsCounts = tagsNameList.Where(x => x.Contains(Name)).ToList().Count;
            if (tagsCounts > 0)
            {
                return "|" + result + "|" + OrderId + "|";
            }
            return "|" + result + "|";
        }

        private static string DeleteProcessTags(string tags, int OrderId, string Name, List<string> tagsNameList)
        {
            if (string.IsNullOrEmpty(tags))
            {
                return null;
            }

            string pattern = $@"\|{OrderId}\|";
            // 替换所有|47|为空  
            string result = Regex.Replace(tags, pattern, "");
            // 去掉开头和结尾的|  
            result = result.Trim('|');
            // 重新用|包裹  
            if (string.IsNullOrEmpty(result))
            {
                var tagsCount = tagsNameList.Where(x => x.Contains(Name)).ToList().Count;
                if (tagsCount > 0)
                {
                    return tagsCount.ToString();
                }
                else
                {
                    return null;
                }
            }


            return "|" + result + "|";
        }
        /// <summary>
        /// 修改收货地址
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        public async Task<OrdersGetAddressResponse> OrdersGetAddress(int OrderId, string Type)
        {
            var data = db.Queryable<orders>()
              .Where(x => x.OrderId == OrderId)
              .First();
            return new OrdersGetAddressResponse
            {
                // 基础属性映射
                FirstName = data.ShippingFirstName,
                LastName = data.ShippingLastName,
                AddressLine1 = data.ShippingAddressLine1,
                AddressLine2 = data.ShippingAddressLine2,
                City = data.ShippingCity,
                State = data.ShippingState,
                ZipCode = data.ShippingZipCode,
                Country = data.ShippingCountry,
                CountryCode = data.ShippingCountryCode,
                PhoneNumber = data.ShippingPhoneNumber,
                TaxCode = data.ShippingTaxCode,

                // 标识信息转换
                CId = Convert.ToInt32(data.ShippingCId),
                SId = Convert.ToInt32(data.ShippingSId),
                CodeOptionId = Convert.ToInt32(data.ShippingCodeOptionId),
                CodeOption = data.ShippingCodeOption,

                // 集合类型保持初始化
                AdditionalInfoData = new List<string>(),
                AdditionalInfoName = new List<string>()
            };

        }
        /// <summary>
        /// 获取国家string
        /// </summary>
        /// <returns></returns>
        public async Task<List<OrderCountryResponse>> GetCountryData()
        {
            var countrys = await db.Queryable<country>()
                .Where(x => x.IsUsed == true)
                .OrderBy(x => x.Country)

                .ToListAsync();
            var result = new List<OrderCountryResponse>();
            foreach (var item in countrys)
            {
                result.Add(new OrderCountryResponse
                {
                    Name = item.Country,
                    Value = item.CId,
                    Type = "country"
                });
            }
            return result;

        }
        /// <summary>
        /// 获取国家下的省份
        /// </summary>
        /// <returns></returns>
        public async Task<List<country_states>> GetCountryStatesData()
        {
            var res = await db.Queryable<country_states>()
                .ToListAsync();
            return res;
        }
        /// <summary>
        /// do:获取国家下的省份
        /// </summary>
        /// <param name="SId"></param>
        /// <returns></returns>
        public async Task<country_states> GetCountryStatesByid(int SId)
        {
            var res = await db.Queryable<country_states>()
                .Where(x => x.SId == SId)
                .FirstAsync();
            return res;
        }
        /// <summary>
        /// 通过id获取国家
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        public async Task<country> GetcountryBy(int CId)
        {
            var res = await db.Queryable<country>()
                .Where(x => x.CId == CId)
                .FirstAsync();
            return res;
        }
        /// <summary>
        /// 获取国家下的省份
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CountryInfoResponse> GetCountryStatesDataTwo()
        {
            // 查询省份数据并按CId分组
            var provinces = db.Queryable<country_states>()
                .Where(p => p.CId.HasValue)
                .ToList()
                .GroupBy(p => p.CId.Value)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select((p, i) => new ProvinceInfo
                    {
                        code = p.AcronymCode,
                        name = p.States,
                        tax = p.Tax,
                        SId = p.SId
                    }).ToList()
                );

            // 构建国家数据结构（这里需要关联国家基础信息，示例使用静态数据）
            var countries = new Dictionary<string, CountryInfoResponse>();

            // 从country表获取国家基础信息（示例查询）
            var baseData = db.Queryable<country>()
                .Select(c => new
                {
                    CId = c.CId,
                    CountryName = c.Country,
                    Acronym = c.Acronym,
                    Tax = c.Tax,
                    TaxThreshold = c.TaxThreshold,
                    PhoneCode = c.Code,
                    AddressFormat = c.AddressFormat
                }).ToList();

            // 构建国家字典
            foreach (var item in baseData)
            {
                countries[item.CId.ToString()] = new CountryInfoResponse
                {
                    name = item.CountryName,
                    code = item.Acronym,
                    tax = item.Tax,
                    threshold = HelpsCart.ConvertPrice(item.TaxThreshold, 2),
                    phone_code = Convert.ToInt32(item.PhoneCode),
                    address_format = item.AddressFormat
                };
            }

            // 合并省份数据到对应国家
            foreach (var countryId in provinces.Keys)
            {
                if (countries.TryGetValue(countryId.ToString(), out var country))
                {
                    var provinceList = provinces[countryId];
                    // 添加带前缀的索引（如"_1"）
                    country.provinces = provinceList
                        .Select((p, i) => new KeyValuePair<string, ProvinceInfo>(
                            $"_{i + 1}",
                            new ProvinceInfo
                            {
                                code = p.code,
                                name = p.name,
                                tax = p.tax,
                                SId = p.SId
                            }))
                        .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                }
            }

            return countries;
        }

        // 3. 辅助方法（处理数值转换）
        public static class HelpsCart
        {
            public static decimal ConvertPrice(object value, int decimals)
            {
                if (value == null || !decimal.TryParse(value.ToString(), out var result))
                    return 0;

                return Math.Round(result, decimals);
            }
        }
        /// <summary>
        /// 在订单详情中更改订单状态改为付款
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="OrderStatus"></param>
        /// <returns></returns>
        public async Task<bool> GetOrdersModStatus(int OrderId, int OrderStatus)
        {
            if (OrderStatus == 4)
            {
                var ordersrow = db.Queryable<orders>()
                    .Where(x => x.OrderId == OrderId).First();

                var paymentFirst = db.Queryable<payment>()
                    .Where(x => x.Name_en == ordersrow.PaymentMethod).First();
                ordersrow.PaymentStatus = PaymentStatusEnum.已付款.GetDescription();
                ordersrow.OrderStatus = OrderStatus;
                ordersrow.PId = paymentFirst.PId;
                //ordersrow.PaymentId = 0;
                ordersrow.PayTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                ordersrow.TradeStatus = TradeStatusEnum.已完成.GetDescription();
                var b = db.Updateable(ordersrow).ExecuteCommandHasChange();

                return b;
            }
            return false;
        }

        /// <summary>
        /// 修改收货地址 
        /// </summary>
        /// <param name="FirstName"></param>
        /// <param name="LastName"></param>
        /// <param name="AddressLine1"></param>
        /// <param name="AddressLine2"></param>
        /// <param name="City"></param>
        /// <param name="country_id_input"></param>
        /// <param name="country_id"></param>
        /// <param name="country_idType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="Province"></param>
        /// <param name="ZipCode"></param>
        /// <param name="CountryCode"></param>
        /// <param name="PhoneNumber"></param>
        /// <param name="tax_code_type"></param>
        /// <param name="tax_code_value"></param>
        /// <param name="OrderId"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        public async Task<OrdersModAddressResponse> OrdersModAddress(string FirstName, string LastName, string AddressLine1, string AddressLine2,
            string City, string country_id_input, int country_id, string country_idType,
            string _DoubleOption, int Province, string ZipCode, string CountryCode, string PhoneNumber, int tax_code_type,
            string tax_code_value, int OrderId, string Type)
        {
            var ordersModAddress = new OrdersModAddressResponse();
            var ordersFirst = db.Queryable<orders>()
                .Where(x => x.OrderId == OrderId)
                .First();
            var country_StatesFirst = new country_states();
            if (Province != 0)
            {
                country_StatesFirst = db.Queryable<country_states>()
                               .Where(x => x.SId == Province)
                               .First();
            }


            ordersFirst.ShippingFirstName = FirstName;
            ordersFirst.ShippingLastName = LastName;
            ordersFirst.ShippingAddressLine1 = AddressLine1;
            ordersFirst.ShippingAddressLine2 = AddressLine2;
            ordersFirst.ShippingCity = City;
            if (Province != 0)
            {
                ordersFirst.ShippingState = country_StatesFirst.States;
            }
            else
            {
                ordersFirst.ShippingState = "";
            }
            ordersFirst.ShippingCountry = country_id_input;
            ordersFirst.ShippingCId = country_id;
            ordersFirst.ShippingSId = Province;
            ordersFirst.ShippingZipCode = ZipCode;
            ordersFirst.ShippingCountryCode = CountryCode;

            ordersFirst.ShippingPhoneNumber = PhoneNumber;
            ordersFirst.ShippingCodeOptionId = tax_code_type;
            ordersFirst.ShippingTaxCode = tax_code_value;

            var b = await db.Updateable(ordersFirst).ExecuteCommandHasChangeAsync();
            if (b)
            {
                ordersFirst = db.Queryable<orders>()
               .Where(x => x.OrderId == OrderId)
               .First();
            }
            var infoOrdersAddres = new InfoOrdersAddresModel();
            infoOrdersAddres.name = FirstName + " " + LastName;
            infoOrdersAddres.phone = CountryCode + " " + PhoneNumber;
            infoOrdersAddres.country = country_id_input;
            if (Province != 0)
            {
                infoOrdersAddres.state = country_StatesFirst.States;
            }
            else
            {
                infoOrdersAddres.state = "";
            }

            infoOrdersAddres.city = City;
            infoOrdersAddres.address1 = AddressLine1;
            infoOrdersAddres.address2 = AddressLine2;
            infoOrdersAddres.personal_vatid = "#" + ordersFirst.ShippingCodeOption + tax_code_value;
            infoOrdersAddres.zipcode = ZipCode;
            infoOrdersAddres.additional = ordersFirst.AdditionalInfoName;


            ordersModAddress.info = infoOrdersAddres;

            var ordersAddresText = new OrdersAddresTextModel();
            ordersAddresText.OrderId = OrderId;
            ordersAddresText.FirstName = FirstName;
            ordersAddresText.LastName = LastName;
            ordersAddresText.AddressLine1 = AddressLine1;
            ordersAddresText.AddressLine2 = AddressLine2;
            ordersAddresText.City = City;
            if (Province != 0)
            {
                ordersAddresText.State = country_StatesFirst.States;
            }
            else
            {
                ordersAddresText.State = "";
            }

            ordersAddresText.SId = Province;
            ordersAddresText.Country = country_id_input;
            ordersAddresText.CId = country_id;
            ordersAddresText.ZipCode = ZipCode;
            ordersAddresText.CodeOption = ordersFirst.ShippingCodeOption;
            ordersAddresText.CodeOptionId = tax_code_type;
            ordersAddresText.TaxCode = tax_code_value;
            ordersAddresText.CountryCode = CountryCode;
            ordersAddresText.PhoneNumber = PhoneNumber;

            ordersModAddress.text = System.Text.Json.JsonSerializer.Serialize(ordersAddresText);

            return ordersModAddress;
        }
        /// <summary>
        /// 获取比37小的最近一个值（35）
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<int> getprevOrder(int OrderId)
        {
            var prevOrder = await db.Queryable<orders>()
                .Where(o => o.OrderId < OrderId)
                .OrderBy(o => o.OrderId, OrderByType.Desc)
                .FirstAsync();
            return prevOrder?.OrderId ?? 0;
        }
        /// <summary>
        /// 获取比37大的最近一个值（38）
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<int> getnextOrder(int OrderId)
        {
            var nextOrder = await db.Queryable<orders>()
                .Where(o => o.OrderId > OrderId)
                .OrderBy(o => o.OrderId, OrderByType.Asc)
                .FirstAsync();
            return nextOrder?.OrderId ?? 0;
        }
        /// <summary>
        /// 获取店铺Logo
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetorderPrintLogo()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "orders" && x.Variable == "orderPrintLogo")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取站点名称
        /// </summary>
        /// <returns></returns>
		public async Task<string> GetSiteName()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "global" && x.Variable == "SiteName")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取公司名称
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetCompeny()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "print" && x.Variable == "Compeny")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取地址
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetAddress()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "print" && x.Variable == "Address")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取邮箱
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetAdminEmail()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "global" && x.Variable == "AdminEmail")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取电话
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetTelephone()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "print" && x.Variable == "Telephone")
                  .FirstAsync();
            return configs?.Value ?? "";
        }
        /// <summary>
        /// 获取海外仓库
        /// </summary>
        /// <returns></returns>
        public async Task<bool> GetConfigOverseas()
        {
            var configs = await db.Queryable<config>()
                  .Where(x => x.GroupId == "global" && x.Variable == "Overseas")
                  .FirstAsync();
            var b = Convert.ToBoolean(configs?.Value);

            return b;
        }


        /// <summary>
        /// 获取产品属性
        /// </summary>
        /// <param name="proIds"></param>
        /// <returns></returns>
        public async Task<string> GetCreateProducts(List<int> proIds)
        {
            // 配置参数初始化
            var c = new
            {
                manage = new { web_lang = "en" }, // 示例值，需根据实际配置填充
                config = new { isOverseas = true, Overseas = new Dictionary<string, int>() }, // 示例值
                my_order = new { Position = "DESC" }
            };

            // 请求参数处理
            var promotion_ary = proIds;

            // 产品数据查询
            var productsRow = new List<products>();
            var productsAry = new Dictionary<int, products>();
            if (promotion_ary.Any())
            {
                productsRow = db.Queryable<products>()
                    .Where(p => promotion_ary.Contains(p.ProId))
                    .OrderBy(p => p.ProId, OrderByType.Desc)
                    .ToList();

                productsAry = productsRow.ToDictionary(p => p.ProId);
            }

            // 产品属性处理
            var attr_ary = new Dictionary<int, List<products_attribute>>();
            if (productsAry.Any())
            {
                var attr_row = db.Queryable<products_attribute>()
                    .Where(a => productsAry.Keys.Contains(a.ProId))
                    //.Select(a => new { a.AttrId, a.Name_en, a.ProId, a.Position, a.Options })
                    .OrderBy(a => a.Position, OrderByType.Asc)
                    .ToList();

                attr_ary = attr_row.GroupBy(a => a.ProId)
                    .ToDictionary(g => g.Key, g => g.ToList());
            }

            // 产品扩展信息
            var develAry = db.Queryable<products_development>()
                .Where(d => productsAry.Keys.Contains(d.ProId))
                .ToDictionary(d => d.ProId, d => d.Warehouse);

            // 库存信息处理
            var productsStockBat = new Dictionary<string, object>
            {
                { "ProIdAry", proIds },
                { "Field", new List<string> { "ProId", "Stock", "SKU" } }
            };

            var ProStockAry = _helpsProductsService.GetProductsStockBat(productsStockBat);
            //GetProductsStockBat(productsAry.Keys.ToList());

            // 仓库配置处理
            var warehouseRow = db.Queryable<shipping_overseas>()
                .Where(w => w.OvId > 0)
                .ToList();

            var warehouseAry = warehouseRow.ToDictionary(w => w.OvId, w => w.Name);
            var warehouseIdAry = warehouseRow.ToDictionary(w => w.Name, w => w.OvId);

            // 组合规格处理
            var combinatin_row = db.Queryable<products_selected_attribute_combination>()
                .Where(comb => productsAry.Keys.Contains(comb.ProId.Value) &&
                             comb.Status == "valid" &&
                             !string.IsNullOrEmpty(comb.Title))
                .OrderBy(comb => comb.CId, OrderByType.Asc)
                .ToList();

            // 组合数据处理
            var ext_ary = new Dictionary<int, Dictionary<string, Dictionary<string, Dictionary<int, object>>>>();
            var variants_ary = new Dictionary<int, Dictionary<string, object>>();
            // ...（其他组合数据处理逻辑保持与PHP一致）
            // 组合数据处理（续）
            //var ext_ary = new Dictionary<int, Dictionary<string, Dictionary<string, Dictionary<int, object>>>>();
            //var variants_ary = new Dictionary<int, Dictionary<string, object>>();
            var overseasUsedAry = new Dictionary<int, int>();
            var attrWarehouseData = new Dictionary<int, Dictionary<int, int>>();

            if (combinatin_row.Any())
            {
                foreach (var v in combinatin_row)
                {
                    // 解码组合数据
                    var comAry = new object[] { v.Price, v.Stock, v.Weight, v.SKU, v.OldPrice, v.ImageId, v.VariantsId, v.OvId };
                    var _key = JsonConvert.DeserializeObject<List<string>>(v.Data);
                    var key = string.Join("_", _key);

                    // 处理属性名称
                    string attrName;
                    if (productsAry[v.ProId.Value].IsCombination == 1)
                    {
                        attrName = "Total";
                    }
                    else
                    {
                        var attrNameJson = JsonConvert.DeserializeObject<List<string>>(v.AttrName);
                        attrName = attrNameJson.First().Trim('"', '[', ']');
                    }

                    // 构建扩展数组
                    if (!ext_ary.ContainsKey(v.ProId.Value))
                    {
                        ext_ary[v.ProId.Value] = new Dictionary<string, Dictionary<string, Dictionary<int, object>>>();
                    }
                    if (!ext_ary[v.ProId.Value].ContainsKey(attrName))
                    {
                        ext_ary[v.ProId.Value][attrName] = new Dictionary<string, Dictionary<int, object>>();
                    }
                    if (!ext_ary[v.ProId.Value][attrName].ContainsKey(key))
                    {
                        ext_ary[v.ProId.Value][attrName][key] = new Dictionary<int, object>();
                    }
                    ext_ary[v.ProId.Value][attrName][key][v.OvId] = comAry;

                    // 构建变体数组
                    variants_ary[Convert.ToInt32(v.VariantsId)] = new Dictionary<string, object>
        {
            { "key", _key },
            { "data", comAry },
            { "warehouse", v.AttrName == JsonConvert.SerializeObject(new[] { "Ship From" }) ? 1 : 0 }
        };

                    // 海外仓库使用跟踪
                    if (!overseasUsedAry.ContainsKey(v.ProId.Value))
                    {
                        overseasUsedAry[v.ProId.Value] = 0;
                    }

                    if (productsAry[v.ProId.Value].IsCombination == 1 && v.OvId > 0 && overseasUsedAry[v.ProId.Value] == 0)
                    {
                        overseasUsedAry[v.ProId.Value] = 1;
                    }

                    if (productsAry[v.ProId.Value].IsCombination == 2 && _key.Contains(c.config.Overseas.First().Key) && overseasUsedAry[v.ProId.Value] == 0)
                    {
                        overseasUsedAry[v.ProId.Value] = 1;
                    }

                    // 仓库数据跟踪
                    if (v.OvId > 0)
                    {
                        if (!attrWarehouseData.Keys.Contains(v.ProId.Value))
                        {
                            attrWarehouseData[v.ProId.Value] = new Dictionary<int, int>();
                        }
                        attrWarehouseData[v.ProId.Value][v.OvId] = 1;
                    }

                    if (productsAry[v.ProId.Value].IsCombination == 2)
                    {
                        var overseasId = c.config.Overseas.First().Value;
                        if (!attrWarehouseData.Keys.Contains(v.ProId.Value))
                        {
                            attrWarehouseData[v.ProId.Value] = new Dictionary<int, int>();
                        }
                        attrWarehouseData[v.ProId.Value][overseasId] = 1;
                    }
                }
            }

            // 单规格产品仓库处理
            foreach (var proId in productsAry.Keys.ToList())
            {
                var proData = productsAry[proId];
                var isCombination = proData.IsCombination;
                var warehouseDataJson = develAry.Keys.Contains(proId.ToString()) ? develAry[proId.ToString()] : "";
                var warehouseData = JsonConvert.DeserializeObject<Dictionary<string, object>>(warehouseDataJson.ToString());

                if (isCombination == 0 && c.config.isOverseas && warehouseData != null && warehouseData.Any())
                {
                    productsAry[proId].SKU = "";
                    var combinationRow = db.Queryable<products_selected_attribute_combination>()
                        .Where(comb => comb.ProId == proId &&
                                     comb.Status == "valid" &&
                                     comb.AttrName == JsonConvert.SerializeObject(new[] { "Ship From" }))
                        .OrderBy(comb => comb.CId, OrderByType.Asc)
                        .ToList();

                    foreach (var v in combinationRow)
                    {
                        var comAry = new object[] { v.Price, v.Stock, v.Weight, v.SKU, v.OldPrice, v.ImageId, v.VariantsId, v.OvId };
                        var _key = JsonConvert.DeserializeObject<List<string>>(v.Data);
                        var key = string.Join("_", _key);

                        // 构建扩展数组
                        if (!ext_ary.Keys.Contains(proId))
                        {
                            ext_ary[proId] = new Dictionary<string, Dictionary<string, Dictionary<int, object>>>();
                        }
                        if (!ext_ary[proId].Keys.Contains("Ship From"))
                        {
                            ext_ary[proId]["Ship From"] = new Dictionary<string, Dictionary<int, object>>();
                        }
                        if (!ext_ary[proId]["Ship From"].Keys.Contains(key))
                        {
                            ext_ary[proId]["Ship From"][key] = new Dictionary<int, object>();
                        }
                        ext_ary[proId]["Ship From"][key][v.OvId] = comAry;

                        // 构建变体数组
                        variants_ary[Convert.ToInt32(v.VariantsId)] = new Dictionary<string, object>
                        {
                            { "key", _key },
                            { "data", comAry }
                        };

                        // 仓库数据跟踪
                        if (!attrWarehouseData.Keys.Contains(proId))
                        {
                            attrWarehouseData[proId] = new Dictionary<int, int>();
                        }
                        attrWarehouseData[proId][v.OvId] = 1;
                    }

                    // 更新海外使用状态
                    if (!overseasUsedAry.Keys.Contains(proId))
                    {
                        overseasUsedAry[proId] = 0;
                    }
                    if (combinationRow.Any())
                    {
                        overseasUsedAry[proId] = 1;
                    }
                }
            }



            // 视图渲染处理
            var html_ary = new List<string>();
            var html_id_ary = new List<int>();

            foreach (var proid in promotion_ary)
            {
                if (proid == 0) continue;

                if (productsAry.TryGetValue(proid, out var row))
                {
                    //string product_contents;
                    ////if (proid.ToString().Contains("Add_"))
                    ////{
                    ////    product_contents = RenderCustomizeView(row);
                    ////}
                    ////else
                    ////{
                    ////    product_contents = RenderProductView(row, proid, variants_ary, warehouseIdAry);
                    ////}

                    //html_ary.Add(product_contents);
                    html_id_ary.Add(proid);
                }
            }

            //return Json(new
            //{
            //    Html = html_ary,
            //    ProId = html_id_ary,
            //    IsFirst = proidAry.Any() ? 0 : 1
            //});
            return "";


        }


        /// <summary>
        /// 获取产品属性
        /// </summary>
        /// <param name="combinations"></param>
        /// <param name="Pro"></param>
        /// <returns></returns>
        public string GetProAttribute(List<products_selected_attribute_combination> combinations, products Pro)
        {
            var extAry = new Dictionary<string, Dictionary<string, Dictionary<string, object[]>>>();

            foreach (var combo in combinations)
            {
                // 解码Data字段（假设存储的是JSON数组）
                var keys = JsonConvert.DeserializeObject<List<string>>(combo.Data);
                string key = string.Join("_", keys);

                // 获取属性名称
                string attrName = Pro.IsCombination == 1
                    ? "Total"
                    : JsonConvert.DeserializeObject<List<string>>(combo.AttrName)
                        .FirstOrDefault()?.Trim('"', ']', '[') ?? "";

                // 构建数据数组（保持与PHP相同的顺序）
                var dataArray = new object[]
         {
            combo.Price.Value.ToString("F2"),
            combo.Stock,
            combo.Weight,
            combo.SKU,
            combo.OldPrice.ToString("F2"),
            combo.ImageId,
            combo.VariantsId,
            combo.OvId // 保持原始类型（可能为int/string）
         };


                SetExtAryValue(
                    extAry,
                    Pro.IsCombination == 1 ? "Total" : attrName, // 直接使用attrName
                    key,
                    combo.OvId.ToString(),
                    dataArray // 直接传递数组
                );
            }

            // 移除外层字典包装
            //        var result = new Dictionary<string, object>();
            //        foreach (var attr in extAry)
            //        {
            //            var innerDict = new Dictionary<string, object>();
            //            foreach (var keyVal in attr.Value)
            //            {
            //                innerDict[keyVal.Key] = keyVal.Value
            //.Select(arr => arr.ToList())
            //.ToList();
            //            }
            //            result[attr.Key] = innerDict;
            //        }
            var settings = new Newtonsoft.Json.JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver
                {
                    NamingStrategy = new Newtonsoft.Json.Serialization.DefaultNamingStrategy()
                },
                //StringEscapeHandling = StringEscapeHandling.EscapeNonAscii,
                StringEscapeHandling = StringEscapeHandling.EscapeHtml,
                Formatting = Formatting.None  // 保持紧凑格式（HTML中需要时可手动换行）
            };

            return JsonConvert.SerializeObject(extAry, settings)
                //.Replace("\"", "")  // 手动转义HTML中的双引号
                .Replace("\r\n", "");      // 移除换行符（根据需求调整）
            /*.Replace(" ", "");*/        // 移除空格（根据需求调整）
                                          //return JsonConvert.SerializeObject(result, Formatting.Indented);
        }
        private void SetExtAryValue1(
            Dictionary<string, Dictionary<string, List<List<object>>>> extAry,
            string attrName,
            string key,
            string OvId,
            List<object> dataArray)
        {
            if (!extAry.ContainsKey(attrName))
            {
                extAry[attrName] = new Dictionary<string, List<List<object>>>();
            }

            if (!extAry[attrName].ContainsKey(key))
            {
                extAry[attrName][key] = new List<List<object>>();
            }

            // 直接添加数组到二维列表
            extAry[attrName][key].Add(dataArray);
        }
        private void SetExtAryValue(
    Dictionary<string, Dictionary<string, Dictionary<string, object[]>>> extAry,
    string attrName,
    string key,
    string ovId,
    object[] dataArray)
        {
            // 第一层：属性名称
            if (!extAry.ContainsKey(attrName))
            {
                extAry[attrName] = new Dictionary<string, Dictionary<string, object[]>>();
            }

            // 第二层：组合键
            if (!extAry[attrName].ContainsKey(key))
            {
                extAry[attrName][key] = new Dictionary<string, object[]>();
            }

            // 第三层：OvId直接作为键存储数组
            extAry[attrName][key][ovId] = dataArray;
        }




        /// <summary>
        /// 获取产品分类
        /// </summary>
        /// <returns></returns>
        public async Task<List<products_category>> GetProducts_Categories()
        {
            var ret = await db.Queryable<products_category>()
                    .OrderBy(x => x.Category_en, OrderByType.Asc)
                    .ToListAsync();
            return ret;
        }
        /// <summary>
        /// 获取产品标签
        /// </summary>
        /// <returns></returns>
        public async Task<List<products_tags>> GetProducts_tags()
        {
            var ret = await db.Queryable<products_tags>()
                    .OrderBy(x => x.Name_en, OrderByType.Asc)
                    .ToListAsync();
            return ret;
        }
        /// <summary>
        /// 获取添加产品--产品信息
        /// </summary>
        /// <param name="Keyword"></param>
        /// <param name="CateId"></param>
        /// <param name="TagId"></param>
        /// <param name="MoreId"></param>
        /// <param name="exclude"></param>
        /// <param name="Page"></param>
        /// <param name="Count"></param>
        /// <returns></returns>
        public async Task<PagedList<products>> GetProductsChoiceSearchActionV2
            (string Keyword = "", string CateId = "", string TagId = "", string MoreId = "", string exclude = "",
            int Page = 1, int Count = 32)
        {


            try
            {
                var CategoryProIdList = new List<int>();
                if (CateId != "0")
                {
                    CategoryProIdList = db.Queryable<products_category_relate>()
                        .Where(x => x.CateId == Convert.ToInt32(CateId))
                        .Select(x => x.ProId)
                        .ToList();
                }
                var TagIdList = new List<int>();
                if (!string.IsNullOrWhiteSpace(TagId))
                {
                    TagIdList = TagId.Split(',').Select(int.Parse).ToList();
                }
                Expressionable<products> exp = Expressionable.Create<products>();
                foreach (var tag in TagIdList)
                {
                    string formattedTag = $"|{tag}|"; // 格式化为 |10| 或 |11|
                    exp = exp.Or(o => o.Tags.Contains(formattedTag));
                }


                // query = query
                //.LeftJoin<orders_tags>((o, ot) =>
                //    SqlFunc.Contains(ot.Tags, "|" + o.OrderId + "|")
                //).Where((o, ot) => TIdList.Contains(ot.Name))
                //.Select(o => o);




                var MoreIdList = new List<string>();
                if (!string.IsNullOrWhiteSpace(MoreId))
                {
                    MoreIdList = MoreId.Split(',').ToList();
                }

                var productsList = await db.Queryable<products>()
                    .Where(o => o.SoldOut == false)
                     .WhereIF(!string.IsNullOrWhiteSpace(Keyword), o => o.Name_en.Contains(Keyword) || o.SKU.Contains(Keyword) || o.Number.Contains(Keyword))
                     .WhereIF(!string.IsNullOrWhiteSpace(CateId) && CateId != "0", o => CategoryProIdList.Contains(o.ProId))
                     .WhereIF(!string.IsNullOrWhiteSpace(TagId) && TagIdList.Count > 0, exp.ToExpression())
                //.WhereIF(!string.IsNullOrWhiteSpace(MoreId) && MoreIdList.Count > 0, o => true)
                .ToListAsync();

                // 执行分页查询
                var paged = productsList.OrderByDescending(o => o.ProId).Skip((Page - 1) * Count).Take(Count).ToList();

                var resss = new PagedList<products>(paged, Page - 1, Count, productsList.Count);
                return resss;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询订单列表失败");
                return new PagedList<products>(new List<products>(), Page, Count, 0);
            }
        }


        /// <summary>
        /// 产品筛选下拉窗构建
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetSelectProductsFilter(string CateId, string tagId, string moreId)
        {
            // 参数获取与处理
            int cateId = 0;
            if (!string.IsNullOrEmpty(CateId))
            {
                cateId = Convert.ToInt32(CateId ?? "0");
            }
            int[] tagIdAry = [];
            if (!string.IsNullOrEmpty(CateId))
            {
                tagIdAry = string.IsNullOrEmpty(tagId) ? new int[0] : tagId.Split(',').Select(int.Parse).ToArray();
            }

            //// 参数处理
            //int.TryParse(request.Form["CateId"].FirstOrDefault(), out int cateId);
            //string tagId = request.Form["TagId"].FirstOrDefault();
            //string moreId = request.Form["MoreId"].FirstOrDefault();

            // 分类数据
            var allCategory = _helpsCategoryService.AllCategory();
            var productsCategoryRow = allCategory.UId["0,"].Take(20).ToList();


            var categorySearchRow = db.Queryable<products_category>()
                .Select(c => new { c.CateId, c.Category_en })
                .First(c => c.CateId == cateId);

            // 标签数据
            var tagsRow = db.Queryable<products_tags>()
                .OrderBy(t => t.TId, OrderByType.Desc)
                .Take(20)
                .ToList();
            var tagsSearchRow = db.Queryable<products_tags>()
                .Select(t => new { t.TId, t.Name_en })
                .OrderBy(t => t.TId, OrderByType.Desc)
                .ToList();
            if (!string.IsNullOrEmpty(tagId))
            {
                tagsSearchRow = db.Queryable<products_tags>()
               .Select(t => new { t.TId, t.Name_en })
               .Where(t => tagIdAry.Contains(t.TId))
               .OrderBy(t => t.TId, OrderByType.Desc)
               .ToList();
            }


            // 构建HTML
            var htmlBuilder = new StringBuilder();

            // 分类下拉框
            htmlBuilder.Append(@"<div class='rows i_select_category item clean'>
                <label>分类</label>
                <div class='input'>");

            var categoryOptions = productsCategoryRow.Select(v => new
            {
                Name = v.Category_en,
                Type = "products_category",
                Value = v.CateId,
                Disabled = false,
                Table = v.CategoryChild ? "products_category" : null
            }).ToList();

            var categoryValue = categorySearchRow != null ? new
            {
                //Input = allCategory.CateId.TryGetValue(cateId, out var cat) ? cat.CategoryAliasName : categorySearchRow.Category_en,
                Input = allCategory.CateId.TryGetValue(cateId, out var cat) ? cat.Category_en : categorySearchRow.Category_en,
                Select = categorySearchRow.CateId,
                Type = "products_category"
            } : null;

            htmlBuilder.Append(_helpsManageService.BoxDropDouble(
                "cateId", "Select",
                categoryOptions,
                categoryValue,
                0, "", 0, 1, "", "Select", 0));

            htmlBuilder.Append(@"</div></div>");

            // 标签下拉框
            htmlBuilder.Append(@"<div class='rows i_select_tags item clean'>
                <label>标签</label>
                <div class='input'>");

            var tagOptions = tagsRow.Select(t => new
            {
                Name = t.Name_en,
                Value = t.TId,
                Type = "products_tags"
            }).ToList();

            var tagValues = tagsSearchRow.Select(t => new
            {
                Name = t.Name_en,
                Value = t.TId,
                Type = "products_tags"
            }).ToList();

            htmlBuilder.Append(_helpsManageService.BoxDropDouble(
                "curtagId", "Select",
                tagOptions,
                tagValues,
                0, "", 1, 1, "", "Select", 0));

            htmlBuilder.Append(@"</div></div>");

            // 更多筛选下拉框
            htmlBuilder.Append(@"<div class='rows i_select_more item clean'>
                <label>更多筛选</label>
                <div class='input'>");

            var moreOptions = new List<dynamic>
            {
                new { Name = "新品", Value = "new", Type = "more_filter" },
                new { Name = "畅销产品", Value = "sales", Type = "more_filter" }
            };
            var moreValues = new object();
            moreValues = moreId?.Split(',').Select(v => new
            {
                //Name = LangPack.Lang($"plugins.products_recommend.products_type.{v}"),
                Name = "相关产品",
                Value = v,
                Type = "more_filter"
            }).ToList();

            htmlBuilder.Append(_helpsManageService.BoxDropDouble(
                "curmoreId", "Select",
                moreOptions,
                moreValues,
                0, "", 1, 1, "", "Select", 0));

            htmlBuilder.Append(@"</div></div>");

            return htmlBuilder.ToString();
        }











        #region
        //private (Dictionary<string, List<products_category>> UId, Dictionary<int, products_category> CateId) GetAllCategory(string status = "")
        //{
        //    var result = new
        //    {
        //        UId = new Dictionary<string, List<products_category>>(),
        //        CateId = new Dictionary<int, products_category>()
        //    };

        //    var query = db.Queryable<products_category>();

        //    // 处理状态过滤
        //    if (!string.IsNullOrEmpty(status))
        //    {
        //        if (status == "soldin")
        //            query = query.Where(c => c.IsSoldOut == false);
        //        else if (status == "soldout")
        //            query = query.Where(c => c.IsSoldOut == true);
        //    }

        //    var allCategories = query.OrderBy(c => c.CateId, OrderByType.Asc).ToList();

        //    // 构建分类树结构
        //    foreach (var category in allCategories)
        //    {
        //        var uKey = $"{category.UId}{category.CateId},";
        //        var aliasName = string.Join(" > ", uKey.Split(',')
        //            .Where(c => !string.IsNullOrEmpty(c))
        //            .Select(c => category.Category_en));

        //        category.Category_en = aliasName.TrimStart('>');

        //        if (!result.UId.ContainsKey(category.UId))
        //            result.UId[category.UId] = new List<products_category>();

        //        result.UId[category.UId].Add(category);
        //        result.CateId[category.CateId] = category;
        //    }

        //    // 添加子级标记
        //    foreach (var item in result.CateId)
        //    {
        //        var category = item.Value;
        //        category.CategoryChild = result.UId.ContainsKey($"{category.UId}{category.CateId},");
        //        result.CateId[item.Key] = category;
        //    }

        //    return (result.UId, result.CateId);
        //}




        //// 构建分类下拉HTML
        //private string BuildCategoryHtml(List<products_category> categories,
        //                         dynamic categorySearchRow,
        //                         Dictionary<int, products_category> cateIdCategory)
        //{
        //    var sb = new StringBuilder();
        //    sb.AppendLine("<div class='input c_s_box'>");

        //    // 构建下拉选项
        //    var selectItems = categories.Select(c => new
        //    {
        //        Name = c.Category_en,
        //        Type = "products_category",
        //        Value = c.CateId,
        //        Disabled = false,
        //        Table = c.CategoryChild ? "products_category" : null
        //    }).ToList();

        //    // 构建已选值
        //    var selectedValue = new
        //    {
        //        Input = categorySearchRow != null ?
        //            (cateIdCategory.ContainsKey(categorySearchRow.CateId) ?
        //                cateIdCategory[categorySearchRow.CateId].CategoryAliasName :
        //                categorySearchRow.Category_en) :
        //            "",
        //        Select = categorySearchRow?.CateId,
        //        Type = "products_category"
        //    };

        //    sb.Append(BoxDropDouble("cateId", "Select", selectItems, selectedValue));
        //    sb.AppendLine("</div>");

        //    return sb.ToString();
        //}
        //// 构建标签下拉HTML
        //private string BuildTagsHtml(List<products_tags> tags, List<products_tags> selectedTags)
        //{
        //    var sb = new StringBuilder();
        //    sb.AppendLine("<div class='input'>");

        //    // 构建下拉选项
        //    var selectItems = tags.Select(t => new
        //    {
        //        Name = t.Name_en,
        //        Value = t.TId,
        //        Type = "products_tags"
        //    }).ToList();

        //    // 构建已选值
        //    var selectedValues = selectedTags.Select(t => new
        //    {
        //        Name = t.Name_en,
        //        Value = t.TId,
        //        Type = "products_tags"
        //    }).ToList();

        //    sb.Append(BoxDropDouble("curtagId", "Select", selectItems, selectedValues));
        //    sb.AppendLine("</div>");

        //    return sb.ToString();
        //}
        //public static string BoxDropDouble(string selectName, string inputName,
        //                              IEnumerable<object> dataAry, object value,
        //                              int type = 0, string inputAttr = "",
        //                              int isCheckbox = 0, int isMore = 0,
        //                              string placeholder = "", string topType = "",
        //                              int isShowAdd = 1, string tips = "")
        //{
        //    var sb = new StringBuilder();
        //    var dataList = dataAry?.ToList() ?? new List<object>();
        //    var val = value as dynamic ?? new { Input = "", Select = "", Type = "" };
        //    var isEdit = type == 0;
        //    var isMulti = isCheckbox == 1;
        //    var jsonData = JsonConvert.SerializeObject(dataList);
        //    var moreClass = isMore == 1 && dataList.Count >= 20 ? "block" : "none";
        //    var nodataClass = dataList.Count == 0 ? "" : "none";

        //    sb.AppendLine($@"<dl class='box_drop_double{(isEdit ? " edit_box" : "")}' data-checkbox='{isMulti}' data-showadd='{isShowAdd}'>");

        //    // 多选模式（复选框列表）
        //    if (isMulti)
        //    {
        //        sb.AppendLine("<dt class='box_checkbox_list'>");
        //        sb.AppendLine("<div class='select_placeholder' style='display: none;'></div>");
        //        sb.AppendLine("<div class='select_list'>");

        //        // 生成已选项
        //        if (val is IEnumerable<object> values)
        //        {
        //            foreach (dynamic item in values)
        //            {
        //                sb.AppendLine($@"<div class='selected_item'>
        //                <span>{item.Name}</span>
        //                <input type='hidden' name='{selectName}[]' value='{item.Value}' />
        //                <a href='javascript:;' class='remove_item'>×</a>
        //            </div>");
        //            }
        //        }

        //        sb.AppendLine("</div>");
        //        sb.AppendLine($@"<input type='text' class='box_input check_input' 
        //                {inputAttr} name='{inputName}' value='' 
        //                placeholder='{placeholder}' autocomplete='off' />");
        //        sb.AppendLine($@"<input type='hidden' name='{selectName}' value='' class='hidden_value' />
        //                <input type='hidden' name='{selectName}Type' value='' class='hidden_type' />");
        //        sb.AppendLine("</dt>");
        //    }
        //    else
        //    {
        //        dynamic vals = val;
        //        // 编辑模式（文本框+下拉）
        //        if (!IsNullOrEmptyTwo(vals))
        //        {
        //            if (isEdit)
        //            {
        //                sb.AppendLine($@"<dt>
        //            <input type='text' class='box_input' 
        //                name='{inputName}' 
        //                placeholder='{(string.IsNullOrEmpty(placeholder) ? "请选择或输入" : placeholder)}' 
        //                value='{vals.Input}' 
        //                autocomplete='off' {inputAttr} />
        //            <input type='hidden' name='{selectName}' 
        //                value='{vals.Select}' 
        //                class='hidden_value' />
        //            <input type='hidden' name='{selectName}Type' 
        //                value='{vals.Type}' 
        //                class='hidden_type' />
        //        </dt>");
        //            }
        //            // 纯下拉模式
        //            else
        //            {
        //                sb.AppendLine($@"<dt>
        //            <div class='box_select'>
        //                <span>{(string.IsNullOrEmpty(vals.Name) ? "请选择" : vals.Name)}</span>
        //                <input type='hidden' name='{selectName}' 
        //                    value='{vals.Select}' 
        //                    {inputAttr} 
        //                    class='hidden_value' />
        //                <input type='hidden' name='{selectName}Type' 
        //                    value='{vals.Type}' 
        //                    class='hidden_type' />
        //            </div>
        //        </dt>");
        //            }
        //        }

        //    }

        //    // 下拉菜单主体
        //    sb.AppendLine(@"<dd class='drop_down'>
        //    <div class='drop_menu' data-type='" + topType + @"'>");

        //    // 提示信息
        //    if (!string.IsNullOrEmpty(tips))
        //    {
        //        sb.AppendLine($@"<div class='global_app_tips obvious'>
        //        <em></em><span>{tips}</span>
        //    </div>");
        //    }

        //    // 返回按钮
        //    sb.AppendLine(@"<a href='javascript:;' class='btn_back' 
        //    data-value='' data-type='' 
        //    data-table='' data-top='0' 
        //    data-all='0' style='display:none;'>返回</a>");

        //    // 下拉内容区域
        //    sb.AppendLine($@"<div class='drop_skin' style='display:none;'></div>
        //    <div class='drop_list' 
        //        data='{jsonData.Replace("\"", "&quot;")}' 
        //        data-more='{moreClass}'>");

        //    // 生成选项列表
        //    if (dataList!=null&& dataList.Count>0)
        //    {
        //        foreach (dynamic item in dataList)
        //        {
        //            string iconHtml = "";
        //            if (item.GetType().GetProperty("Icon") != null)
        //            {
        //                iconHtml = $"<i class='{item.Icon}'></i>";
        //            }

        //            //var dict = item as IDictionary<string, object>;
        //            sb.AppendLine($@"<div class='drop_item' 
        //            data-value='{item.Value}' 
        //            data-type='{item.Type}'>
        //            {item.Name}
        //            {iconHtml}
        //        </div>");
        //        }
        //    }


        //    sb.AppendLine(" </div>");

        //    // 加载更多按钮
        //    sb.AppendLine($@"<a href='javascript:;' class='btn_load_more' 
        //    data-value='' 
        //    data-type='' 
        //    data-table='' 
        //    data-top='0' 
        //    data-all='0' 
        //    data-check-all='0' 
        //    data-start='1' 
        //    style='display:{moreClass};'>加载更多</a>");

        //    // 空数据提示
        //    sb.AppendLine($@"<div class='drop_nodata_tip' 
        //    style='display:{nodataClass};'>暂无数据</div>");

        //    sb.AppendLine("</div></dd></dl>");

        //    return sb.ToString();
        //}
        //[SugarTable("ProductsCategory")]
        //public class ProductsCategory
        //{
        //    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        //    public int CateId { get; set; }
        //    public string Category_en { get; set; }
        //    public string UId { get; set; }
        //    public bool IsSoldOut { get; set; }
        //    public string CategoryAliasName { get; set; }
        //    public bool CategoryChild { get; set; }
        //}
        #endregion
        public static bool IsNullOrEmptyTwo(dynamic value)
        {
            // 1. 先判断是否为null
            if ((object)value == null) return true;

            // 2. 处理字符串类型
            if (value is string str)
                return string.IsNullOrEmpty(str);

            // 3. 处理集合类型
            // 判断集合是否为空（适用于数组、List等）
            if (value is System.Collections.IEnumerable collection)
            {
                return !collection.GetEnumerator().MoveNext();
            }
            // 4. 处理值类型默认值
            var type = value.GetType();
            if (type.IsValueType)
                return type.IsGenericType &&
                       type.GetGenericTypeDefinition() == typeof(Nullable<>) &&
                       value.Equals(Activator.CreateInstance(type));

            return false;
        }













        /// <summary>
        /// 根据产品id获取产品信息
        /// </summary>
        /// <param name="ProIdList"></param>
        /// <returns></returns>
        public async Task<List<products>> GetproductsByProId(List<int> ProIdList)
        {
            var ret = await db.Queryable<products>()
                    .Where(x => ProIdList.Contains(x.ProId))
                    .OrderBy(x => x.ProId, OrderByType.Desc)
                    .ToListAsync();
            return ret;
        }
        /// <summary>
        /// 根据产品id获取产品的库存
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<int> getQtyByProId(int ProId)
        {
            var ret = db.Queryable<products_selected_attribute_combination>()
                    .Where(x => x.ProId == ProId)
                    .Sum(x => x.Stock) ?? 0;
            return ret;

        }
        /// <summary>
        /// 根据产品id获取规格属性列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<List<products_selected_attribute_combination>> getAttributeByProId(int ProId)
        {
            var ret = db.Queryable<products_selected_attribute_combination>()
                    .Where(x => x.ProId == ProId && x.Status == "valid" && x.Title != "")
                    .OrderBy(x => x.CId, OrderByType.Asc)
                    .ToList();
            return ret;

        }
        /// <summary>
        /// 根据产品id获取规格属性列表--用于新增的时候
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<List<products_selected_attribute_combination>> getAttributeTwoByProId(int ProId)
        {
            var ret = db.Queryable<products_selected_attribute_combination>()
                    .Where(x => x.ProId == ProId)
                    .OrderBy(x => x.CId, OrderByType.Asc)
                    .ToList();
            return ret;

        }
        /// <summary>
        /// 根据产品id获取规格属性收货列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<List<products_selected_attribute_combination>> getAttributeShipByProId(int ProId)
        {
            var ret = db.Queryable<products_selected_attribute_combination>()
                    .Where(x => x.ProId == ProId && x.Status == "valid" && x.AttrName.Contains("Ship From"))
                    .OrderBy(x => x.CId, OrderByType.Asc)
                    .ToList();
            return ret;

        }

        /// <summary>
        /// 根据产品id获取规格属性标题列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<List<products_attribute>> getproducts_attributeByProId(int ProId)
        {
            var ret = db.Queryable<products_attribute>()
                    .Where(x => x.ProId == ProId)
                    .ToList();
            return ret;
        }
        /// <summary>
        /// 根据规格属性id获取规格属性标题
        /// </summary>
        /// <param name="AttrId"></param>
        /// <returns></returns>
        public async Task<string> getproducts_attributeByAttrId(int AttrId)
        {
            var ret = db.Queryable<products_attribute>()
                    .Where(x => x.AttrId == AttrId)
                    .First()?.Name_en ?? "";
            return ret;
        }
        /// <summary>
        /// 根据仓库id获取仓库名称
        /// </summary>
        /// <param name="OvId"></param>
        /// <returns></returns>
        public async Task<string> getshipping_overseasByOvId(int OvId)
        {
            var ret = db.Queryable<shipping_overseas>()
                    .Where(x => x.OvId == OvId)
                    .First()?.Name ?? "";
            return ret;
        }
        /// <summary>
        /// 获取仓库列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<shipping_overseas>> getshipping_overseas()
        {
            var ret = db.Queryable<shipping_overseas>()
                    .ToList();
            return ret;
        }

        /// <summary>
        /// 根据产品id获取仓库列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<List<shipping_overseas>> Getshipping_overseasByProId(int ProId)
        {
            var proattr = db.Queryable<products_selected_attribute_combination>()
                    .Where(x => x.ProId == ProId)
                    .Select(x => x.OvId)
                    .Distinct()
                    .ToList();

            var warehouses = await Db.Queryable<shipping_overseas>()
                .Where(x => proattr.Contains(x.OvId))
                       .OrderBy(x => x.MyOrder)
                       .ToListAsync();
            return warehouses;
        }
        /// <summary>
        /// 根据id获取用户地址
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<List<user_address_book>> getuser_address_bookList(int UserId)
        {
            var user_address_bookList = db.Queryable<user_address_book>()
                .Where(x => x.UserId == UserId && x.IsBillingAddress == false)
                .OrderBy(x => x.AId, OrderByType.Desc)
                .ToList();

            var CIds = user_address_bookList.Select(x => x.CId).ToList();
            var countryList = db.Queryable<country>()
                .Where(x => CIds.Contains(x.CId)).ToList();
            foreach (var item in user_address_bookList)
            {
                item.Country = countryList.Where(x => x.CId == item.CId).First()?.Country;
            }
            return user_address_bookList;
        }
        /// <summary>
        /// 获取用户邮箱和id
        /// </summary>
        /// <returns></returns>
        public async Task<List<CarrierResultResponse>> GetUserBookData()
        {
            try
            {
                //var UserIdList = db.Queryable<user_address_book>()
                //.Select(x => x.UserId)
                //.Distinct()
                //.ToList();
                //WHERE IsRegistered = 1 and Status = 1 and Locked = 0 ORDER BY Email DESC

                var UserList = await db.Queryable<user>()
                    //.Where(x => UserIdList.Contains(x.UserId))
                    .Where(x => x.IsRegistered == true && x.Status == 1 && x.Locked == false)
                    .OrderBy(x => x.Email, OrderByType.Asc)
                    .OrderBy(x => x.UserId, OrderByType.Desc)
                    .Take(20)
                    .ToListAsync();
                //转换数据
                var result = new List<CarrierResultResponse>();
                foreach (var item in UserList)
                {
                    result.Add(new CarrierResultResponse
                    {
                        Name = item.Email,
                        Value = item.UserId.ToString(),
                        Type = "customer"
                    });

                }
                return result;
            }
            catch (Exception ex)
            {
                return new List<CarrierResultResponse>();
                throw;
            }

        }

        /// <summary>
        /// 点击下拉框获取用户邮箱和id
        /// </summary>
        /// <returns></returns>
        public async Task<PagedList<CarrierResultResponse>> GetNextPageUserBookData(int Start, string Keyword = "")
        {
            try
            {
                var Count = 20;//查询总数上限
                RefAsync<int> total = 0;
                var UserList = await db.Queryable<user>()
                    //.Where(x => UserIdList.Contains(x.UserId))
                    .Where(x => x.IsRegistered == true && x.Status == 1 && x.Locked == false)
                    .WhereIF(!string.IsNullOrEmpty(Keyword), x => x.Email.Contains(Keyword))
                    .OrderBy(x => x.Email, OrderByType.Desc)
                    .Take(20)
                    .ToPageListAsync(Start + 1, Count, total);

                //var pList = orderList.ToPagedList(pageNum, pageSize, total);
                //转换数据
                var result = new PagedList<CarrierResultResponse>();
                foreach (var item in UserList)
                {
                    result.Add(new CarrierResultResponse
                    {
                        Name = item.Email,
                        Value = item.UserId.ToString(),
                        Type = "customer"
                    });

                }
                return result;
            }
            catch (Exception ex)
            {
                return new PagedList<CarrierResultResponse>();
                throw;
            }

        }

        /// <summary>
        /// 更改/插入用户地址
        /// </summary>
        /// <param name="CustomerValue"></param>
        /// <param name="Customer"></param>
        /// <param name="CustomerType"></param>
        /// <param name="FirstName"></param>
        /// <param name="LastName"></param>
        /// <param name="AddressLine1"></param>
        /// <param name="AddressLine2"></param>
        /// <param name="City"></param>
        /// <param name="country_id_input"></param>
        /// <param name="country_id"></param>
        /// <param name="country_idType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="Province"></param>
        /// <param name="ZipCode"></param>
        /// <param name="CountryCode"></param>
        /// <param name="PhoneNumber"></param>
        /// <param name="tax_code_type"></param>
        /// <param name="tax_code_value"></param>
        /// <param name="SaveAddress"></param>
        /// <param name="AddressId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateUser_address_book(string CustomerValue, string Customer, string CustomerType, string FirstName, string LastName,
            string AddressLine1, string AddressLine2, string City, string country_id_input, string country_id, string country_idType, string _DoubleOption,
            string Province, string ZipCode, string CountryCode, string PhoneNumber, string tax_code_type, string tax_code_value, string SaveAddress, string AddressId)
        {






            var ret = db.Queryable<user_address_book>()
                .Where(x => x.UserId == Convert.ToInt32(country_id))
                .ToList();

            return true;
        }
        /// <summary>
        /// 根据id获取货币
        /// </summary>
        /// <param name="currencyId"></param>
        /// <returns></returns>
        public async Task<currency> getCurrency(int currencyId)
        {
            var currencyList = await _currencyService.GetAllCurrencyCache();
            var ret = currencyList
                .Where(x => x.CId == currencyId)
                .First();
            return ret;
        }
        /// <summary>
        /// 通过货币缩写，获取货币中的符号
        /// </summary>
        /// <param name="Currency"></param>
        /// <returns></returns>
        public async Task<string> getCurrencySymbol(string Currency)
        {
            var currencyList = await _currencyService.GetAllCurrencyCache();
            var ret = currencyList
                .Where(x => x.Currency == Currency)
                .First();
            return ret?.Symbol;
        }

        /// <summary>
        /// 获取货币列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<currency>> getCurrencyList()
        {
            var currencyList = await _currencyService.GetAllCurrencyCache();
            var ret = currencyList
                .ToList();
            return ret;
        }
        /// <summary>
        /// 根据id获取产品信息
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        public async Task<products> GetproductById(int ProId)
        {
            var ret = db.Queryable<products>()
                .Where(x => x.ProId == ProId)
                .ToList()
                .FirstOrDefault();
            return ret;
        }
        /// <summary>
        /// 根据ids获取产品信息
        /// </summary>
        /// <param name="ProIds"></param>
        /// <returns></returns>
        public async Task<List<products>> GetProductsByIds(List<int?>? ProIds)
        {
            var ret = db.Queryable<products>()
                .Where(x => ProIds.Contains(x.ProId))
                .ToList();
            return ret;
        }

        /// <summary>
        /// 根据邮箱获取用户信息
        /// </summary>
        /// <param name="userEmail"></param>
        /// <returns></returns>
        public async Task<user> GetUserByEmailAsync(string userEmail)
        {
            var ret = db.Queryable<user>()
                .Where(x => x.Email == userEmail)
                .First();
            return ret;
        }
        /// <summary>
        /// 通过id获取支付方式
        /// </summary>
        /// <param name="PId"></param>
        /// <returns></returns>
        public async Task<payment> GetpaymentByid(int PId)
        {
            var ret = db.Queryable<payment>()
                .Where(x => x.PId == PId)
                .First();
            return ret;
        }
        /// <summary>
        /// 获取可以使用的支付方式列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<payment>> GetpaymentList()
        {
            var ret = await db.Queryable<payment>()
                .Where(x => x.IsUsed == true)
                .ToListAsync();
            return ret;
        }

        /// <summary>
        /// 新增订单
        /// </summary>
        /// <param name="ordersData"></param>
        /// <returns></returns>
        public async Task<int> AddOrder(orders ordersData)
        {
            try
            {
                var OrderId = await db.Insertable(ordersData).ExecuteReturnIdentityAsync();
                return OrderId;
            }
            catch (Exception e)
            {

                throw;
            }

        }
        /// <summary>
        /// 获取订单前缀
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetOrderPrefix()
        {
            var jsonStringConfig = db.Queryable<config>()
                     .Where(x => x.GroupId == "global" && x.Variable == "OrderPrefix")
                     .First();
            return jsonStringConfig?.Value ?? "";
        }



        /// <summary>
        /// 创建商品订单和包裹信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="packageAry"></param>
        /// <param name="isShippingTemplate"></param>
        /// <param name="ShippingMethod"></param>
        /// <param name="ShippingName"></param>
        /// <param name="ShippingPrice"></param>
        /// <returns></returns>
        public async Task CreateOrdersPackageAsync(int orderId, Dictionary<string, object> packageAry, bool isShippingTemplate,
            List<string> ShippingMethod, List<string> ShippingName, List<string> ShippingPrice, dynamic shippingAry)
        {
            try
            {
                // 创建订单产品
                var insertProducts = new List<orders_products_list>();
                int productCounter = 1;

                //foreach (var package in packageAry)
                //{
                //    var items = JsonConvert.DeserializeObject<List<ProductItemTwo>>(package.Value.ToString());
                //    foreach (var item in items)
                //    {
                //        decimal price = decimal.TryParse(item.PicPath.ToString(), out decimal p) ? p : 0;
                //        int qty = int.TryParse(item.Qty.ToString(), out int quantity) ? quantity : 0;
                //        ProductPrice += HelpsCart.CeilPrice(price * qty);
                //        TotalWeight += (decimal.TryParse(item.Weight.ToString(), out decimal weight) ? weight : 0) * qty;
                //    }
                //}




                foreach (var pack in packageAry)
                {
                    var items = JsonConvert.DeserializeObject<List<BuyItem>>(pack.Value.ToString());
                    foreach (var item in items)
                    {
                        string variantsId = "";
                        if (item.VariantsId != null && item.VariantsId != "")
                        {
                            variantsId = item.IsCombination
                                ? item.VariantsId
                                : item.VariantsId[0].ToString() ?? "";
                            //variantsId = item.IsCombination
                            //    ? JsonConvert.SerializeObject(item.VariantsId)
                            //    : item.VariantsId[0].ToString() ?? "";
                        }
                        var ProId = 0;
                        if (!item.ProId.Contains("Add_"))
                        {
                            ProId = Convert.ToInt32(item.ProId);
                        }
                        var KeyId = 0;
                        if (!item.KeyId.Contains("Add_"))
                        {
                            KeyId = Convert.ToInt32(item.KeyId);
                        }
                        insertProducts.Add(new orders_products_list
                        {
                            OrderId = orderId,
                            ProId = ProId,
                            BuyType = Convert.ToSByte(item.BuyType),
                            KeyId = KeyId,
                            Name = item.Name?.ToString() ?? "",
                            SKU = item.SKU?.ToString() ?? "",
                            PicPath = CheckCdnPicture(item.PicPath?.ToString() ?? ""),
                            StartFrom = Convert.ToInt16(item.StartFrom),
                            Weight = item.Weight,
                            Price = item.Price,
                            Qty = Convert.ToInt32(item.Qty),
                            Property = JsonConvert.SerializeObject(item.Property),
                            PropertyPrice = item.PropertyPrice,
                            CustomPrice = item.CustomPrice,
                            Attr = JsonConvert.SerializeObject(item.Attr),
                            VariantsId = variantsId,
                            //VariantsId = !string.IsNullOrEmpty(variantsId)? (!variantsId.Contains("[")? "[" + variantsId + "]" : variantsId ) : "",
                            OvId = Convert.ToInt16(item.OvId),
                            CId = 0,
                            Discount = 100,
                            Remark = "",
                            Language = item.Language?.ToString() ?? "",
                            AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                            isVirtual = item.isVirtual,
                            ProductsType = "normal",
                            Volume = 0m,
                            PriceType = ""
                        });

                        // 批量插入
                        if (productCounter++ % 100 == 0)
                        {
                            await db.Insertable(insertProducts).ExecuteCommandAsync();
                            insertProducts.Clear();
                        }
                    }
                }

                if (insertProducts.Any())
                {
                    await db.Insertable(insertProducts).ExecuteCommandAsync();
                }

                // 创建包裹信息
                //var shippingAry = new
                //{
                //    ShippingMethod = ShippingMethod,
                //    ShippingName = ShippingName,
                //    ShippingPrice = ShippingPrice
                //};

                await CreateOrdersPackageAsyncs(orderId, isShippingTemplate, ShippingMethod, ShippingName, ShippingPrice, shippingAry);
            }
            catch (Exception e)
            {

                throw;
            }

        }



        public async Task CreateOrdersPackageAsyncs(int orderId, bool shippingTemplate,
            List<string> ShippingMethod, List<string> ShippingName, List<string> ShippingPrice, dynamic shippingAry)
        {
            try
            {
                // 删除现有包裹
                await db.Deleteable<orders_package>().Where(o => o.OrderId == orderId).ExecuteCommandAsync();

                // 获取订单产品
                var products = await db.Queryable<orders_products_list>()
                    .Where(o => o.OrderId == orderId)
                    .Select(o => new
                    {
                        o.LId,
                        o.ProId,
                        o.Qty,
                        o.OvId,
                        o.CId,
                        o.Weight,
                        o.BuyType,
                        o.isVirtual
                    })
                    .ToListAsync();

                var proIds = products.Select(p => p.ProId).Distinct().ToList();
                var tIdDict = new Dictionary<int, int>();

                if (shippingTemplate)
                {
                    var templateProducts = await db.Queryable<products>()
                        .Where(p => proIds.Contains(p.ProId))
                        .Select(p => new { p.TId, p.ProId })
                        .ToListAsync();

                    tIdDict = templateProducts.ToDictionary(tp => tp.ProId, tp => tp.TId);
                }

                var overseas = await db.Queryable<shipping_overseas>()
                    .Select(s => new { s.OvId, s.Name })
                    .ToDictionaryAsync(s => s.OvId, s => s.Name);

                //var productGroups = products.GroupBy(p => $"{p.OvId}-{tIdDict.GetValueOrDefault(p.ProId, 0)}");
                //var virtualData = products.GroupBy(p => $"{p.OvId}-{tIdDict.GetValueOrDefault(p.ProId, 0)}")
                //                         .ToDictionary(g => g.Key, g => g.Select(p => p.isVirtual));

                // 修改 productGroups 分组
                var productGroups = products.GroupBy(p =>
                    $"{p.OvId}-{GetTIdOrDefault(Convert.ToInt32(p.ProId), tIdDict)}");

                // 修改 virtualData 分组
                var virtualData = products.GroupBy(p =>
                    $"{p.OvId}-{GetTIdOrDefault(Convert.ToInt32(p.ProId), tIdDict)}")
                    .ToDictionary(g => g.Key, g => g.Select(p => p.isVirtual));



                int virtualOrderCount = products.Count(p => p.isVirtual);
                bool allVirtualOrder = virtualOrderCount > 0 && virtualOrderCount == products.Count;
                if (ShippingMethod != null)
                {
                    var packages = new List<orders_package>();
                    //var shippingMethods = ShippingMethod;
                    //var shippingNames = ShippingName;
                    //var shippingPrices = ShippingPrice;
                    int i = 0;

                    //var shippingMethodss = shippingAry.ShippingMethod.ToObject<string[]>();
                    //var shippingNamess = shippingAry.ShippingName.ToObject<string[]>();
                    //var shippingPricess = shippingAry.ShippingPrice.ToObject<decimal[]>();

                    var shippingMethods = (string[])shippingAry.ShippingMethod;  // 直接类型转换
                    var shippingNames = (string[])shippingAry.ShippingName;
                    var shippingPrices = (decimal[])shippingAry.ShippingPrice;
                    #region 注释
                    //foreach (var method in shippingMethodss)
                    //{
                    //    var key = method;
                    //    var currentProducts = products.Where(p => key.Contains(p.OvId.ToString()));

                    //    decimal totalWeight = Convert.ToDecimal(currentProducts.Sum(p => p.Weight * p.Qty));
                    //    int ovId = int.Parse(key.Split('-')[0]);
                    //    var proWhere = currentProducts.Select(p => p.ProId).ToList();


                    //    var maxGoodsType = await db.Queryable<products_customs>()
                    //        .Where(pc => proWhere.Contains(pc.ProId))
                    //        .MaxAsync(pc => pc.GoodsType) ?? 1;

                    //    var proInfo = currentProducts.ToDictionary(p => p.LId, p => p.Qty);
                    //    var sdfsdf = shippingNamess[Array.IndexOf(shippingMethodss, method)];
                    //    var asdfsadf = shippingPricess[Array.IndexOf(shippingMethodss, method)];
                    //    packages.Add(new orders_package
                    //    {
                    //        OrderId = orderId,
                    //        TempOrderId = 0,
                    //        ShippingExpress = shippingNamess[Array.IndexOf(shippingMethodss, method)],
                    //        ShippingMethodSId = Convert.ToInt16(method),
                    //        ShippingPrice = shippingPricess[Array.IndexOf(shippingMethodss, method)],
                    //        ProInfo = JsonConvert.SerializeObject(proInfo),
                    //        Weight = totalWeight,
                    //        OvId = Convert.ToInt16(ovId)
                    //    });
                    //}
                    #endregion

                    foreach (var method in shippingMethods)
                    {
                        var key = method;

                        int ovId = int.Parse(key.Split('-')[0]);
                        var currentProducts = products.Where(p => ovId == p.OvId);

                        decimal totalWeight = Convert.ToDecimal(currentProducts.Sum(p => p.Weight * p.Qty));

                        var proWhere = currentProducts.Select(p => p.ProId).ToList();

                        string Warehouse = "";
                        if (overseas.TryGetValue(Convert.ToString(ovId), out var warehouseName))
                        {
                            Warehouse = warehouseName.ToString();
                        }
                        else
                        {
                            Warehouse = ""; // 或记录日志
                        }

                        var maxGoodsType = await db.Queryable<products_customs>()
                            .Where(pc => proWhere.Contains(pc.ProId))
                            .MaxAsync(pc => pc.GoodsType) ?? 1;

                        var proInfo = currentProducts.ToDictionary(p => p.LId, p => p.Qty);

                        packages.Add(new orders_package
                        {
                            OrderId = orderId,
                            TempOrderId = 0,
                            ShippingExpress = shippingNames[Array.IndexOf(shippingMethods, method)],
                            ShippingMethodSId = Convert.ToInt16(key.Split('-')[1]),
                            ShippingPrice = shippingPrices[Array.IndexOf(shippingMethods, method)],
                            ProInfo = JsonConvert.SerializeObject(proInfo),
                            Weight = totalWeight,
                            OvId = Convert.ToInt16(ovId),
                            //Warehouse = overseas.GetValueOrDefault(ovId, ""),
                            Carrier = "",
                            Warehouse = Warehouse,
                            GoodsType = maxGoodsType,
                            allVirtual = currentProducts.All(p => p.isVirtual),
                            ParentId = 0,
                            Main = false,
                            ShippingInsurance = false,
                            ShippingInsurancePrice = 0m,
                            Status = 0,
                            ShippingTime = 0,
                            ApiShippingPrice = 0m

                        });
                        i++;
                    }
                    if (packages.Any())
                    {
                        await db.Insertable(packages).ExecuteCommandAsync();
                    }
                }


                #region 注释
                //if (shippingAry?.ShippingMethod != null)
                //{
                //    var packages = new List<orders_package>();
                //    var shippingMethods = shippingAry.ShippingMethod.ToObject<string[]>();
                //    var shippingNames = shippingAry.ShippingName.ToObject<string[]>();
                //    var shippingPrices = shippingAry.ShippingPrice.ToObject<decimal[]>();

                //    foreach (var method in shippingMethods)
                //    {
                //        var key = method;
                //        var currentProducts = products.Where(p => key.Contains(p.OvId.ToString()));

                //        decimal totalWeight = Convert.ToDecimal(currentProducts.Sum(p => p.Weight * p.Qty));
                //        int ovId = int.Parse(key.Split('-')[0]);
                //        var proWhere = currentProducts.Select(p => p.ProId).ToList();

                //        string Warehouse = "";
                //        if (overseas.TryGetValue(Convert.ToString(ovId), out var warehouseName))
                //        {
                //            Warehouse = warehouseName.ToString();
                //        }
                //        else
                //        {
                //            Warehouse = ""; // 或记录日志
                //        }

                //        var maxGoodsType = await db.Queryable<products_customs>()
                //            .Where(pc => proWhere.Contains(pc.ProId))
                //            .MaxAsync(pc => pc.GoodsType) ?? 1;

                //        var proInfo = currentProducts.ToDictionary(p => p.LId, p => p.Qty);

                //        packages.Add(new orders_package
                //        {
                //            OrderId = orderId,
                //            TempOrderId = 0,
                //            ShippingExpress = shippingNames[Array.IndexOf(shippingMethods, method)],
                //            ShippingMethodSId = method,
                //            ShippingPrice = shippingPrices[Array.IndexOf(shippingMethods, method)],
                //            ProInfo = JsonConvert.SerializeObject(proInfo),
                //            Weight = totalWeight,
                //            OvId = Convert.ToInt16(ovId),
                //            //Warehouse = overseas.GetValueOrDefault(ovId, ""),
                //            Warehouse = Warehouse,
                //            GoodsType = Convert.ToBoolean(maxGoodsType),
                //            allVirtual = currentProducts.All(p => p.isVirtual)
                //        });
                //    }

                //    if (packages.Any())
                //    {
                //        await db.Insertable(packages).ExecuteCommandAsync();
                //    }
                //}
                #endregion
                if (allVirtualOrder)
                {
                    await db.Updateable<orders>()
                        .SetColumns(o => o.isVirtual)
                        .Where(o => o.OrderId == orderId)
                        .ExecuteCommandAsync();
                }
            }
            catch (Exception e)
            {

                _logger.LogError(e, e.Message);
            }


        }

        public class BuyItem
        {
            public int BuyType { get; set; }
            public string Name { get; set; }
            public string ProId { get; set; }
            public string KeyId { get; set; }
            public string SKU { get; set; }
            public string PicPath { get; set; }
            public int StartFrom { get; set; }
            public decimal Weight { get; set; }
            public int Volume { get; set; }
            public decimal Price { get; set; }
            public decimal PropertyPrice { get; set; }
            public decimal CustomPrice { get; set; }
            public int Discount { get; set; }
            public int Qty { get; set; }
            public Dictionary<string, object> Property { get; set; }
            public Dictionary<string, object> Attr { get; set; }
            public string VariantsId { get; set; }
            public int OvId { get; set; }
            public string Language { get; set; }
            public DateTime AddTime { get; set; }
            public bool IsCombination { get; set; }
            public bool isVirtual { get; set; }
        }

        private string CheckCdnPicture(string path)
        {
            // 实现图片路径检查逻辑
            return string.IsNullOrEmpty(path) ? "" : path;
        }

        private int GetTIdOrDefault(int proId, Dictionary<int, int> tIdDict)
        {
            return tIdDict.TryGetValue(proId, out int tId) ? tId : 0;
        }



        /// <summary>
        /// 记录日志、发送邮件、减库存
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="orderRow"></param>

        public async void CreateOrderLog(int orderId, orders orderRow)
        {
            orderRow = db.Queryable<orders>().Where(x => x.OrderId == orderId).First();
            var paymentStatus = orderRow.PaymentStatus.Trim();
            if (string.IsNullOrEmpty(paymentStatus))
            {
                paymentStatus = "unpaid";
            }

            //var orderRow = _dbContext.Orders.AsNoTracking().FirstOrDefault(o => o.OrderId == orderId);
            if (orderRow == null) return;

            var userName = $"{orderRow.ShippingFirstName} {orderRow.ShippingLastName}";
            var paidData = new Dictionary<string, object>
            {
                { "name", userName },
                { "paymentMethod", orderRow.PaymentMethod },
                { "currency", orderRow.Currency }
            };
            var logOption = await _helpsWriteOrderLogService.GetWriteOrderLogData("create", paidData);

            WriteOrderLog(new orders_log
            {
                IsAdmin = true,
                UserId = orderRow.UserId,
                UserName = userName,
                OrderId = orderId,
                Log = logOption.LogMessage,
                LogManage = logOption.LogTitle,
                LogData = logOption.LogData,
                OrderStatus = 1,
                PaymentStatus = "unpaid",
                ShippingStatus = orderRow.ShippingStatus,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
            });

            // 库存更新逻辑
            if (orderRow.OrderStatus != 7 && orderRow.CutStock == false)
            {
                OrdersProductsUpdate(1, orderRow);
            }

            if (paymentStatus == "paid")
            {
                HandlePayment(orderRow);
                UpdateOrderStatus(orderRow, userName);
                //SendPaymentEmail(orderRow);
            }

            //// 推送CRM队列
            //var queue = new HelpsQueue();
            //queue.Create("pushOrdersToCrmSend", new { OrderId = new[] { orderId } });
        }



        private async void UpdateOrderStatus(orders orderRow, string userName)
        {
            var userCount = db.Queryable<orders>()
                .Count(o => o.Email == orderRow.Email && new[] { 4, 5, 6 }.Contains(o.OrderStatus));

            var isNewCustom = userCount == 0 ? 1 : Convert.ToInt32(orderRow.IsNewCustom);

            orderRow.OrderStatus = 4;
            orderRow.PaymentStatus = "paid";
            orderRow.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            orderRow.IsNewCustom = Convert.ToBoolean(isNewCustom);
            db.Updateable(orderRow)
                         .ExecuteCommand();

            // 付款日志记录
            var paymentAmount = await OrdersPrice(orderRow, 1, 0, false);
            orderRow.PaymentMethod = paymentAmount.ToString();
            var paidData = new Dictionary<string, object>
            {
                { "paymentAmount", paymentAmount },
                { "paymentMethod", orderRow.PaymentMethod },
                { "currency", orderRow.Currency },
                { "paymentStatus", orderRow.PaymentStatus },
                { "operationType", "手动" }
            };


            var logOption = await _helpsWriteOrderLogService.GetWriteOrderLogData("paid", paidData);

            WriteOrderLog(new orders_log
            {
                IsAdmin = false,
                UserId = orderRow.UserId,
                UserName = userName,
                OrderId = orderRow.OrderId,
                Log = logOption.LogMessage,
                LogManage = logOption.LogTitle,
                LogData = logOption.LogData,
                OrderStatus = orderRow.OrderStatus,
                PaymentStatus = orderRow.PaymentStatus,
                ShippingStatus = orderRow.ShippingStatus,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
            });
        }





        private void HandlePayment(orders orderRow)
        {
            try
            {
                // 1. 获取支付信息
                var payment = db.Queryable<payment>()
                               .First(p => p.PId == orderRow.PId);

                // 2. 判断是否需要离线处理
                if (payment.IsOnline == false)
                {
                    // 3. 构建支付信息
                    var paymentInfo = new orders_payment_info
                    {
                        OrderId = orderRow.OrderId,
                        TempOrderId = orderRow.TempOrderId,
                        Account = orderRow.Email,
                        FirstName = orderRow.ShippingFirstName,
                        LastName = orderRow.ShippingLastName,
                        SentMoney = orderRow.ProductPrice,
                        MTCNNumber = "",
                        Currency = orderRow.Currency,
                        Country = orderRow.ShippingCountry,
                        SentTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now).ToString(),
                        BankTransactionNumber = orderRow.ShippingPhoneNumber,
                        Contents = "",
                        PicPath_0 = "",
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                    };

                    // 4. 获取或创建支付记录
                    var existingPayment = db.Queryable<orders_payment_info>()
                                           .First(p => p.OrderId == orderRow.OrderId);

                    if (existingPayment != null)
                    {
                        // 5. 精准更新字段
                        existingPayment.SentMoney = paymentInfo.SentMoney;
                        existingPayment.MTCNNumber = paymentInfo.MTCNNumber;
                        existingPayment.Currency = paymentInfo.Currency;
                        existingPayment.Country = paymentInfo.Country;
                        existingPayment.SentTime = paymentInfo.SentTime;
                        existingPayment.BankTransactionNumber = paymentInfo.BankTransactionNumber;
                        existingPayment.AccTime = paymentInfo.AccTime;

                        db.Updateable(existingPayment)
                          .UpdateColumns(p => new
                          {
                              p.SentMoney,
                              p.MTCNNumber,
                              p.Currency,
                              p.Country,
                              p.SentTime,
                              p.BankTransactionNumber,
                              p.AccTime
                          })
                          .ExecuteCommand();
                    }
                    else
                    {
                        // 6. 插入新记录
                        db.Insertable(paymentInfo)
                          .ExecuteReturnIdentity();
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }


        public void OrdersProductsUpdate(int orderStatus, orders ordersRow, int type = 0)
        {
            int orderId = ordersRow.OrderId;
            bool doIt = true;

            // 条件判断逻辑（保持不变）
            if ((type == 0 && orderStatus < 7 && !ordersRow.CutStock == false) ||
                (type == 1 && orderStatus == 7 && !ordersRow.CutCancel == false))
            {
                if (type == 1 && orderStatus == 7 && !ordersRow.CutStock == false)
                {
                    doIt = false;
                }

                if (doIt)
                {
                    // 1. 获取订单产品信息
                    var itemRows = db.Queryable<orders_products_list>()
                        .Where(p => p.OrderId == orderId)
                        .OrderBy(p => p.LId)
                        .ToList();

                    if (itemRows.Any())
                    {
                        // 2. 获取产品库存信息
                        var proIdAry = itemRows.Select(p => Convert.ToInt32(p.ProId)).Distinct().ToList();
                        //var proStockDict = GetProductsStockBat(new GetProductsStockBatParam
                        //{
                        //    ProIdAry = proIdAry,
                        //    Field = new[] { "ProId", "Stock" }
                        //}).ToDictionary(p => p.ProId);
                        var proStockAry = GetProductsStockBat(new GetProductsStockBatParam
                        {
                            ProIdAry = proIdAry,
                            Field = new[] { "ProId", "Stock" }
                        });

                        // 3. 获取产品信息
                        //var productsDict = db.Queryable<products>()
                        //    .Where(p => proIdAry.Contains(p.ProId))
                        //    .Select(p => new { p.ProId, p.CateId, p.MaxOQ, p.IsCombination })
                        //    .ToDictionary(p => p.ProId);
                        // 获取产品信息
                        var productsRow = db.Queryable<products>()
                            .Where(p => proIdAry.Contains(p.ProId))
                            .Select(p => new
                            {
                                p.ProId,
                                p.CateId,
                                p.MaxOQ,
                                p.IsCombination
                            })
                            .ToList();

                        var productsAry = productsRow.ToDictionary(p => p.ProId);
                        foreach (var item in itemRows)
                        {
                            if (!productsAry.TryGetValue(Convert.ToInt32(item.ProId), out var product)) continue;
                            if (!proStockAry.TryGetValue(Convert.ToInt32(item.ProId), out var stockInfo)) continue;


                            //if (!productsDict.TryGetValue(item.ProId, out var product) ||
                            //    !proStockDict.TryGetValue(item.ProId, out var stockInfo)) continue;

                            // 4. 处理组合商品库存
                            if (product.IsCombination is 0 or 2)
                            {
                                UpdateCombinationStock(item, type, product.IsCombination == 2);
                            }

                            // 5. 更新产品修改时间（SqlSugar 写法）
                            db.Updateable<products>()
                                .SetColumns(p => p.EditTime == DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now))
                                .Where(p => p.ProId == item.ProId)
                                .ExecuteCommand();

                            // 6. 更新产品销量（SqlSugar 表达式更新）
                            db.Updateable<products>()
                                .SetColumns(p => p.Sales == (type == 1 ? p.Sales - item.Qty : p.Sales + item.Qty))
                                .Where(p => p.ProId == item.ProId)
                                .ExecuteCommand();

                            // 7. 处理变体库存
                            if (!string.IsNullOrEmpty(item.VariantsId) &&
                                (product.IsCombination is 0 or 1))
                            {
                                UpdateVariantStock(item, type);
                            }
                        }

                        // 8. 自动上下架处理
                        //var allProIds = itemRows.Select(p => Convert.ToInt32(p.ProId)).Distinct().ToList();
                        //HelpsProducts.ProductsAutoOnOffShelf(new AutoOnOffShelfParam
                        //{
                        //    ProIdAry = allProIds,
                        //    CrmStockAry = new Dictionary<int, int>(),
                        //    CrmStockType = type == 1 ? "return" : "reduce"
                        //});
                    }

                    // 9. 更新订单状态（SqlSugar 条件更新）
                    var updateBuilder = db.Updateable<orders>()
                        .Where(o => o.OrderId == orderId);

                    if (type == 0)
                    {
                        updateBuilder.SetColumns(o => o.CutStock == true);
                    }
                    else if (type == 1)
                    {
                        updateBuilder.SetColumns(o => o.CutCancel == true);
                    }

                    updateBuilder.SetColumns(o => o.UpdateTime == DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now))
                        .ExecuteCommand();
                }
            }
        }

        private void UpdateCombinationStock(orders_products_list item, int type, bool isUeeshopCrm)
        {
            var combination = db.Queryable<products_selected_attribute_combination>()
                .First(c => c.ProId == item.ProId && c.VariantsId == "" && c.Status == "valid");

            if (combination != null)
            {
                int stockChange = type == 1 ? Convert.ToInt32(item.Qty) : -Convert.ToInt32(item.Qty);

                // SqlSugar 批量更新写法
                db.Updateable<products_selected_attribute_combination>()
                  .SetColumns(c => c.Stock == c.Stock + stockChange)
                  .Where(c => c.CId == combination.CId)
                  .ExecuteCommand();
            }
        }

        private void UpdateVariantStock(orders_products_list item, int type)
        {
            var combination = db.Queryable<products_selected_attribute_combination>()
                .First(c => c.ProId == item.ProId && c.VariantsId == item.VariantsId);

            if (combination != null)
            {
                int newStock = type == 1
                    ? Convert.ToInt32(combination.Stock) + Convert.ToInt32(item.Qty)
                    : Convert.ToInt32(combination.Stock) - Convert.ToInt32(item.Qty);

                // SqlSugar 批量更新写法
                db.Updateable<products_selected_attribute_combination>()
                  .SetColumns(c => c.Stock == newStock)
                  .Where(c => c.CId == combination.CId)
                  .ExecuteCommand();
            }
        }

        //public Dictionary<int, ProductStockInfo> GetProductsStockBat(GetProductsStockBatParam param)
        //{
        //    // 1. 先获取符合条件的产品ID列表
        //    var validProIds = db.Queryable<products>()
        //        .Where(p => param.ProIdAry.Contains(p.ProId) &&
        //                  (p.IsCombination == 0 || p.IsCombination == 2))
        //        .Select(p => p.ProId)
        //        .ToList();

        //    // 2. 查询组合库存信息
        //    var combinations = db.Queryable<products_selected_attribute_combination>()
        //        .Where(c => validProIds.Contains(Convert.ToInt32(c.ProId)) &&
        //                  c.Status == "valid" &&
        //                  c.StockStatus == "valid")
        //        .Select(c => new ProductStockInfo
        //        {
        //            ProId = Convert.ToInt32(c.ProId),
        //            Stock = Convert.ToInt32(c.Stock)
        //        })
        //        .ToList();

        //    return combinations.ToDictionary(c => c.ProId);
        //}
        public Dictionary<int, ProductStockInfo> GetProductsStockBat(GetProductsStockBatParam param)
        {
            var proIdAry = param.ProIdAry;
            var fields = param.Field ?? new[] { "ProId", "SKU", "Stock", "Weight" };

            var products = db.Queryable<products>()
                .Where(p => proIdAry.Contains(p.ProId) && new[] { 0, 2 }.Contains(p.IsCombination))
                .Select(p => new { p.ProId, p.IsCombination })
                .ToList();

            var combinations = new List<products_selected_attribute_combination>();

            foreach (var group in products.GroupBy(p => p.IsCombination))
            {
                var query = db.Queryable<products_selected_attribute_combination>()
                    .Where(c =>
                        proIdAry.Contains(Convert.ToInt32(c.ProId)) &&
                        c.Status == "valid" &&
                        c.StockStatus == "valid");

                if (group.Key == 2)
                {
                    query = query.Where(c => c.Title == "");
                }

                combinations.AddRange(query.ToList());
            }

            return combinations.ToDictionary(
                c => Convert.ToInt32(c.ProId),
                c => new ProductStockInfo
                {
                    ProId = Convert.ToInt32(c.ProId),
                    Stock = Convert.ToInt32(c.Stock)
                });
        }




        // 辅助类（保持不变）
        public class GetProductsStockBatParam
        {
            public List<int> ProIdAry { get; set; }
            public string[] Field { get; set; }
        }

        public class ProductStockInfo
        {
            public int ProId { get; set; }
            public int Stock { get; set; }
        }

        public class AutoOnOffShelfParam
        {
            public List<int> ProIdAry { get; set; }
            public Dictionary<int, int> CrmStockAry { get; set; }
            public string CrmStockType { get; set; }
        }













        /// <summary>
        /// 获取物流信息
        /// </summary>
        /// <param name="KId"></param>
        /// <param name="orderCreate"></param>
        /// <param name="orderAddress"></param>
        /// <param name="packageAry"></param>
        /// <param name="isShippingTemplate"></param>
        /// <param name="countryId"></param>
        /// <param name="statesSId"></param>
        /// <returns></returns>
        public Dictionary<string, object> actionThirdShipping(string KId, Dictionary<string, object> orderCreate,
                 Dictionary<string, object> orderAddress, Dictionary<string, object> packageAry, bool isShippingTemplate, int countryId, int statesSId)
        {

            //var c = GetAppParams();
            //var KId = Request.Form["packageId"].FirstOrDefault() ?? "0";
            //var OrderCreate = HttpContext.Session.GetString("OrderCreate") ?? "{}";
            //var orderAddress = JsonConvert.DeserializeObject<Dictionary<string, object>>(OrderCreate)?["second"] as Dictionary<string, object> ?? new Dictionary<string, object>();
            //var packageAry = (JsonConvert.DeserializeObject<Dictionary<string, object>>(OrderCreate)?["third"] as Dictionary<string, object>)?["package"] as Dictionary<string, object> ?? new Dictionary<string, object>();
            //var isShippingTemplate = c.plugins.Used.Contains("shipping_template");

            //// 获取国家ID和省份ID
            //int countryId = Convert.ToInt32(orderAddress["CountryId"]);
            //int statesSId = Convert.ToInt32(orderAddress["Province"]);
            var productsInfo = new List<object>();
            if (packageAry.TryGetValue(KId, out var rawValue))
            {
                // 第一层检测：直接是数组
                if (rawValue is object[] directArray)
                {
                    productsInfo = directArray.Cast<object>().ToList();
                }
                // 第二层检测：被包裹在单对象数组中
                else if (rawValue is Dictionary<string, object> singleObj &&
                         singleObj.TryGetValue("data", out var wrappedData) &&
                         wrappedData is object[] wrappedArray)
                {
                    productsInfo = wrappedArray.Cast<object>().ToList();
                }
                // 第三层检测：直接是对象但需要解包
                else if (rawValue is Dictionary<string, object> unwrappedObj)
                {
                    productsInfo.Add(unwrappedObj);
                }
                // 第四层检测：其他集合类型
                else if (rawValue is IEnumerable<object> enumerable)
                {
                    productsInfo = enumerable.Cast<object>().ToList();
                }
                // 兜底方案：尝试强制转换
                else
                {
                    try
                    {
                        productsInfo = JsonConvert.DeserializeObject<List<object>>(
                            JsonConvert.SerializeObject(rawValue));
                    }
                    catch { /* 忽略异常 */ }
                }
            }


            //List<object> productsInfo = null;

            //if (packageAry.TryGetValue(KId, out var rawValue))
            //{
            //    // 尝试直接转换为 List<object>
            //    if (rawValue is List<object> list)
            //    {
            //        productsInfo = list;
            //    }
            //    // 尝试处理嵌套结构（如 JSON 反序列化的对象）
            //    else if (rawValue is Dictionary<string, object> nestedDict &&
            //             nestedDict.TryGetValue("data", out var nestedValue) &&
            //             nestedValue is List<object> nestedList)
            //    {
            //        productsInfo = nestedList;
            //    }
            //}

            // 获取包裹信息
            //var productsInfo = packageAry.ContainsKey(KId) ? (packageAry[KId] as List<object>) : new List<object>();
            var proIdAry = productsInfo.Select(p => ((dynamic)p)["ProId"]).ToList();

            List<products> productsRow = new List<products>();
            Dictionary<int, products> productsDataAry = new Dictionary<int, products>();

            if (proIdAry.Any())
            {
                productsRow = db.Queryable<products>()
                    .Where(p => proIdAry.Contains(p.ProId))
                    .Select(p => new products
                    {
                        ProId = p.ProId,
                        IsFreeShipping = p.IsFreeShipping,
                        TId = p.TId,
                        IsCombination = p.IsCombination
                    })
                    .ToList();

                productsDataAry = productsRow.ToDictionary(p => p.ProId);
            }

            // 会员权益
            var freeShipping = _helpsUserService.GetUserFreeShipping(Convert.ToInt32(orderAddress["UserId"]));

            var result = GetShippingMethod(countryId, statesSId, productsInfo, productsDataAry, isShippingTemplate, freeShipping, KId);

            return result;
        }

        public Dictionary<string, object> GetShippingMethod(
       int countryId,
       int statesSId,
       List<object> productsInfo,
       Dictionary<int, products> productsDataAry,
       bool isShippingTemplate,
       bool freeShipping = false, string KId = "")
        {
            var proInfoAry = new Dictionary<string, dynamic>();
            var typeWeightAry = new Dictionary<string, dynamic>();

            if (productsInfo != null && productsInfo.Any())
            {
                foreach (var proDataObj in productsInfo)
                {
                    //var proData = (dynamic)proDataObj;
                    //if (productsDataAry.ContainsKey(Convert.ToInt32(proData["ProId"])))
                    //{
                    //    proData = proData.Merge(productsDataAry[Convert.ToInt32(proData["ProId"])]);
                    //}

                    //int proId = Convert.ToInt32(proData["ProId"]);

                    //将原始数据转换为Dictionary<string, object>
                    var proData = proDataObj as dynamic;
                    var proIdData = proData["ProId"].ToString();
                    int proId = 0;
                    if (!proIdData.Contains("Add_"))
                    {
                        proId = Convert.ToInt32(proData["ProId"]);
                    }
                    // 获取产品ID并转换为int

                    try
                    {

                        // 修改后的合并逻辑
                        if (productsDataAry.TryGetValue(proId, out products productData))
                        {
                            var productJObject = JObject.FromObject(productData);

                            // 合并所有属性（自动覆盖同名属性）
                            foreach (var prop in productJObject.Properties())
                            {
                                if (!((JObject)proData).Property(prop.Name)?.HasValues ?? true)
                                {
                                    ((JObject)proData)[prop.Name] = prop.Value;
                                }
                            }
                        }

                        // 合并产品数据（如果存在）
                        //if (productsDataAry.TryGetValue(proId, out products productData))
                        //{
                        //    // 手动合并属性（需要实现合并逻辑）
                        //    foreach (var prop in productData.GetType().GetProperties())
                        //    {
                        //        if (!proData.ContainsKey(prop.Name))
                        //        {
                        //            proData[prop.Name] = prop.GetValue(productData);
                        //        }
                        //    }
                        //}
                    }
                    catch (Exception e)
                    {

                        throw;
                    }


                    //var kid = $"{proData["OvId"]}-{(isShippingTemplate ? proData["TId"] : 0)}";
                    var kid = KId;
                    var price = _helpsCartService.CeilPrice(
                        (Convert.ToDecimal(proData["Price"]) + Convert.ToDecimal(proData["PropertyPrice"]) + Convert.ToDecimal(proData["CustomPrice"])) *
                        (proData["Discount"].ToString() != "100" ? Convert.ToDecimal(proData["Discount"]) / 100 : 1)) *
                        Convert.ToInt32(proData["Qty"]);

                    if (!proInfoAry.ContainsKey(kid))
                    {
                        proInfoAry[kid] = new ShippingInfo
                        {
                            Weight = 0m,
                            Volume = 0m,
                            tWeight = 0m,
                            tVolume = 0m,
                            Price = 0m,
                            IsFreeShipping = 0,
                            OvId = proData["OvId"],
                            tQty = 0,
                            Qty = 0,
                            IsCombination = proData["IsCombination"],
                            IsFreeShippingAry = new List<int>(),
                            TId = proData["TId"],
                            GoodsType = 0
                        };
                    }
                    try
                    {
                        proInfoAry[kid].tWeight += Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].tVolume += Convert.ToDecimal(proData["Volume"]) * Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].tQty += Convert.ToInt32(proData["Qty"]);
                        proInfoAry[kid].Price += price;

                        var _isFreeShipping = "0";
                        if (Convert.ToString(proData["IsFreeShipping"]) == "False")
                        {
                            _isFreeShipping = "0";
                        }
                        else if (Convert.ToString(proData["IsFreeShipping"]) == "True")
                        {
                            _isFreeShipping = "1";
                        }
                        else
                        {
                            _isFreeShipping = Convert.ToString(proData["IsFreeShipping"]);
                        }
                        if (Convert.ToInt32(_isFreeShipping) == 1)
                        {
                            proInfoAry[kid].IsFreeShipping = 1;
                        }
                        else
                        {
                            proInfoAry[kid].Weight += Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                            proInfoAry[kid].Volume += Convert.ToDecimal(proData["Volume"]) * Convert.ToInt32(proData["Qty"]);
                            if (proData["BuyType"].ToString() != "5")
                            {
                                proInfoAry[kid].Qty += Convert.ToInt32(proData["Qty"]);
                            }
                        }

                        proInfoAry[kid].IsFreeShippingAry.Add(Convert.ToInt32(_isFreeShipping));
                    }
                    catch (Exception e)
                    {

                        throw;
                    }


                    // 获取当前产品的物流类型
                    var goodsType = Convert.ToInt32(db.Queryable<products_customs>()
                        .Where(pc => pc.ProId == proId)
                        .Select(pc => pc.GoodsType)
                        .First());

                    var aisiflyWeight = Convert.ToDecimal(proData["Weight"]) * Convert.ToInt32(proData["Qty"]);
                    try
                    {
                        var existing = typeWeightAry.ContainsKey(kid) ? typeWeightAry[kid] : new { };
                        var IsFreeShipping = proData["IsFreeShipping"].ToString();

                        typeWeightAry[kid] = PackageInfo(
                        existing,
                        goodsType,
                        proData["IsFreeShipping"].ToString() == "1" ? 0 : aisiflyWeight,
                        aisiflyWeight,
                        Convert.ToInt32(proData["OvId"]),
                        Convert.ToInt32(proData["TId"])
                        );

                        proInfoAry[kid].GoodsType = goodsType;
                    }
                    catch (Exception e)
                    {

                        throw;
                    }

                }
            }

            return GetShippingMethod(countryId, statesSId, proInfoAry, typeWeightAry, isShippingTemplate);
        }

        /// <summary>
        /// 获取物流方式和运费
        /// </summary>
        /// <param name="countryId">国家ID</param>
        /// <param name="statesSId">省份ID</param>
        /// <param name="proInfoAry">产品信息</param>
        /// <param name="typeWeightAry">产品的物流类型</param>
        /// <param name="isShippingTemplate">是否免运费(会员权益)</param>
        /// <returns>运费相关信息</returns>
        public Dictionary<string, object> GetShippingMethod(
      int countryId,
      int statesSId,
      Dictionary<string, dynamic> proInfoAry,
      Dictionary<string, dynamic> typeWeightAry,
      bool isShippingTemplate)
        {
            // 1. 参数检查
            if (countryId == 0 || proInfoAry == null || proInfoAry.Count == 0)
            {
                return new Dictionary<string, object>();
            }

            // 2. 整理物流类型和产品类型
            var goodsTypeAry = new Dictionary<string, List<int>>();
            try
            {
                foreach (var kid in proInfoAry.Keys)
                {
                    var proData = proInfoAry[kid];
                    goodsTypeAry[kid] = new List<int> { (int)proData.GoodsType };
                }
            }
            catch (Exception e)
            {

                throw;
            }


            var maxGoodsTypeAry = new Dictionary<string, int>();
            foreach (var k in goodsTypeAry.Keys)
            {
                var maxType = goodsTypeAry[k].Max();
                maxGoodsTypeAry[k] = maxType > 0 ? maxType : 1;
            }

            // 3. 处理地区信息
            if (statesSId > 0)
            {
                var statesCount = db.Queryable<country_states>()
                    .Where(cs => cs.CId == countryId && cs.SId == statesSId)
                    .Count();

                if (statesCount == 0)
                {
                    statesSId = 0;
                }
            }

            // 4. 获取可用快递信息
            var shippingQuery = db.Queryable<shipping>()
                .Where(s => s.ApiType == "0" && s.IsGet == true && s.IsUsed == 1)
                .Select(s => new
                {
                    s.SId,
                    s.Express,
                    s.Logo,
                    s.IsWeightArea,
                    s.UseCondition,
                    s.MinWeight,
                    s.MaxWeight,
                    s.IsUsed,
                    s.IsAPI,
                    s.WeightType,
                    s.TId,
                    s.GoodsType,
                    s.FreightRate,
                    s.OvId
                })
                .ToList();

            var shippingAry = shippingQuery.ToDictionary(s => s.SId.ToString());

            // 5. 物流分区信息
            var shippingAIdAry = db.Queryable<shipping_country>()
                .Where(sc => sc.CId == countryId)
                .Select(sc => sc.AId)
                .ToList();

            var shippingAreaQuery = db.Queryable<shipping_area>()
                .Where(sa => shippingAIdAry.Contains(sa.AId))
                .OrderBy(sa => sa.SId, OrderByType.Asc)
                .OrderBy(sa => sa.AId, OrderByType.Asc)
                .ToList();

            var row = new List<dynamic>();
            foreach (var v in shippingAreaQuery)
            {
                if (shippingAry.TryGetValue(v.SId.ToString(), out var shippingInfo))
                {
                    row.Add(new
                    {
                        shippingInfo.SId,
                        shippingInfo.Express,
                        shippingInfo.Logo,
                        shippingInfo.IsWeightArea,
                        shippingInfo.UseCondition,
                        shippingInfo.MinWeight,
                        shippingInfo.MaxWeight,
                        shippingInfo.IsUsed,
                        shippingInfo.IsAPI,
                        shippingInfo.WeightType,
                        shippingInfo.TId,
                        shippingInfo.GoodsType,
                        shippingInfo.FreightRate,
                        shippingInfo.OvId,
                        v.FreeType,
                        v.AId,
                        v.Brief,
                        v.FreeShippingPrice,
                        v.IsFreeShipping,
                        v.FreeShippingWeight,
                        v.FreeShippingQty,
                        v.AffixPrice
                    });
                }
            }


            // 6. 物流价格数据
            var shippingPriceQuery = db.Queryable<shipping_price>()
                .Where(sp => shippingAIdAry.Contains((ushort)sp.AId))
                .OrderBy(sp => sp.AId, OrderByType.Asc)
                .OrderBy(sp => sp.StartWeight, OrderByType.Asc)
                .ToList();

            var shippingPriceAry = shippingPriceQuery
                .GroupBy(sp => sp.AId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 7. 处理产品运费
            var rowAry = new Dictionary<int, dynamic>();
            var usedShippingSidAry = new List<int>();
            var shippingTidAry = new Dictionary<int, List<int>>();

            foreach (var v in shippingQuery)
            {
                usedShippingSidAry.Add(v.SId);
                if (isShippingTemplate && !string.IsNullOrEmpty(v.TId))
                {
                    var tIds = v.TId.Split('|', StringSplitOptions.RemoveEmptyEntries)
                                  .Select(int.Parse)
                                  .ToList();

                    foreach (var tId in tIds)
                    {
                        if (!shippingTidAry.ContainsKey(tId))
                        {
                            shippingTidAry[tId] = new List<int>();
                        }
                        shippingTidAry[tId].Add(v.SId);
                    }
                }
            }

            // 补全缺失的物流分区与仓库匹配逻辑
            foreach (var v in row)
            {
                int sId = v.SId;
                if (!usedShippingSidAry.Contains(sId)) continue;

                // 初始化快递条目（如果不存在）
                if (!rowAry.ContainsKey(sId))
                {
                    rowAry[sId] = new
                    {
                        info = v,
                        overseas = new Dictionary<int, dynamic>()
                    };
                }

                // 处理OvId仓库映射（包含-1默认仓库逻辑）
                string ovIdStr = v.OvId?.ToString().Trim() ?? "";
                var ovidAry = ovIdStr.Split('|', StringSplitOptions.RemoveEmptyEntries)
                                     .Select(id => id == "-1" ? "0" : id)
                                     .Select(int.Parse)
                                     .ToList();

                foreach (int warehouseId in ovidAry)
                {
                    // 确保overseas字典存在（理论上已初始化）
                    if (!rowAry[sId].overseas.ContainsKey(warehouseId))
                    {
                        rowAry[sId].overseas[warehouseId] = v;
                    }
                }
            }


            // 初始化结果结构
            var info = new Dictionary<string, List<Dictionary<string, object>>>();
            foreach (var kid in proInfoAry.Keys)
            {
                info[kid] = new List<Dictionary<string, object>>();
            }

            // 处理每个快递方式
            // 7. 处理产品运费（补全核心逻辑）
            foreach (var keyVal in rowAry)
            {
                var key = keyVal.Key;
                var val = keyVal.Value;
                var rowTwo = val.info;
                var overseasData = val.overseas;

                // 检查仓库匹配

                var hasMatchingOvId = proInfoAry.Values
                    .Any(p => overseasData.ContainsKey(Convert.ToInt32(p.OvId)));

                if (!hasMatchingOvId) continue;

                foreach (var productItem in proInfoAry)
                {
                    var productKey = productItem.Key;
                    var product = productItem.Value;

                    if (!overseasData.TryGetValue(Convert.ToInt32(product.OvId), out dynamic overseas)) continue;

                    // 检查产品类型
                    var goodsTypesTwo = (string)overseas.GoodsType;
                    var goodsTypes = goodsTypesTwo
                        .Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries) // 添加去除空项
                        .Select(int.Parse)
                        .ToList();
                    if (!goodsTypes.Contains(maxGoodsTypeAry[productKey])) continue;

                    // 检查运费模板
                    if (isShippingTemplate)
                    {
                        if (!shippingTidAry.TryGetValue(product.TId, out List<int> shippingIds) ||
                        !shippingIds.Contains(rowTwo.SId))
                        {
                            continue;
                        }

                    }

                    // 检查使用条件
                    var open = CalculateUseCondition(rowTwo, product);
                    if (!open) continue;

                    // 计算运费
                    var shippingPrice = CalculateBasePrice(overseas, product, shippingPriceAry);

                    // 应用运费调整
                    if (rowTwo.FreightRate != 0)
                    {
                        shippingPrice *= (1 + rowTwo.FreightRate);
                    }
                    var conformFree = false; //是否符合免费条件
                    // 处理免费条件
                    if (ShouldApplyFreeShipping(overseas, product, isShippingTemplate))
                    {
                        shippingPrice = 0;
                        conformFree = true;
                    }

                    // 附加费用
                    if ((!conformFree || (conformFree && !IsFreeType(overseas.FreeType, "surcharge"))) && overseas.AffixPrice > 0)
                    {
                        shippingPrice += overseas.AffixPrice;
                    }

                    // 构建返回数据
                    var shippingItem = new Dictionary<string, object>
                    {
                        { "SId", rowTwo.SId },
                        { "Name", overseas.Express },
                        { "Brief", overseas.Brief },
                        { "IsAPI", overseas.IsAPI },
                        { "type", "" },
                        { "weight", product.Weight },
                        { "ShippingPrice", _helpsCartService.CeilPrice(shippingPrice) },
                        { "ApiType", "0" }
                    };

                    info[productKey].Add(shippingItem);
                }
            }



            // 8. 集成U闪达物流（示例结构）
            //if (typeWeightAry != null && _appParams.Plugins.Used.Contains("asiafly"))
            if (typeWeightAry != null)
            {
                var acronym = db.Queryable<country>()
                    .Where(c => c.CId == countryId)
                    .Select(c => c.Acronym)
                    .First();

                var result = _helpsAsiaflyService.GetShippingExpress(acronym, typeWeightAry, 0, 1, isShippingTemplate, statesSId);
                MergeAsiaflyResults(info, result);
                // 合并结果逻辑...
            }

            // 9. 整理最终结果
            var infoAry = new Dictionary<string, List<Dictionary<string, object>>>();
            foreach (var kid in info.Keys)
            {
                var products = info[kid];
                var grouped = products
                    .GroupBy(p => p["ShippingPrice"])
                    .OrderBy(g => g.Key)
                    .SelectMany(g => g)
                    .ToList();

                infoAry[kid] = grouped;
            }

            return new Dictionary<string, object>
            {
                { "info", infoAry },
                { "shipping_template", isShippingTemplate ? 1 : 0 }
            };
        }



        private void MergeAsiaflyResults(
    Dictionary<string, List<Dictionary<string, object>>> info,
    Dictionary<string, List<dynamic>> asiaflyResult)
        {
            foreach (var productKey in asiaflyResult.Keys)
            {
                if (!info.ContainsKey(productKey))
                {
                    info[productKey] = new List<Dictionary<string, object>>();
                }

                var products = info[productKey];
                foreach (var item in asiaflyResult[productKey])
                {
                    var shippingItem = new Dictionary<string, object>
            {
                { "SId", item.SId },
                { "Name", item.Express },
                { "Brief", item.Brief },
                { "IsAPI", item.IsAPI },
                { "type", "" },
                { "weight", item.Weight },
                { "ShippingPrice", item.ShippingPrice },
                { "ApiType", "0" }
            };

                    products.Add(shippingItem);
                }
            }
        }


        private bool IsFreeType(string freeTypeJson, string targetType)
        {
            try
            {
                var freeTypes = JsonConvert.DeserializeObject<List<string>>(freeTypeJson) ?? new List<string>();
                return freeTypes.Contains(targetType);
            }
            catch
            {
                return false;
            }
        }


        private bool ShouldApplyFreeShipping(
    dynamic overseas,
    dynamic product,
    bool isFreeShipping)
        {
            var freeType = JsonConvert.DeserializeObject<List<string>>(overseas.FreeType);
            try
            {
                var res = isFreeShipping ||
                   (overseas.IsWeightArea != 3 &&
                    !freeType.Contains("0") &&
                    product.IsFreeShipping == 1 &&
                    product.Weight == 0) ||
                   (overseas.IsWeightArea == 3 &&
                    !freeType.Contains("0") &&
                    product.IsFreeShipping == 1 &&
                    product.Qty == 0) ||
                   (overseas.IsFreeShipping == true &&
                    overseas.FreeShippingPrice > 0 &&
                    product.Price >= overseas.FreeShippingPrice) ||
                   (overseas.IsFreeShipping == true &&
                    overseas.FreeShippingWeight > 0 &&
                    product.Weight < overseas.FreeShippingWeight) ||
                   (overseas.IsFreeShipping == true &&
                    overseas.FreeShippingQty > 0 &&
                    product.Qty >= overseas.FreeShippingQty) ||
                   (overseas.IsFreeShipping == true &&
                    overseas.FreeShippingPrice == 0 &&
                    overseas.FreeShippingWeight == 0 &&
                    overseas.FreeShippingQty == 0);
                return res;
            }
            catch (Exception e)
            {
                return true;
                throw;
            }


        }

        private decimal CalculateBasePrice(
    dynamic overseas,
    dynamic product,
    Dictionary<int, List<shipping_price>> shippingPriceAry)
        {
            if (!shippingPriceAry.TryGetValue(overseas.AId, out List<shipping_price> priceAry))
            {
                return 0;
            }



            //var shipping_priceList = shippingPriceAry[overseas.AId];
            //if (shipping_priceList==null)
            //{
            //    return 0;
            //}

            //var priceAry = (List<shipping_price>)shipping_priceList;
            decimal shippingPrice = 0;

            switch (Convert.ToInt32(overseas.IsWeightArea))
            {
                case 5: // 固定费用
                    var fixedPrice = GetPriceData(priceAry[0].Data)?.fixedPrice ?? 0;
                    return fixedPrice;

                case 0: // 按重量
                    var weightPrice = priceAry.FirstOrDefault(p => product.Weight <= p.EndWeight);
                    if (weightPrice == null) return 0;

                    var priceData = GetPriceData(weightPrice.Data);
                    if (weightPrice.Calculation == "additional")
                    {
                        var extWeight = product.Weight > priceData.firstWeight
                            ? product.Weight - priceData.firstWeight
                            : 0;

                        var extCountTwo = priceData.extWeight > 0
                            ? (int)Math.Ceiling(extWeight / priceData.extWeight)
                            : 0;

                        shippingPrice = priceData.firstPrice + (extCountTwo * priceData.extPrice);
                    }
                    else if (weightPrice.Calculation == "total")
                    {
                        shippingPrice = priceData.fixedPrice;
                    }
                    else if (weightPrice.Calculation == "each")
                    {
                        shippingPrice = priceData.fixedPrice * _helpsManageService.ToWeightUnitConversion(product.Weight, "KG");
                    }
                    return shippingPrice;

                case 3: // 按数量
                    var qtyPrice = GetPriceData(priceAry[0].Data);
                    var extQty = product.Qty > qtyPrice.firstMaxQty
                        ? product.Qty - qtyPrice.firstMaxQty
                        : 0;

                    var extCount = qtyPrice.extQty > 0
                        ? (int)Math.Ceiling((decimal)extQty / qtyPrice.extQty)
                        : 0;

                    return qtyPrice.firstQtyPrice + (extCount * qtyPrice.extQtyPrice);

                default:
                    return 0;
            }
        }
        private PriceData GetPriceData(string jsonData)
        {
            return JsonConvert.DeserializeObject<PriceData>(jsonData);
        }
        public class PriceData
        {
            public decimal firstPrice { get; set; }
            public decimal extPrice { get; set; }
            public decimal fixedPrice { get; set; }
            public decimal firstWeight { get; set; }
            public decimal extWeight { get; set; }
            public decimal firstQtyPrice { get; set; }
            public decimal extQtyPrice { get; set; }
            public int firstMaxQty { get; set; }
            public int extQty { get; set; }
        }

        private bool CalculateUseCondition(dynamic row, dynamic product)
        {
            bool open = false;
            if ((int)row.UseCondition == 1)
            {
                // 重量限制
                if ((row.MaxWeight > 0)
                        ? (product.tWeight >= row.MinWeight && product.tWeight <= row.MaxWeight)
                        : (product.tWeight >= row.MinWeight))
                {
                    open = true;
                }
            }
            else if ((int)row.UseCondition == 2)
            {
                // 数量限制
                if ((row.MaxWeight > 0)
                        ? (product.tQty >= row.MinWeight && product.tQty <= row.MaxWeight)
                        : (product.tQty >= row.MinWeight))
                {
                    open = true;
                }
            }
            else if ((int)row.UseCondition == 3)
            {
                // 总价限制
                if ((row.MaxWeight > 0)
                        ? (product.Price >= row.MinWeight && product.Price <= row.MaxWeight)
                        : (product.Price >= row.MinWeight))
                {
                    open = true;
                }
            }
            else
            {
                open = true;
            }
            return open;
            //    switch (row.UseCondition)
            //{
            //    case 1: // 重量限制
            //        return (row.MaxWeight > 0)
            //            ? (product.tWeight >= row.MinWeight && product.tWeight <= row.MaxWeight)
            //            : (product.tWeight >= row.MinWeight);

            //    case 2: // 数量限制
            //        return (row.MaxWeight > 0)
            //            ? (product.tQty >= row.MinWeight && product.tQty <= row.MaxWeight)
            //            : (product.tQty >= row.MinWeight);

            //    case 3: // 总价限制
            //        return (row.MaxWeight > 0)
            //            ? (product.Price >= row.MinWeight && product.Price <= row.MaxWeight)
            //            : (product.Price >= row.MinWeight);

            //    default:
            //        return true;
            //}
        }


        public static dynamic PackageInfo(
            dynamic existing,
            int goodsType,
            decimal weight,
            decimal tWeight,
            int ovId,
            int tId)
        {

            decimal WeightTwo = 0m;
            decimal tWeightTwo = 0m;

            // 检测空匿名对象
            bool isEmptyAnonymous = existing?.GetType().GetProperties().Length == 0;

            if (existing != null && !isEmptyAnonymous)
            {
                try
                {
                    WeightTwo = Convert.ToDecimal(existing.Weight);
                    tWeightTwo = Convert.ToDecimal(existing.tWeight);
                }
                catch (Microsoft.CSharp.RuntimeBinder.RuntimeBinderException)
                {
                    // 属性不存在时的处理逻辑
                }
            }
            return new
            {
                GoodsType = goodsType,
                Weight = WeightTwo + weight,
                tWeight = tWeightTwo + tWeight,
                OvId = ovId,
                TId = tId
            };
        }









        public class ShippingInfo
        {
            public decimal Weight { get; set; }
            public decimal Volume { get; set; }
            public decimal tWeight { get; set; }
            public decimal tVolume { get; set; }
            public decimal Price { get; set; }
            public int IsFreeShipping { get; set; }
            public object OvId { get; set; }
            public int tQty { get; set; }
            public int Qty { get; set; }
            public object IsCombination { get; set; }
            public List<int> IsFreeShippingAry { get; set; }
            public object TId { get; set; }
            public int GoodsType { get; set; }
        }





        // 辅助类
        public class ProductInfoTwo
        {
            public int ProId { get; set; }
            public int OvId { get; set; }
            public decimal Weight { get; set; }
            public decimal Volume { get; set; }
            public int Qty { get; set; }
            public decimal Price { get; set; }
            public decimal PropertyPrice { get; set; }
            public decimal CustomPrice { get; set; }
            public decimal Discount { get; set; }
            public int BuyType { get; set; }
            public bool IsFreeShipping { get; set; }
            public int TId { get; set; }
            public bool IsCombination { get; set; }
        }

        public class ShippingItemTwo
        {
            public decimal Weight { get; set; }
            public decimal Volume { get; set; }
            public decimal tWeight { get; set; }
            public decimal tVolume { get; set; }
            public int tQty { get; set; }
            public decimal Price { get; set; }
            public int IsFreeShipping { get; set; }
            public int OvId { get; set; }
            public List<int> IsFreeShippingAry { get; } = new List<int>();
            public int TId { get; set; }
            public int GoodsType { get; set; }
            public bool IsCombination { get; set; }
        }

        public class ShippingResult
        {
            public Dictionary<string, List<ShippingMethod>> Info { get; set; }
            public bool ShippingTemplate { get; set; }
        }

        public class ShippingMethod
        {
            public int SId { get; set; }
            public string Name { get; set; }
            public string Brief { get; set; }
            public bool IsAPI { get; set; }
            public string Type { get; set; }
            public decimal Weight { get; set; }
            public decimal ShippingPrice { get; set; }
        }

        // 扩展方法
        //public static class Extensions
        //{
        //    public static int ToInt(this object value, int defaultValue = 0)
        //    {
        //        return value != null ? Convert.ToInt32(value) : defaultValue;
        //    }

        //    public static T ToObject<T>(this object value)
        //    {
        //        return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(value));
        //    }

        //    public static JObject ToJObject(this object value)
        //    {
        //        return value != null ? JObject.FromObject(value) : new JObject();
        //    }
        //}


















        public void WriteOrderLog(orders_log log)
        {
            db.Insertable(log).ExecuteReturnIdentity();
            var order = db.Queryable<orders>().First(o => o.OrderId == log.OrderId);

            // 更新订单支付时间
            if (new[] { "paid", "pending" }.Contains(log.PaymentStatus) && order.PayTime == null)
            {
                if (order != null)
                {
                    order.PayTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    var b = db.Updateable(order).ExecuteCommandHasChange();
                }
            }
        }




        /// <summary>
        /// 分配会员
        /// </summary>
        /// <param name="data"></param>
        /// <exception cref="Exception"></exception>
        public void AssignMember(orders data, Dictionary<string, object> orderAddress)
        {
            //var c = _config.GetSection("Params").Get<AppParams>();
            //var lang = c.Manage.WebLang;
            var ip = "127.0.0.1";
            using (var _db = db.Ado.UseTran()) // 开启事务
            {
                try
                {
                    // 获取或创建用户
                    var user = GetOrCreateUser(data, ip);
                    var userId = user?.UserId ?? 0;

                    // 处理地址信息
                    HandleAddress(data, userId, orderAddress);

                    // 更新订单关联
                    db.Updateable<orders>()
                      .SetColumns(o => new orders { UserId = userId })
                      .Where(o => o.OId == data.OId)
                      .ExecuteCommand();

                    // 设置初始会员等级
                    if (userId > 0)
                    {
                        SetInitialUserLevel(userId);
                        //订单数量-付款订单数量
                        db.Updateable<user>()
                          .SetColumns(u => u.OrdersCount == u.OrdersCount + 1)
                          .SetColumns(u => u.PaymentOrderCount == u.PaymentOrderCount + 1)
                          .Where(u => u.UserId == userId)
                          .ExecuteCommand();

                    }

                    //// 推送CRM队列
                    //_queueService.Enqueue("pushUserToCrm", new { UserId = userId });

                    _db.CommitTran(); // 提交事务
                }
                catch (Exception ex)
                {
                    _db.RollbackTran(); // 回滚事务
                    throw new Exception("用户分配失败", ex);
                }
            }
        }
        private user GetOrCreateUser(orders data, string ip)
        {
            var existingUser = db.Queryable<user>()
                .Where(u => u.Email == data.Email)
                .First();

            if (existingUser == null && data.UserId == 0)
            {
                var newUser = new user
                {
                    Language = "en",
                    FirstName = data.ShippingFirstName,
                    LastName = data.ShippingLastName,
                    Email = data.Email,
                    Telephone = data.ShippingPhoneNumber,
                    RegTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    RegIp = ip,
                    LastLoginTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    LastLoginIp = ip,
                    LoginTimes = 1,
                    Status = 0,
                    IsLocked = false,
                    IsRegistered = false,
                    SalesId = data.SalesId,
                    LevelUpdateType = "auto",
                    DISTUId = "0,",
                    DISTDept = 1,
                    DISTBalance = 0,
                    DISTTotalBalance = 0,
                    Locked = false,
                    IsNewsletter = 0,
                    RefererId = 0,
                    RefererName = "",
                    IsTaxExempt = false,
                    MessageTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    ReviewStatus = "pending",
                    RejectReason = "",
                    IsFreeShipping = false,
                    Points = 0,
                    TotalPoints = 0,
                    OrdersCount = 1,
                    PaymentOrderCount = data.OrderStatus == 4 ? 1 : 0
                };

                db.Insertable(newUser).ExecuteReturnEntity();

                // 插入收货地址
                InsertAddress(newUser.UserId, data, isBilling: false);

                // 插入账单地址
                InsertAddress(newUser.UserId, data, isBilling: true);

                return newUser;
            }

            return existingUser;
        }

        private void InsertAddress(int userId, orders data, bool isBilling)
        {
            var address = new user_address_book
            {
                UserId = userId,
                FirstName = data.ShippingFirstName,
                LastName = data.ShippingLastName,
                AddressLine1 = data.ShippingAddressLine1,
                AddressLine2 = data.ShippingAddressLine2,
                City = data.ShippingCity,
                State = data.ShippingState,
                SId = data.ShippingSId,
                CId = data.ShippingCId,
                CodeOption = Convert.ToSByte(data.ShippingCodeOptionId),
                TaxCode = data.ShippingTaxCode,
                ZipCode = data.ShippingZipCode,
                CountryCode = Convert.ToInt32(data.ShippingCountryCode.Trim('+')),
                PhoneNumber = data.ShippingPhoneNumber,
                AdditionalInfoData = data.AdditionalInfoData,
                AdditionalInfoName = data.AdditionalInfoName,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                IsBillingAddress = isBilling
            };

            db.Insertable(address).ExecuteCommand();
        }

        private void HandleAddress(orders data, int userId, Dictionary<string, object> orderAddress)
        {
            int SaveAddress = int.TryParse(orderAddress["SaveAddress"]?.ToString(), out int saveAddress) ? saveAddress : 0;
            if (SaveAddress == 1)
            {
                int AddressId = int.TryParse(orderAddress["AddressId"]?.ToString(), out int addressId) ? addressId : 0;
                var address = db.Queryable<user_address_book>()
                    .Where(a => a.AId == AddressId)
                    .First();

                if (address == null)
                {
                    address = new user_address_book();
                }

                address.UserId = userId;
                address.FirstName = data.ShippingFirstName;
                address.LastName = data.ShippingLastName;
                address.AddressLine1 = data.ShippingAddressLine1;
                address.AddressLine2 = data.ShippingAddressLine2;
                address.City = data.ShippingCity;
                address.State = data.ShippingState;
                address.SId = data.ShippingSId;
                address.CId = data.ShippingCId;
                address.CodeOption = Convert.ToSByte(data.ShippingCodeOptionId);
                address.TaxCode = data.ShippingTaxCode;
                address.ZipCode = data.ShippingZipCode;
                address.CountryCode = Convert.ToInt32(data.ShippingCountryCode.Trim('+'));
                address.PhoneNumber = data.ShippingPhoneNumber;
                address.AdditionalInfoData = data.AdditionalInfoData;
                address.AdditionalInfoName = data.AdditionalInfoName;
                address.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                db.Insertable(address).ExecuteCommand();
            }
        }

        private void SetInitialUserLevel(int userId)
        {
            var lid = db.Queryable<user_level>()
                .Where(ul => ul.FullPrice <= 0)
                .OrderBy(ul => ul.LId, OrderByType.Asc)
                .Select(ul => ul.LId)
                .First();

            if (lid > 0)
            {
                db.Updateable<user>()
                  .SetColumns(u => new user { Level = lid })
                  .Where(u => u.UserId == userId)
                  .ExecuteCommand();
            }
        }

        /// <summary>
        /// 根据搜索key获取用户列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public async Task<List<user>> GetUserByEmail(string keyword)
        {

            var UserIdList = db.Queryable<user_address_book>()
                .Select(x => x.UserId)
                .Distinct()
                .ToList();
            var UserList = await db.Queryable<user>()
                .Where(x => UserIdList.Contains(x.UserId) && x.Email.Contains(keyword))
                .ToListAsync();

            return UserList;
        }




        /// <summary>
        /// 退款信息中变动信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<OrdersSuggestedRefundResponse> GetOrdersSuggestedRefund(OrderModelRefundRequest request)
        {
            var data = new OrdersSuggestedRefundResponse();
            var ret = 1;
            decimal itemCount = 0;

            // 获取订单基础数据
            var orderRow = db.Queryable<orders>()
                            .Where(o => o.OrderId == request.orderId)
                            .First();

            //if (orderRow == null)
            //{
            //    return NotFound();
            //}

            // 处理商品项合并
            var lineItemsAry = new Dictionary<int, int>();
            foreach (var v in request.lineItems)
            {
                if (v.quantity > 0)
                {

                    lineItemsAry[v.itemId] = lineItemsAry.ContainsKey(v.itemId)
                        ? lineItemsAry[v.itemId] + v.quantity
                        : v.quantity;
                    itemCount += v.quantity;
                }
            }

            // 计算订单详情价格
            var replaceData = new ReplaceDataModel
            {
                shippingPrice = request.shippingAmount,
                lineItems = lineItemsAry.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };

            var priceData = await OrdersDetailPrice(orderRow, 1, 0, false, true, replaceData);

            data.ProductPrice = priceData.ProductPrice;
            data.DiscountPrice = priceData.DiscountPrice;
            data.CouponPrice = priceData.CouponPrice;
            data.PointsPrice = priceData.PointsPrice;
            data.ShippingPrice = priceData.ShippingPrice;
            data.FeePrice = priceData.FeePrice;
            data.TaxPrice = priceData.TaxPrice;
            data.TotalPrice = priceData.TotalPrice;
            data.Currency = orderRow.Currency;
            //data.RefundTotalPrice = OrdersPrice(orderRow, 1, 0, false, true, replaceData);
            data.RefundTotalPrice = await OrdersPrice(orderRow, 1, 0, false);
            data.Tax = orderRow.Tax;
            data.itemCount = (decimal)itemCount;
            // 处理已退款金额
            var refundedTotal = db.Queryable<orders_refund_info>()
                .Where(r => r.OrderId == request.orderId && r.Status != "fail")
                .Sum(r => (decimal?)r.Amount) ?? 0;

            if (refundedTotal > 0)
            {
                // 可退款的总金额费用
                data.RefundTotalPrice = _helpsCartService.CeilPrice(data.RefundTotalPrice - refundedTotal);
            }

            if (data.TotalPrice > data.RefundTotalPrice)
            {
                data.TotalPrice = data.RefundTotalPrice;
            }

            if (request.shippingAmount <= 0 && data.ProductPrice <= 0)
            {
                ret = 0;
                data.TotalPrice = 0;
            }

            // 货币格式化处理
            foreach (var prop in data.GetType().GetProperties())
            {
                if (prop.Name == "Currency" || prop.Name == "Tax") continue;
                if (prop.PropertyType == typeof(decimal))
                {
                    var value = (decimal)prop.GetValue(data);
                    prop.SetValue(data, _helpsCartService.CurrencyFloatPrice(value, orderRow.Currency));
                }
            }

            return data;
        }



        /**
	 * 订单各种价格格式显示
	 * @return array				订单各种价格
	 * 								● ProductPrice 产品价格
	 * 								● DiscountPrice 订单折扣金额
	 * 								● CouponPrice 优惠券折扣金额
	 * 								● PointsPrice 积分
	 * 								● ShippingPrice 运费
	 * 								● FeePrice 手续费
	 * 								● TaxPrice 税费
	 * 								● TotalPrice 订单总价格
	 */
        /// <summary>
        /// 获取订单总价格--检查第一遍
        /// </summary>
        /// <param name="data">订单数据</param>
        /// <param name="method">显示格式 0 符号+价格，1 价格</param>
        /// <param name="isManage">后台货币 0 不计算 1 计算</param>
        /// <param name="isRefund">是否计算减去退款金额 true-计算 false-不计算</param>
        /// <param name="isDelete">是否计算减去已删除金额 true-计算 false-不计算</param>
        /// <param name="replaceData">替换参数 可传入:● shippingPrice 运费● lineItems 商品数据</param>
        /// <returns>订单总价格</returns>

        public async Task<(decimal ProductPrice, decimal DiscountPrice, decimal CouponPrice, decimal PointsPrice, decimal ShippingPrice,
            decimal FeePrice, decimal TaxPrice, decimal TotalPrice)>
            OrdersDetailPrice(orders data, int method, int isManage, bool isRefund = true, bool isDelete = true, ReplaceDataModel replaceData = null)
        {
            var currency = isManage == 1 ? data.ManageCurrency : data.Currency;
            var isRate = await CalculateRateStatus(data, isManage);

            string symbol = string.Empty;
            if (method == 0)
            {
                symbol = _helpsCartService.IconvPrice(0, 1, (isManage == 1 ? data.ManageCurrency : data.Currency));
            }

            ////产品总价
            //var itemReplace = new Dictionary<int, int>();
            //if (replaceData!=null&& replaceData.lineItems!=null)
            //{
            //    itemReplace = replaceData.lineItems;
            //}
            //var ProductPrice = OrdersProductPrice(data, 1, isManage, isRefund, isDelete, itemReplace);


            // 商品价格计算--产品总价
            var productPrice = Convert.ToDecimal(OrdersProductPrice(data, 1, isManage, isRefund, isDelete,
                replaceData?.lineItems?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)));

            // 折扣计算
            var discountPrice = CalculateDiscountPrice(productPrice, data.Discount!.Value, data.DiscountPrice!.Value,
                isManage, isRefund, isDelete, isRate, data, replaceData);

            // 优惠券计算
            var couponPrice = CalculateCouponPrice(productPrice, data.CouponDiscount!.Value, data.CouponPrice!.Value,
                isManage, isRefund, isDelete, isRate, data, replaceData);


            // 积分价格计算
            var pointsPrice = CalculatePointsPrice(productPrice, productPrice, data.PointsPrice,
                isManage, isRate, data, replaceData);

            // 运费计算
            var shippingPrice = CalculateShippingPrice(data.OrderId, isManage, isRefund, isRate,
                data, replaceData?.shippingPrice);

            // 税费计算
            var taxPrice = CalculateTaxPrice(productPrice, shippingPrice, Convert.ToBoolean(data.TaxType),
                data.Tax, currency);
            // 手续费计算
            var TotalPrice = await OrdersPrice(data, 0, isManage, isRefund, isDelete, replaceData);
            var feePrice = CalculateFeePrice(TotalPrice,
                data.PayAdditionalFee.Value, data.PayAdditionalAffix.Value, isManage, isRate, data, currency);

            // 总价格计算
            var totalPrice = await OrdersPrice(data, 1, isManage, isRefund, isDelete, replaceData);


            return (
                ProductPrice: _helpsCartService.CurrencyFloatPrice(productPrice, currency),
                DiscountPrice: _helpsCartService.CurrencyFloatPrice(discountPrice),
                CouponPrice: _helpsCartService.CurrencyFloatPrice(couponPrice),
                PointsPrice: _helpsCartService.CurrencyFloatPrice(pointsPrice),
                ShippingPrice: _helpsCartService.CurrencyFloatPrice(shippingPrice),
                FeePrice: _helpsCartService.CurrencyFloatPrice(feePrice),
                TaxPrice: _helpsCartService.CurrencyFloatPrice(taxPrice),
                TotalPrice: _helpsCartService.CurrencyFloatPrice(totalPrice)
            );
        }

        /// <summary>
        /// 实际付款金额
        /// </summary>
        /// <param name="data"></param>
        /// <param name="IsManage"></param>
        /// <returns></returns>
        public async Task<decimal> actualPayment(orders data, int IsManage = 1)
        {
            // 订单总价
            var ordersPrice = await OrdersPrice(data, 1, IsManage, false, true);
            //订单退款信息
            var ordersRefundRow = db.Queryable<orders_refund_info>()
                .Where(x => x.OrderId == data.OrderId)
                .ToList();
            //var refundedTotalPrice = 0;
            //foreach (var item in ordersRefundRow)
            //{
            //    item.Status == "success" && refundedTotalPrice += item.Amount;
            //}
            // 3. 计算成功退款总额
            decimal refundedTotalPrice = ordersRefundRow
                .Where(r => r.Status == "success")
                .Sum(r => (decimal)r.Amount);
            if (IsManage == 1)
            {
                refundedTotalPrice = _helpsCartService.CurrencyFloatPrice(_helpsCartService.CeilPrice(refundedTotalPrice / (data.Rate > 0 ? data.Rate : 1)), data.ManageCurrency);
            }
            else
            {
                refundedTotalPrice = _helpsCartService.CurrencyFloatPrice(refundedTotalPrice, data.Currency);
            }
            // 实际付款 = 订单总价 - 退款金额
            var totalPrice = ordersPrice - refundedTotalPrice;
            if (totalPrice < 0)
            {
                totalPrice = 0;
            }
            return totalPrice;
            //return _helpsCartService.CurrencyFloatPrice(totalPrice, data.Currency); 
        }





        /**
	 * 获取订单总价格
	 *
	 * @param array $Data			订单数据
	 * @param integer $IsFee		计算手续费 0 不计算 1 计算
	 * @param integer $IsManage		后台货币 0 不计算 1 计算
	 * @param boolean $IsRefund		是否计算减去退款金额 true-计算 false-不计算
	 * @param boolean $IsDelete		是否计算减去已删除金额 true-计算 false-不计算
	 * @param array $replaceData	替换参数 可传入:
	 * 								● shippingPrice 运费
	 * 								● lineItems 商品数据
	 * @return float				订单总价格
	 */
        /// <summary>
        /// 获取订单总价格
        /// </summary>
        /// <param name="data">订单数据</param>
        /// <param name="isFee">计算手续费 0 不计算 1 计算</param>
        /// <param name="isManage">后台货币 0 不计算 1 计算</param>
        /// <param name="isRefund">是否计算减去退款金额 true-计算 false-不计算</param>
        /// <param name="isDelete">是否计算减去已删除金额 true-计算 false-不计算</param>
        /// <param name="replaceData">替换参数 可传入:● shippingPrice 运费● lineItems 商品数据</param>
        /// <returns>订单总价格</returns>
        public async Task<decimal> OrdersPrice(
            orders data, int isFee, int isManage, bool isRefund = true, bool isDelete = true,
            ReplaceDataModel replaceData = null)
        {
            var currency = isManage == 1 ? data.ManageCurrency : data.Currency;
            var isRate = await CalculateRateStatus(data, isManage);
            // 商品价格计算
            var productPrice = Convert.ToDecimal(OrdersProductPrice(data, 1, isManage, isRefund, isDelete,
                replaceData?.lineItems?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)));
            // 折扣计算
            var discountPrice = CalculateDiscountPrice(productPrice, data.Discount!.Value,
                data.DiscountPrice!.Value, isManage, isRefund, isDelete, isRate, data, replaceData);
            // 优惠券计算
            var couponPrice = CalculateCouponPrice(productPrice, data.CouponDiscount!.Value,
                data.CouponPrice!.Value, isManage, isRefund, isDelete, isRate, data, replaceData);
            // 运费计算
            var shippingPrice = CalculateShippingPrice(data.OrderId, isManage, isRefund, isRate,
                data, replaceData?.shippingPrice);
            // 税费计算
            var taxPrice = CalculateTaxPrice(productPrice, shippingPrice, Convert.ToBoolean(data.TaxType),
                data.Tax, currency);
            //产品总价
            var ProductTotalPrice = OrdersProductPrice(data, 1, isManage, isRefund, isDelete);
            // 积分价格计算
            var pointsPrice = CalculatePointsPrice(productPrice, Convert.ToDecimal(ProductTotalPrice), data.PointsPrice,
                isManage, isRate, data, replaceData);

            // 价格计算
            var productTotal = productPrice - discountPrice - couponPrice;
            var shipInsTotal = shippingPrice;
            var taxTotal = taxPrice;
            var addPrice = 0m;
            if (isManage == 1)
            {
                addPrice = data.CouponPrice!.Value + data.DiscountPrice!.Value;
            }
            else
            {
                if (isRate == 1)
                {
                    addPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.CouponPrice!.Value, 2, data.Currency, data.Rate)) +
                   Convert.ToDecimal(_helpsCartService.IconvPrice(data.DiscountPrice!.Value, 2, data.Currency, data.Rate));
                }
                else
                {
                    addPrice = _helpsCartService.CurrencyFloatPrice(data.CouponPrice!.Value + data.DiscountPrice!.Value, data.Currency);

                    //  addPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.CouponPrice!.Value, 2, data.Currency)) +
                    //Convert.ToDecimal(_helpsCartService.IconvPrice(data.DiscountPrice!.Value, 2, data.Currency));
                }
            }

            productPrice = _helpsCartService.CurrencyFloatPrice(productPrice, currency);
            shippingPrice = _helpsCartService.CurrencyFloatPrice(shippingPrice, currency);
            taxPrice = _helpsCartService.CurrencyFloatPrice(taxPrice, currency);
            addPrice = _helpsCartService.CurrencyFloatPrice(addPrice, currency);
            pointsPrice = _helpsCartService.CurrencyFloatPrice(pointsPrice, currency);
            var totalPrice = productPrice + shippingPrice + taxPrice - addPrice - pointsPrice;

            // 手续费计算
            if (isFee == 1)
            {
                //加上付款手续费
                var PayAdditionalAffix = 0m;
                if (isManage == 1)
                {
                    PayAdditionalAffix = data.PayAdditionalAffix!.Value;
                    PayAdditionalAffix = _helpsCartService.CurrencyFloatPrice(PayAdditionalAffix, currency);
                    totalPrice = _helpsCartService.CeilPrice(totalPrice * (1 + data.PayAdditionalFee!.Value / 100)) + PayAdditionalAffix;
                }
                else
                {
                    if (isRate == 1)
                    {
                        PayAdditionalAffix = Convert.ToDecimal(_helpsCartService.IconvPrice(data.PayAdditionalAffix!.Value, 2, data.Currency, data.Rate));
                    }
                    else
                    {
                        PayAdditionalAffix = _helpsCartService.CurrencyFloatPrice(data.PayAdditionalAffix!.Value, data.Currency);
                        //PayAdditionalAffix = Convert.ToDecimal(_helpsCartService.IconvPrice(data.PayAdditionalAffix!.Value, 2, data.Currency));
                    }
                    PayAdditionalAffix = _helpsCartService.CurrencyFloatPrice(PayAdditionalAffix, currency);
                    totalPrice = _helpsCartService.CeilPrice(totalPrice * (1 + data.PayAdditionalFee!.Value / 100)) + PayAdditionalAffix;
                }

            }

            totalPrice = _helpsCartService.CurrencyFloatPrice(totalPrice, currency);

            return totalPrice;
        }

        #region 获取订单产品总价格--格式显示
        /// <summary>
        /// 获取订单产品总价格格式显示
        /// </summary>
        /// <param name="data">订单数据</param>
        /// <param name="Method">显示格式 0 符号+价格 1 价格</param>
        /// <param name="isManage">后台货币 0 不计算 1 计算</param>
        /// <param name="isRefund">是否计算减去退款金额 true-计算 false-不计算</param>
        /// <param name="isDelete">是否计算减去已删除金额 true-计算 false-不计算</param>
        /// <param name="replaceItems">替换参数 可传入:● itemId 商品ID ● quantity 商品数量</param>
        /// <returns>订单产品总价格</returns>
        public string OrdersProductPrice(orders data, int method = 0, int isManage = 0, bool isRefund = true, bool isDelete = true,
            Dictionary<int, int> replaceData = null)
        {
            decimal total = 0;
            string symbol = _helpsCartService.IconvPrice(0, 1,
                isManage == 1 ? data.ManageCurrency : data.Currency);
            bool isRate = false;

            if (data.Currency != data.ManageCurrency && data.Rate > 0)
            {
                isRate = true;
            }
            //var manageSession = _helpsCartService.GetManageDefaultCurrency();
            //if (data.ManageCurrency != manageSession.Currency)
            //{
            //    isRate = true;
            //}

            var orderData = JudgeOrder(data.OId);
            // 退款信息处理
            var refundInfo = db.Queryable<orders_refund_info>()
                .Where(r => r.OrderId == data.OrderId && r.Status == "success")
                .ToList();
            var refundSuccessIds = refundInfo.Select(r => r.ReId).ToList();

            // 删除产品处理
            var deleteProducts = db.Queryable<orders_products_delete_list>()
                .Where(d => d.OrderId == data.OrderId && d.ShippingStatus == "unshipped")
                .ToList();
            // 订单已删除产品（仅限于未发货状态）
            var (refundProducts, deleteProductsList) = ProcessDeleteProducts(deleteProducts, refundSuccessIds);

            // 包裹产品数量信息
            var packageInfo = GetPackageInfo(data.OrderId, orderData.Type);
            // 订单产品
            var products = GetOrderProducts(data.OrderId, orderData.OrdersProTable);

            foreach (var item in products)
            {
                var qty = 0;
                var price = (item.Price + item.PropertyPrice + item.CustomPrice) * (item.Discount < 100 ? item.Discount / 100 : 1);
                if (packageInfo != null && packageInfo.Count > 0)
                {
                    try
                    {
                        qty = packageInfo.GetValueOrDefault(item.LId, 0);

                        //qty = packageInfo[item.LId] != null ? packageInfo[item.LId] : 0;
                    }
                    catch (Exception e)
                    {

                        throw;
                    }

                }
                else
                {
                    qty = item.Qty;
                }
                if (data.OrderStatus != 7)
                {
                    if (isRefund == false && refundProducts.Count > 0 && refundProducts.ContainsKey(item.LId))
                    {
                        qty += Convert.ToInt32(refundProducts[item.LId]);
                    }
                    if (isDelete == false && deleteProductsList.Count > 0 && deleteProductsList.ContainsKey(item.LId))
                    {
                        qty += Convert.ToInt32(deleteProductsList[item.LId]);
                    }
                }

                if (replaceData != null && replaceData.Count > 0)
                {
                    if (replaceData.ContainsKey(Convert.ToInt32(item.LId)))
                    {
                        qty = Convert.ToInt32(replaceData[Convert.ToInt32(item.LId)]);
                    }
                    else
                    {
                        qty = 0;
                    }
                }


                if (isManage == 1)
                {
                    total += _helpsCartService.CurrencyFloatPrice(_helpsCartService.CeilPrice(price), data.Currency) * qty;
                }
                else
                {
                    var sPrice = 0m;
                    if (isRate)
                    {
                        sPrice = _helpsCartService.CurrencyFloatPrice(Convert.ToDecimal(_helpsCartService.IconvPrice(price, 2, data.Currency, data.Rate)), data.Currency);
                    }
                    else
                    {
                        sPrice = _helpsCartService.CurrencyFloatPrice(price, data.Currency);
                    }

                    total += sPrice * qty;
                }
            }

            total = _helpsCartService.CeilPrice(total);
            return method == 0 ? $"{symbol}{total}" : total.ToString();
        }
        private int GetProductQuantity(
    string lid,
    Dictionary<string, int> packageInfo,
    Dictionary<string, int> refundProducts,
    Dictionary<string, int> deleteProducts,
    int orderStatus,
    bool isRefund,
    bool isDelete,
    Dictionary<int, int> replaceData)
        {

            int qty = packageInfo.ContainsKey(lid) ? packageInfo[lid] : 0;

            if (orderStatus != 7)
            {
                if (!isRefund && refundProducts.ContainsKey(lid))
                {
                    qty += refundProducts[lid];
                }
                if (!isDelete && deleteProducts.ContainsKey(lid))
                {
                    qty += deleteProducts[lid];
                }
            }

            if (replaceData != null && replaceData.ContainsKey(Convert.ToInt32(lid)))
            {
                qty = Convert.ToInt32(replaceData[Convert.ToInt32(lid)]);
            }

            return qty;
        }
        private decimal CalculatePrice(OrderProductTwo product)
        {
            return (product.Price + product.PropertyPrice + product.CustomPrice) *
                   (product.Discount < 100 ? product.Discount / 100m : 1);
        }
        private List<OrderProductTwo> GetOrderProducts(int orderId, string ordersPro)
        {
            return ordersPro == "orders_products_list"
                ? db.Queryable<orders_products_list>()
                    .Where(p => p.OrderId == orderId)
                    .Select(p => new OrderProductTwo
                    {
                        Price = p.Price!.Value,
                        PropertyPrice = p.PropertyPrice!.Value,
                        CustomPrice = p.CustomPrice,
                        Discount = p.Discount!.Value,
                        Qty = Convert.ToInt32(p.Qty),
                        LId = p.LId.ToString()
                    }).ToList()
                : db.Queryable<temp_orders_products_list>()
                    .Where(p => p.OrderId == orderId)
                    .Select(p => new OrderProductTwo
                    {
                        Price = p.Price!.Value,
                        PropertyPrice = p.PropertyPrice!.Value,
                        CustomPrice = p.CustomPrice,
                        Discount = p.Discount!.Value,
                        Qty = Convert.ToInt32(p.Qty),
                        LId = p.LId.ToString()
                    }).ToList();
        }
        public class OrderProductTwo
        {
            public decimal Price { get; set; }
            public decimal PropertyPrice { get; set; }
            public decimal CustomPrice { get; set; }
            public decimal Discount { get; set; }
            public int Qty { get; set; }
            public string LId { get; set; }
        }
        // 包裹产品数量信息
        private Dictionary<string, int> GetPackageInfo(int orderId, string orderType)
        {
            if (orderType != "temp")
            {
                var packages = db.Queryable<orders_package>()
                    .Where(p => p.OrderId == orderId && p.ParentId == 0 && p.ProInfo != "[]")
                    .ToList();

                var packageInfo = new Dictionary<string, int>();
                foreach (var pkg in packages)
                {
                    var proInfo = JsonConvert.DeserializeObject<Dictionary<string, int>>(pkg.ProInfo);
                    foreach (var kvp in proInfo)
                    {
                        packageInfo[kvp.Key] = packageInfo.GetValueOrDefault(kvp.Key, 0) + kvp.Value;
                    }
                }
                return packageInfo;
            }
            return new Dictionary<string, int>();
        }
        private (Dictionary<string, int>, Dictionary<string, int>) ProcessDeleteProducts(
    List<orders_products_delete_list> deleteProducts,
    List<int> refundSuccessIds)
        {
            var refundProducts = new Dictionary<string, int>();
            var deleteProductsList = new Dictionary<string, int>();

            foreach (var item in deleteProducts)
            {
                var proInfo = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                foreach (var kvp in proInfo)
                {
                    if (item.RefundId > 0 && refundSuccessIds.Contains(item.RefundId))
                    {
                        refundProducts[kvp.Key] = refundProducts.GetValueOrDefault(kvp.Key, 0) + kvp.Value;
                    }
                    else
                    {
                        deleteProductsList[kvp.Key] = deleteProductsList.GetValueOrDefault(kvp.Key, 0) + kvp.Value;
                    }
                }
            }

            return (refundProducts, deleteProductsList);
        }

        #endregion



        #region 辅助计算方法
        /// <summary>
        /// 折扣计算
        /// </summary>
        /// <param name="productPrice">产品总价</param>
        /// <param name="discount">折扣</param>
        /// <param name="discountPrice">限时促销(折扣)</param>
        /// <param name="isManage">后台货币 0 不计算 1 计算</param>
        /// <param name="isRate">订单固定的货币汇率  0 不计算 1 计算</param>
        /// <param name="data">订单数据</param>
        /// <param name="replaceData">替换参数 可传入:● shippingPrice 运费● lineItems 商品数据</param>
        /// <returns></returns>
        private decimal CalculateDiscountPrice(decimal productPrice, decimal discount,
            decimal discountPrice, int isManage, bool isRefund, bool isDelete, int isRate, orders data,
            ReplaceDataModel replaceData)
        {
            var DiscountPrice = _helpsCartService.CeilPrice(productPrice * (1 - (100 - data.Discount!.Value) / 100));

            if (isManage == 1)
            {
                if (data.DiscountPrice > 0 && DiscountPrice == data.DiscountPrice)
                {
                    DiscountPrice = data.DiscountPrice!.Value;
                }
                else if (data.DiscountPrice > 0)
                {
                    DiscountPrice = data.DiscountPrice!.Value;
                }
            }
            else
            {
                if (data.DiscountPrice > 0)
                {
                    if (isRate == 1)
                    {
                        DiscountPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.DiscountPrice!.Value, 2, data.Currency, data.Rate));
                    }
                    else
                    {
                        DiscountPrice = _helpsCartService.CurrencyFloatPrice(data.DiscountPrice!.Value, data.Currency);

                    }
                    if (replaceData?.lineItems != null)
                    {
                        var ProductTotalPrice = Convert.ToDecimal(OrdersProductPrice(data, 1, isManage, isRefund, isDelete));
                        var Discount = DiscountPrice / ProductTotalPrice; // 折扣 Custom discount
                        DiscountPrice = _helpsCartService.CeilPrice(productPrice * Discount);
                    }
                }
            }
            return DiscountPrice;
        }
        /// <summary>
        /// 优惠券计算
        /// </summary>
        /// <param name="productPrice"></param>
        /// <param name="couponDiscount"></param>
        /// <param name="couponPrice"></param>
        /// <param name="isManage"></param>
        /// <param name="isRate"></param>
        /// <param name="data"></param>
        /// <param name="replaceData"></param>
        /// <returns></returns>
        private decimal CalculateCouponPrice(decimal productPrice, decimal couponDiscount,
            decimal couponPrice, int isManage, bool isRefund, bool isDelete, int isRate, orders data,
            ReplaceDataModel replaceData)
        {
            var CouponPrice = _helpsCartService.CeilPrice(productPrice * data.CouponDiscount!.Value);// 优惠券(折扣)
            // 优惠券(价格)
            if (data.CouponPrice > 0)
            {
                if (isManage == 1)
                {
                    CouponPrice = data.CouponPrice!.Value;
                }
                else
                {
                    if (isRate == 1)
                    {
                        CouponPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.CouponPrice!.Value, 2, data.Currency, data.Rate));
                    }
                    else
                    {
                        CouponPrice = _helpsCartService.CurrencyFloatPrice(data.CouponPrice!.Value, data.Currency);
                        //CouponPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.CouponPrice!.Value, 2, data.Currency));
                    }
                    if (replaceData?.lineItems != null)
                    {
                        var ProductTotalPrice = Convert.ToDecimal(OrdersProductPrice(data, 1, isManage, isRefund, isDelete));
                        var Discount = CouponPrice / ProductTotalPrice;
                        CouponPrice = _helpsCartService.CeilPrice(productPrice * Discount);
                    }


                }
            }
            return CouponPrice;
        }
        /// <summary>
        /// 积分价格计算
        /// </summary>
        /// <param name="productPrice"></param>
        /// <param name="pointsPrice"></param>
        /// <param name="isManage"></param>
        /// <param name="isRate"></param>
        /// <param name="data"></param>
        /// <param name="replaceData"></param>
        /// <returns></returns>
        private decimal CalculatePointsPrice(decimal productPrice, decimal ProductTotalPrice, decimal pointsPrice,
            int isManage, int isRate, orders data, ReplaceDataModel replaceData)
        {
            var PointsPrice = 0m;
            if (isManage == 1)
            {
                PointsPrice = data.PointsPrice;
            }
            else
            {
                if (isRate == 1)
                {
                    PointsPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.PointsPrice, 2, data.Currency, data.Rate));
                }
                else
                {
                    PointsPrice = _helpsCartService.CurrencyFloatPrice(data.PointsPrice, data.Currency);
                    //PointsPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(data.PointsPrice, 2, data.Currency));
                }
                if (PointsPrice > 0 && replaceData?.lineItems != null)
                {
                    PointsPrice = _helpsCartService.CeilPrice(productPrice * (PointsPrice / ProductTotalPrice)); // 限时促销(折扣)
                }

            }

            return PointsPrice;
        }
        /// <summary>
        /// 封装运费计算逻辑（核心方法）
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <param name="isManage">是否后台管理模式（影响货币处理）</param>
        /// <param name="isRate">是否需要汇率转换</param>
        /// <param name="data">订单完整数据</param>
        /// <param name="replaceShippingPrice">替换运费价格（优先使用）</param>
        /// <returns>格式化后的运费金额</returns>
        private decimal CalculateShippingPrice(int orderId, int isManage, bool isRefund, int isRate, orders data, decimal? replaceShippingPrice)
        {
            var shippingPrice = 0m;
            // 获取订单包裹信息
            var packages = GetOrdersPackage(orderId, data);

            foreach (var package in packages.Parent)
            {
                if (isManage == 1)
                {
                    shippingPrice += package.ShippingPrice.Value;
                }
                else
                {
                    if (isRate == 1)
                    {
                        shippingPrice += Convert.ToDecimal(_helpsCartService.IconvPrice(package.ShippingPrice.Value, 2, data.Currency, data.Rate));
                    }
                    else
                    {
                        shippingPrice = _helpsCartService.CurrencyFloatPrice(package.ShippingPrice.Value, data.Currency);
                        //shippingPrice += Convert.ToDecimal(_helpsCartService.IconvPrice(package.ShippingPrice.Value, 2, data.Currency));
                    }
                }
            }
            // 处理退款
            if (isRefund)
            {
                var refundAmount = db.Queryable<orders_refund_info>()
                    .Where(r => r.OrderId == orderId &&
                               r.Status == "success" &&
                               r.ShippingAmount > 0)
                    .Sum(r => (decimal?)r.ShippingAmount) ?? 0;

                shippingPrice -= refundAmount;
            }

            // 应用替换运费
            if (replaceShippingPrice != null && replaceShippingPrice.Value >= 0)
            {
                shippingPrice = replaceShippingPrice.Value;
            }
            return shippingPrice;
        }



        /// <summary>
        /// 税费计算
        /// </summary>
        /// <param name="productPrice"></param>
        /// <param name="shippingPrice"></param>
        /// <param name="taxType"></param>
        /// <param name="taxRate"></param>
        /// <param name="currency"></param>
        /// <returns></returns>
        private decimal CalculateTaxPrice(decimal productPrice, decimal shippingPrice,
            bool taxType, decimal taxRate, string currency)
        {
            var taxBase = taxType ? productPrice + shippingPrice : productPrice;
            return _helpsCartService.CeilPrice(taxBase * taxRate);
        }
        /// <summary>
        /// 手续费计算
        /// </summary>
        /// <param name="TotalPrice">总价</param>
        /// <param name="payAdditionalFee"></param>
        /// <param name="payAdditionalAffix"></param>
        /// <param name="isManage"></param>
        /// <param name="isRate"></param>
        /// <param name="data"></param>
        /// <param name="currency"></param>
        /// <returns></returns>
        private decimal CalculateFeePrice(decimal TotalPrice, decimal payAdditionalFee, decimal payAdditionalAffix,
            int isManage, int isRate, orders data, string currency)
        {
            var feePrice = _helpsCartService.CeilPrice(TotalPrice * (payAdditionalFee / 100));
            feePrice = _helpsCartService.CurrencyFloatPrice(feePrice, currency);

            if (data.PayAdditionalAffix > 0)
            {
                if (isManage == 1)
                {
                    feePrice += payAdditionalAffix;
                }
                else
                {
                    var affixPrice = 0m;
                    if (isRate == 1)
                    {
                        affixPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(payAdditionalAffix, 2, data.Currency, data.Rate));
                    }
                    else
                    {
                        //affixPrice = Convert.ToDecimal(_helpsCartService.IconvPrice(payAdditionalAffix, 2, data.Currency));

                        affixPrice = _helpsCartService.CurrencyFloatPrice(payAdditionalAffix, data.Currency);
                    }
                    affixPrice = _helpsCartService.CurrencyFloatPrice(affixPrice, currency); // 兼容取整货币的计算
                    feePrice += affixPrice;
                }
            }

            return feePrice;
        }



        private async Task<int> CalculateRateStatus(orders data, int isManage)
        {
            var isRate = 0;
            if (data.Currency != data.ManageCurrency && data.Rate > 0)
            {
                // 使用订单固定的货币汇率
                isRate = 1;
            }

            var manageSessionCurrency = await _currencyService.GetManageDefaultCurrency();

            if (data.ManageCurrency != manageSessionCurrency.Currency)
            {
                // 跟现在后台货币不一致
                isRate = 1;
            }

            return isRate;
        }

        #endregion



        /// <summary>
        /// 操作退款
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<string> AddRefund(OrderModelRefundRequest request)
        {
            try
            {
                var reason = "";
                // 参数验证
                if (request.totalAmount <= 0)
                    return reason = "退款金额必须大于0";

                // 检查正在进行的退款
                var processingRefund = db.Queryable<orders_refund_info>()
                    .Any(r => r.OrderId == request.orderId && r.Status == "begin");
                if (processingRefund)
                    return reason = "存在进行中的退款操作";

                // 获取订单信息
                var order = db.Queryable<orders>()
                    //.Includes(o => o.Payment)
                    .First(o => o.OrderId == request.orderId);

                var paymentRow = db.Queryable<payment>()
                  .Where(x => x.PId == order.PId)
                  .First();

                if (order == null)
                    return reason = "订单不存在";

                // 构建退款数据
                var refundData = new orders_refund_info
                {
                    OrderId = request.orderId,
                    Number = order.PaymentId.ToString(),
                    RefundMethod = request.refundType == "original"
                        ? paymentRow?.Name_en
                        : "",
                    Reason = request.Reason,
                    Amount = request.totalAmount,
                    ShippingAmount = request.shippingAmount,
                    Currency = order.Currency,
                    Status = "begin",
                    UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    RefundId = "",
                    RefundDetail = ""
                };

                // 处理无交易号退款
                if (string.IsNullOrEmpty(order.PaymentId.ToString()) && paymentRow?.SystemType == "system")
                {
                    refundData.Status = "success";
                }
                // 退款类型判断
                //var orderTotal = await OrdersPrice(order, 1, 0, false);
                // 处理已退款金额
                //var refundedTotalTwo = db.Queryable<orders_refund_info>()
                //    .Where(r => r.OrderId == request.orderId && r.Status != "fail")
                //    .Sum(r => (decimal?)r.Amount) ?? 0;

                //if (refundedTotalTwo > 0)
                //{
                //    // 可退款的总金额费用
                //    orderTotal = _helpsCartService.CeilPrice(orderTotal - refundedTotalTwo);
                //}

                // 保存退款记录
                var refundId = db.Insertable(refundData).ExecuteReturnIdentity();

                var deleteProducts = new List<int>();
                // 处理退货产品
                if (request.lineItems != null && request.lineItems.Count > 0)
                {
                    foreach (var item in request.lineItems)
                    {
                        if (item.quantity == 0) continue;

                        var deleteData = new orders_products_delete_list
                        {
                            OrderId = request.orderId,
                            RefundId = (int)refundId,
                            PackageId = item.packageId,
                            ProInfo = JsonConvert.SerializeObject(new Dictionary<int, int> { { item.itemId, item.quantity } }),
                            ShippingStatus = item.status,
                            RestockType = item.type ? "return" : "no-restock",
                            AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                        };

                        var deleteId = db.Insertable(deleteData).ExecuteReturnIdentity();
                        deleteProducts.Add(deleteId);
                    }
                }
                decimal itemCount = 0;
                // 处理商品项合并
                var lineItemsAry = new Dictionary<int, int>();
                foreach (var v in request.lineItems)
                {
                    if (v.quantity > 0)
                    {

                        lineItemsAry[v.itemId] = lineItemsAry.ContainsKey(v.itemId)
                            ? lineItemsAry[v.itemId] + v.quantity
                            : v.quantity;
                        itemCount += v.quantity;
                    }
                }

                // 计算订单详情价格
                var replaceData = new ReplaceDataModel
                {
                    shippingPrice = request.shippingAmount,
                    lineItems = lineItemsAry.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                };
                // 退款类型判断
                
                var orderTotal = await OrdersPrice(order, 1, 0, false, true);
                //var orderTotal = CalculateOrderTotal(order);
                var refundedTotal = db.Queryable<orders_refund_info>()
                    .Where(r => r.OrderId == request.orderId && r.Status != "fail")
                    .Sum(r => r.Amount);

                var refundTypeFlag = refundedTotal > 0 && refundedTotal < orderTotal ? 0 : 1;

                // 支付接口调用
                if (request.refundType == "original" && !string.IsNullOrEmpty(order.PaymentId.ToString()))
                {
                    var refundOriginal = await CallPaymentGatewayAsync(order, refundData, refundTypeFlag);
                    if (refundOriginal.status)
                    {
                        db.Updateable<orders_refund_info>()
                         .SetColumns(x => new orders_refund_info { Status = "success" })
                         //.SetColumns(x => new orders_refund_info { Reason = refundOriginal.msg })
                         .Where(x => x.ReId == refundId)
                         .ExecuteCommand();
                    }
                    else
                    {
                        db.Updateable<orders_refund_info>()
                        .SetColumns(x => new orders_refund_info { Status = "fail" })
                        .Where(x => x.ReId == refundId)
                        .ExecuteCommand();
                    }
                }
                if (request.refundType == "offline")
                {
                    db.Updateable<orders_refund_info>()
                      .SetColumns(x => new orders_refund_info { Status = "success" })
                      .Where(x => x.ReId == refundId)
                      .ExecuteCommand();
                }
                var isDelete = 1; // 是否允许删除产品
                // 更新退款状态
                var refundRecord = db.Queryable<orders_refund_info>()
                    .First(r => r.ReId == refundId);
                // API返回结果后，重新获取数据
                if (refundRecord.Status == "begin")
                {
                    db.Updateable<orders_refund_info>()
                      .SetColumns(x => new orders_refund_info { Status = "pending" })
                      .Where(x => x.ReId == refundId)
                      .ExecuteCommand();
                    isDelete = 0;
                }
                if (refundRecord.Status == "fail")
                {
                    db.Deleteable<orders_products_delete_list>()
                        .Where(d => d.RefundId == refundId)
                        .ExecuteCommand();
                    isDelete = 0;
                }
                db.Updateable<orders>()
                      .SetColumns(x => new orders { UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) })
                      .Where(x => x.OrderId == request.orderId)
                      .ExecuteCommand();

                if (isDelete == 1)
                {
                    UpdateRefundSuccess(request.orderId, deleteProducts, refundTypeFlag, order.OrderStatus, 0, "refund", refundId);
                }

                //    $data = array(
                //    'proData' 		=>	$proData,
                //    'totalAmount'	=>	$totalAmount,
                //    'currency'		=>	$orderRow['Currency'],
                //    'paymentMethod'	=>	$paymentRow["Name_en"],
                //    'refundStatus'	=>	$refundRow['Status'],
                //    'refundType'	=>	$actionRefundType,
                //    'refundNo'		=>	$refundResultData['refundNo'] ?? '',

                //);

                var paidData = new Dictionary<string, object>
                {
                    { "proData", itemCount },
                    { "totalAmount", request.totalAmount },
                    { "currency", order.Currency },
                    { "paymentMethod", paymentRow.Name_en },
                    { "refundStatus", refundData.Status },
                    { "refundType", request.refundType },
                    { "refundNo", refundData.RefundId }
                };

                // 记录订单日志
                await _helpsWriteOrderLogService.GetWriteOrderLogData((refundRecord.Status == "fail" ? "refundFail" : "refund"), paidData);
                //// 推送CRM队列
                //await PushToCrmQueueAsync(order.OrderId);

                // 返回结果

                if (refundRecord.Status == "success")
                {
                    reason = "退款成功";

                }
                else if (refundRecord.Status == "pending")
                {
                    reason = "处理中";
                }
                else
                {
                    reason = "退款失败";
                }
                return reason;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "退款处理异常");
                return "系统错误";
            }
        }

        public void UpdateRefundSuccess(
       int orderId,
       List<int> deleteProData,
       int refundType,
       int passOrderStatus,
       int isCancel = 0,
       string entrance = "cancel", int refundId = 0)
        {
            // 初始化参数

            var orderRow = db.Queryable<orders>().First(o => o.OrderId == orderId);

            // 删除包裹产品数据
            var dataAry = new Dictionary<string, Dictionary<int, dynamic>>();
            if (deleteProData?.Any() == true)
            {
                var deleteRows = db.Queryable<orders_products_delete_list>()
                    .Where(d => deleteProData.Contains(d.DeleteId))
                    .ToList();

                foreach (var row in deleteRows)
                {
                    var proInfo = JsonConvert.DeserializeObject<Dictionary<int, int>>(row.ProInfo);
                    foreach (var kvp in proInfo)
                    {
                        if (!dataAry.ContainsKey(row.ShippingStatus))
                            dataAry[row.ShippingStatus] = new Dictionary<int, dynamic>();

                        if (!dataAry[row.ShippingStatus].ContainsKey(row.PackageId))
                            dataAry[row.ShippingStatus][row.PackageId] = new { proInfo = new Dictionary<int, int>(), type = row.RestockType };

                        dataAry[row.ShippingStatus][row.PackageId].proInfo[kvp.Key] = kvp.Value;
                    }
                }
            }

            // 处理包裹数据
            var returnProducts = new Dictionary<int, int>();
            var ordersPackage = GetOrdersPackage(orderId);
            //var packageData = ordersPackage.Valid.SelectMany(v => v).ToDictionary(p => p.WId);
            var packageData = ordersPackage.Valid
                .SelectMany(v => v.Value)  // 显式访问Value
                .Where(p => p.WId != 0)   // 过滤无效WId
                .GroupBy(p => p.WId)       // 处理重复键
                .ToDictionary(
                    g => g.Key,
                    g => g.First()        // 取第一个或根据需求处理
                );
            foreach (var statusGroup in dataAry)
            {
                foreach (var packageGroup in statusGroup.Value)
                {
                    if (!packageData.TryGetValue(packageGroup.Key, out var package)) continue;

                    //var packageProInfo = JsonConvert.DeserializeObject<Dictionary<int, int>>(package.ProInfo);
                    // 1. 显式指定反序列化类型
                    var packageProInfo = JsonConvert.DeserializeObject<Dictionary<int, int>>(package.ProInfo)
                                         ?? new Dictionary<int, int>();
                    var totalQty = 0;

                    foreach (var kvp in packageProInfo.ToList())
                    {
                        if (packageGroup.Value.proInfo is Dictionary<int, int> validProInfo &&
               validProInfo.TryGetValue(kvp.Key, out int refundQty)) // 明确指定int类型
                        {
                            var newQty = kvp.Value - refundQty;
                            if (newQty < 1)
                                packageProInfo.Remove(kvp.Key);
                            else
                                packageProInfo[kvp.Key] = newQty;

                            totalQty += newQty;
                        }
                        else
                        {
                            totalQty += kvp.Value;
                        }
                    }

                    //var proJson = JsonConvert.SerializeObject(packageProInfo);
                    var proJson = packageProInfo.Count == 0 ? "[]" : JsonConvert.SerializeObject(packageProInfo);
                    if (package.Status == 0)
                    {
                        db.Updateable<orders_package>()
                            .SetColumns(p => new orders_package { ProInfo = proJson })
                            .Where(p => p.WId == packageGroup.Key)
                            .ExecuteCommand();
                        if (true)
                        {

                        }
                    }

                    if (statusGroup.Key == "UnShipped" && packageGroup.Value.type == "return")
                    {
                        foreach (var kvp in packageGroup.Value.proInfo)
                        {
                            returnProducts[kvp.Key] = returnProducts.TryGetValue(kvp.Key, out int val) ? val + kvp.Value : kvp.Value;
                        }
                    }
                }
            }

            // 重新入库处理
            if (returnProducts.Any())
            {
                var productIds = ordersPackage.ProInfo.Select(p => p.LId).Distinct().ToList();
                var proStocks = db.Queryable<products>()
                    .Where(p => productIds.Contains(p.ProId))
                    .Select(p => new { p.ProId, p.CateId, p.MaxOQ, p.Stock, p.IsCombination, p.Sales })
                    .ToList()
                    .ToDictionary(p => p.ProId);

                foreach (var product in ordersPackage.ProInfo)
                {
                    if (!returnProducts.TryGetValue(product.LId, out var qty) || qty == 0) continue;

                    var proRow = proStocks.TryGetValue(product.ProId, out var row) ? row : null;
                    if (proRow == null) continue;

                    // 更新产品基础信息
                    db.Updateable<products>()
                        .SetColumns(p => new products { EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) })
                        .Where(p => p.ProId == product.ProId)
                        .ExecuteCommand();

                    // 处理组合产品库存
                    if (new[] { 0, 2 }.Contains(proRow.IsCombination))
                    {
                        var comRow = db.Queryable<products_selected_attribute_combination>()
                            .First(c => c.ProId == product.ProId && c.Status == "valid" && c.VariantsId == "");

                        if (comRow.Source == "UeeshopCrm")
                            //_appSettings.CrmStockAry[comRow.SourceId] = qty;

                            db.Updateable<products_selected_attribute_combination>()
                                .SetColumns(c => new products_selected_attribute_combination { Stock = comRow.Stock + qty })
                                .Where(c => c.ProId == product.ProId && c.Status == "valid" && c.VariantsId == "")
                                .ExecuteCommand();
                    }

                    // 更新库存和销量
                    if (orderRow.CutStock == true && orderRow.CutCancel == false)
                    {
                        db.Updateable<products>()
                            .SetColumns(p => new products { Sales = proRow.Sales - qty })
                            .Where(p => p.ProId == product.ProId)
                            .ExecuteCommand();

                        if (!string.IsNullOrEmpty(product.VariantsId) && new[] { 0, 1 }.Contains(proRow.IsCombination))
                        {
                            var combination = db.Queryable<products_selected_attribute_combination>()
                                .First(c => c.ProId == product.ProId && c.VariantsId == product.VariantsId);

                            if (combination != null && combination.Source == "UeeshopCrm")
                                //_appSettings.CrmStockAry[combination.SourceId] = qty;

                                db.Updateable<products_selected_attribute_combination>()
                                    .SetColumns(c => new products_selected_attribute_combination { Stock = combination.Stock + qty })
                                    .Where(c => c.CId == combination.CId)
                                    .ExecuteCommand();
                        }
                    }
                }

                ProductsAutoOnOffShelf(new Dictionary<string, object> { { "ProIdAry", productIds } });

            }

            // 更新订单状态
            var ordersPackageRow = GetOrdersPackage(orderId);

            if (ordersPackageRow.Count == 0)
                refundType = 1;

            if (isCancel == 0)
            {
                var updateData = new orders();
                if (refundType == 0)
                {

                    db.Updateable<orders>()
                    .SetColumns(x => new orders
                    {
                        PaymentStatus = "partially_refunded",
                        UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                    })
                    .Where(o => o.OrderId == orderId)
                    .ExecuteCommand();
                }
                else
                {

                    db.Updateable<orders>()
                    .SetColumns(x => new orders
                    {
                        OrderStatus = 7,
                        PaymentStatus = "refunded",
                        UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                    })
                    .Where(o => o.OrderId == orderId)
                    .ExecuteCommand();
                }





                if (passOrderStatus == 6)
                    OrdersUserUpdate(orderId, refundType == 0 ? "partially" : "");

                //if (refundType == 0)
                //    HelpsUser.CancelPoints(orderRow, refundId);
                //else
                //    HelpsUser.OrdersReturnPoints(orderRow);

                //SendRefundEmail(orderRow);
            }
            else
            {
                db.Updateable<orders>()
                    .SetColumns(o => new orders
                    {
                        OrderStatus = 7,
                        PaymentStatus = "voided",
                        UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                    })
                    .Where(o => o.OrderId == orderId)
                    .ExecuteCommand();


                if (passOrderStatus == 6)
                    OrdersUserUpdate(orderId, "");

                //HelpsUser.OrdersReturnPoints(orderRow);
                //HelpsUser.CancelPoints(orderRow);
            }

            //OperationLog("更新付款状态", orderRow.OId);
        }

        /// <summary>
        /// 订单取消积分（收回积分）
        /// </summary>
        /// <param name="orderRow">订单信息</param>
        /// <param name="refundId">退款ID</param>
        /// <returns></returns>
        //public bool CancelPoints(dynamic orderRow, int refundId = 0)
        //{
        //    var orderId = orderRow.OrderId;
        //    var userId = orderRow.UserId;

        //    // 获取会员积分配置
        //    var mConfig = _appConfig.MemberPointsConfig;
        //    var cancelPointsConfig = mConfig?["return"]?["cancelPoints"]?.ToString() ?? "return";

        //    if (cancelPointsConfig != "return") return false;

        //    // 获取总获得积分
        //    var totalGetPoints = _db.Queryable<MemberPointsLog>()
        //        .Where(l => l.SourceType == "placeorder"
        //                 && l.UserId == userId
        //                 && l.OrderId == orderId)
        //        .Sum(l => l.Points) ?? 0;

        //    if (totalGetPoints == 0) return false;

        //    // 获取已取消积分
        //    var totalCanceledPoints = Math.Abs(_db.Queryable<MemberPointsLog>()
        //        .Where(l => l.SourceType == "cancel"
        //                 && l.UserId == userId
        //                 && l.OrderId == orderId)
        //        .Sum(l => l.Points) ?? 0);

        //    var cancelPoints = 0;

        //    if (refundId > 0)
        //    {
        //        // 部分退款处理
        //        var refundInfo = _db.Queryable<OrdersRefundInfo>()
        //            .Where(r => r.ReId == refundId)
        //            .First();

        //        var orderTotal = GetOrderTotalPrice(orderRow);
        //        var refundAmount = refundInfo.Amount + refundInfo.ShippingAmount;

        //        var percentage = orderTotal > 0
        //            ? (decimal)refundAmount / orderTotal
        //            : 0;

        //        cancelPoints = (int)Math.Ceiling(totalGetPoints * percentage);
        //        cancelPoints = Math.Min(cancelPoints, totalGetPoints - totalCanceledPoints);
        //    }
        //    else
        //    {
        //        // 全额退款处理
        //        cancelPoints = totalGetPoints - totalCanceledPoints;
        //    }

        //    if (cancelPoints > 0)
        //    {
        //        return MemberService.MemberPointsLog(
        //            userId,
        //            cancelPoints,
        //            "cancel",
        //            "expend",
        //            new { OrderId = orderId }
        //        );
        //    }

        //    return false;
        //}


        /// <summary>
        /// 处理会员数据 消费金额，会员等级
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="type">处理类型 plus:增加累计金额 less:扣除累计金额</param>
        /// <param name="refundType">退款类型 all:全款 partially:部分退款</param>
        public async void OrdersUserUpdate(int orderId, string type = "plus", string refundType = "all")
        {
            var ordersRowTwo = db.Queryable<orders>()
               .Where(o => o.OrderId == orderId).First();
            var ordersRow = db.Queryable<orders>()
                .Where(o => o.OrderId == orderId)
                .Select(o => new
                {
                    o.OrderId,
                    o.UserId,
                    o.Rate,
                    o.ManageCurrency,
                    o.OrderStatus,
                    o.CutUser
                })
                .First();

            if (ordersRow?.UserId == null) return;

            var price = 0.00m;
            if (refundType == "all")
            {
                price = await GetActualPayment(ordersRowTwo);
            }
            else if (refundType == "partially")
            {
                var refundAmount = db.Queryable<orders_refund_info>()
                    .Where(r => r.OrderId == orderId && r.Status == "success")
                    .OrderBy(r => r.ReId, OrderByType.Desc)
                    .Select(r => r.Amount)
                    .First();

                price = _helpsCartService.CurrencyFloatPrice(
                    _helpsCartService.CeilPrice(refundAmount / ordersRow.Rate),
                    ordersRow.ManageCurrency
                );
            }

            var userRow = db.Queryable<user>()
                .Where(u => u.UserId == ordersRow.UserId)
                .Select(u => new
                {
                    u.Consumption,
                    u.ConsumptionTime,
                    u.LevelUpdateType
                })
                .First();

            var data = new Dictionary<string, object>();
            var curPrice = userRow?.Consumption ?? 0;

            if (type == "plus" && ordersRow.CutUser == false)
            {
                curPrice += price;
                data["Consumption"] = curPrice;
                data["ConsumptionTime"] = (userRow?.ConsumptionTime ?? 0) + 1;
            }
            else if (type == "less")
            {
                curPrice -= price;
                data["Consumption"] = curPrice;

                if (ordersRow.OrderStatus == 7)
                {
                    data["ConsumptionTime"] = (userRow?.ConsumptionTime ?? 0) - 1;
                }
            }

            //if (_appSettings.Plugins.Used.Contains("user_level") && userRow?.LevelUpdateType == "auto")
            //{
            //    var levelRow = _db.Queryable<UserLevel>()
            //        .OrderBy(l => l.FullPrice, OrderByType.Desc)
            //        .ToList();

            //    var matchedLevel = levelRow.FirstOrDefault(l => curPrice >= l.FullPrice);
            //    data["Level"] = matchedLevel?.LId ?? 0;
            //}

            if (data.Any())
            {
                db.Updateable<user>()
                    .SetColumns(x => new user { Consumption = Convert.ToDecimal(data["Consumption"]), ConsumptionTime = (short)(data["ConsumptionTime"]) })
                    .Where(u => u.UserId == ordersRow.UserId)
                    .ExecuteCommand();
            }

            db.Updateable<orders>()
                .SetColumns(o => new orders
                {
                    UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    CutUser = true
                })
                .Where(o => o.OrderId == orderId)
                .ExecuteCommand();
        }


        public async Task<decimal> GetActualPayment(orders orderData, int isManage = 1)
        {
            // 1. 获取订单总价
            var ordersPrice = await OrdersPrice(orderData, 1, isManage, false, false, new ReplaceDataModel());
            // 2. 查询退款信息
            var refunds = db.Queryable<orders_refund_info>()
                .Where(r => r.OrderId == orderData.OrderId && r.Status == "success")
                .Select(r => r.Amount)
                .ToList();

            var refundedTotal = refunds.Sum();

            // 3. 处理货币转换
            if (isManage == 1)
            {
                var rate = orderData.Rate > 0 ? orderData.Rate : 1;
                refundedTotal = _helpsCartService.CurrencyFloatPrice(
                    _helpsCartService.CeilPrice(refundedTotal / rate),
                    orderData.ManageCurrency
                );
            }
            else
            {
                refundedTotal = _helpsCartService.CurrencyFloatPrice(
                    refundedTotal,
                    orderData.Currency
                );
            }

            // 4. 计算实际支付金额
            var actualPayment = ordersPrice - (decimal)refundedTotal;
            return actualPayment < 0 ? 0 : actualPayment;
        }



        public void ProductsAutoOnOffShelf(Dictionary<string, object> param)
        {
            var proIdAry = param.ContainsKey("ProIdAry")
                ? param["ProIdAry"] as List<int>
                : new List<int>();

            // 自动上架逻辑
            var offShelfProIds = db.Queryable<products>()
                .Where(p => p.SoldOut == true && p.SoldStatus == 2 && proIdAry.Contains(p.ProId))
                .Select(p => p.ProId)
                .ToList();

            if (offShelfProIds.Any())
            {
                var stockSummary = db.Queryable<products_selected_attribute_combination>()
                    .Where(c => offShelfProIds.Contains(Convert.ToInt32(c.ProId)) && c.Status == "valid")
                    .GroupBy(c => c.ProId)
                    .Select((c) => new { ProId = c.ProId, TotalStock = SqlFunc.AggregateSum(c.Stock) })
                    .ToList();

                var updateBatch = stockSummary
                    .Where(s => s.TotalStock > 0)
                    .Select(s => new products { ProId = Convert.ToInt32(s.ProId), SoldOut = false })
                    .ToList();

                if (updateBatch.Any())
                {
                    db.Updateable(updateBatch)
                        .UpdateColumns(it => new { it.SoldOut })
                        .ExecuteCommand();
                }
            }

            // 自动下架逻辑
            var onShelfProIds = db.Queryable<products>()
                .Where(p => p.SoldStatus == 2 && proIdAry.Contains(p.ProId) && p.SoldOut != true)
                .Select(p => p.ProId)
                .ToList();

            if (onShelfProIds.Any())
            {
                var stockSummary = db.Queryable<products_selected_attribute_combination>()
                    .Where(c => onShelfProIds.Contains(Convert.ToInt32(c.ProId)) && c.Status == "valid")
                    .GroupBy(c => c.ProId)
                    .Select((c) => new { ProId = c.ProId, TotalStock = SqlFunc.AggregateSum(c.Stock) })
                    .ToList();

                var updateBatch = stockSummary
                    .Where(s => s.TotalStock <= 0)
                    .Select(s => new products { ProId = Convert.ToInt32(s.ProId), SoldOut = true })
                    .ToList();

                if (updateBatch.Any())
                {
                    db.Updateable(updateBatch)
                        .UpdateColumns(it => new { it.SoldOut })
                        .ExecuteCommand();
                }
            }

            //// 到货通知处理
            //string arrivalNoticeIdStr = string.Empty;
            //if (_appSettings.Plugins.Used.Contains("arrival_notice"))
            //{
            //    var mailMethod = "manual";
            //    var configData = AppConfig.GetValue(new { ClassName = "arrival_notice" }, "ConfigData");
            //    if (configData != null)
            //    {
            //        var jsonConfig = JsonConvert.DeserializeObject<Dictionary<string, object>>(configData);
            //        mailMethod = jsonConfig?.ContainsKey("mailMethod")
            //            ? jsonConfig["mailMethod"].ToString()
            //            : "manual";
            //    }

            //    if (mailMethod == "auto")
            //    {
            //        var arrivalNotices = _db.Queryable<ArrivalNotice>()
            //            .Where(a => proIdAry.Contains(a.ProId) && a.AwaitCount > 0)
            //            .ToList();

            //        if (arrivalNotices.Any())
            //        {
            //            var proIds = arrivalNotices.Select(a => a.ProId).Distinct().ToList();
            //            var variantIds = arrivalNotices.Select(a => a.VariantsId).Distinct().ToList();

            //            var stockedCombinations = _db.Queryable<ProductsSelectedAttributeCombination>()
            //                .Where(c => proIds.Contains(c.ProId) &&
            //                          variantIds.Contains(c.VariantsId) &&
            //                          c.Status == "valid" &&
            //                          c.Stock > 0)
            //                .Select(c => new { c.ProId, c.VariantsId })
            //                .ToList();

            //            var noticeIds = stockedCombinations
            //                .Join(arrivalNotices,
            //                    sc => new { sc.ProId, sc.VariantsId },
            //                    an => new { an.ProId, an.VariantsId },
            //                    (sc, an) => an.AId)
            //                .Distinct()
            //                .ToList();

            //            arrivalNoticeIdStr = string.Join(",", noticeIds);
            //        }
            //    }
            //}

            //// 同步CRM队列
            //var queue = new HelpsQueue();
            //queue.Create("productsAutoOnOffShelf", new
            //{
            //    proIdAry,
            //    crmStockAry = param.ContainsKey("crmStockAry") ? param["crmStockAry"] : new List<object>(),
            //    crmStockType = param.ContainsKey("crmStockType") ? param["crmStockType"].ToString() : "",
            //    arrivalNoticeIdStr
            //});
        }

        /**
	 * 获取订单包裹信息
	 *
	 * @return array				订单包裹信息
	 * 								● Parent 主包裹信息
	 * 								● Sub 拆分包裹信息[以Parent分类]
	 * 								● Valid 主包裹的有效包裹
	 * 								● ProInfo 包裹产品信息
	 * 								● Count 包裹数量
	 * 								● OvId 海外仓ID数组
	 */
        /// <summary>
        /// 获取订单包裹信息
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <param name="data">订单数据</param>
        /// <returns></returns>
        public (List<orders_package> Parent, Dictionary<int, List<orders_package>> Sub, Dictionary<int, List<orders_package>> Valid,
            List<ProductInfo> ProInfo, int Count, List<int> OvId)
            GetOrdersPackage(int orderId, orders data = null)
        {
            var result = new
            {
                Parent = new List<orders_package>(),
                Sub = new Dictionary<int, List<orders_package>>(),
                Valid = new Dictionary<int, List<orders_package>>(),
                ProInfo = new List<ProductInfo>(),
                Count = 0,
                OvId = new List<int>()
            };

            // 确定查询列
            string column = "OrderId";
            if (data != null && !string.IsNullOrEmpty(data.OId))
            {
                var orderData = JudgeOrder(data.OId);
                if (orderData.Type == "temp")
                    column = "TempOrderId";
            }

            // 查询订单包裹
            var ordersPackage = db.Queryable<orders_package>()
                .WhereIF(column == "OrderId", x => x.OrderId == orderId)
                .WhereIF(column == "TempOrderId", x => x.TempOrderId == orderId)
                .ToList();
            //因为退款的时候只退未发货的包裹，所以这里需要过滤掉已发货的包裹

            // 获取运输方式信息
            var shippingMethodSIds = ordersPackage.Select(p => p.ShippingMethodSId).Distinct().ToList();
            var shippingRows = db.Queryable<shipping>()
                .Where(s => shippingMethodSIds.Contains((short)s.SId))
                .Select(s => new { s.SId, s.ApiType, s.Express })
                .ToList();
            var shippingDict = shippingRows.ToDictionary(s => s.SId);

            // 处理海外仓ID
            var ovidAry = ordersPackage.Select(p => Convert.ToInt32(p.OvId)).Distinct().ToList();

            // 获取包裹产品信息
            var proInfo = GetPackageProinfo(orderId);

            // 包裹主次类型分组
            var packageAry = new
            {
                Parent = new List<orders_package>(),
                Sub = new Dictionary<int, List<orders_package>>()
            };

            foreach (var pkg in ordersPackage)
            {
                if (pkg.ParentId > 0)
                {
                    if (!packageAry.Sub.ContainsKey(Convert.ToInt32(pkg.ParentId)))
                        packageAry.Sub[Convert.ToInt32(pkg.ParentId)] = new List<orders_package>();
                    packageAry.Sub[Convert.ToInt32(pkg.ParentId)].Add(pkg);
                }
                else
                {
                    packageAry.Parent.Add(pkg);
                }
            }

            // 过滤有效包裹
            var validAry = new Dictionary<int, List<orders_package>>();
            foreach (var mainPkg in packageAry.Parent)
            {
                var subPackages = packageAry.Sub.GetValueOrDefault(mainPkg.WId, new List<orders_package>());
                var validPackages = new List<orders_package>();

                if (subPackages.Any())
                {
                    foreach (var subPkg in subPackages)
                    {
                        if (!IsEmptyPackage(subPkg.ProInfo))
                            validPackages.Add(subPkg);
                    }
                }
                else if (!IsEmptyPackage(mainPkg.ProInfo))
                {
                    validPackages.Add(mainPkg);
                }

                if (validPackages.Any())
                    validAry[mainPkg.WId] = validPackages;
            }

            // 构建返回结果
            return (
                Parent: packageAry.Parent,
                Sub: packageAry.Sub,
                Valid: validAry,
                ProInfo: proInfo,
                Count: validAry.Values.Sum(v => v.Count),
                OvId: ovidAry
            );
        }
        /// <summary>
        /// 判断包裹是否为空
        /// </summary>
        /// <param name="proInfoJson">包裹产品信息JSON字符串</param>
        /// <returns>是否为空包裹</returns>
        public bool IsEmptyPackage(string proInfoJson)
        {
            // 空值检查
            if (string.IsNullOrWhiteSpace(proInfoJson))
                return true;

            try
            {
                // 解析JSON数据
                var proInfoData = JsonConvert.DeserializeObject<Dictionary<string, int>>(proInfoJson);

                // 数据有效性检查
                if (proInfoData == null || proInfoData.Count == 0)
                    return true;

                // 计算总数量
                int totalQty = 0;
                foreach (var qty in proInfoData.Values)
                {
                    totalQty += qty;
                }

                // 数量判断
                return totalQty == 0;
            }
            catch (JsonException)
            {
                // JSON解析失败视为无效数据
                return true;
            }
        }
        public List<ProductInfo> GetPackageProinfo(int orderId)
        {
            // 查询订单产品
            var ordersProducts = db.Queryable<orders_products_list>()
                .Where(p => p.OrderId == orderId)
                .ToList();

            // 获取产品ID列表
            var proIds = ordersProducts.Select(p => p.ProId).Distinct().ToList();
            if (!proIds.Any())
                return new List<ProductInfo>();

            // 查询产品定制属性
            var customs = db.Queryable<products_customs>()
                .Where(c => proIds.Contains(c.ProId))
                .ToList()
                .ToDictionary(c => c.ProId);

            // 查询产品基础信息
            var products = db.Queryable<products>()
                .Where(p => proIds.Contains(p.ProId))
                .Select(p => new
                {
                    p.ProId,
                    p.Prefix,
                    p.Number,
                    p.PicPath_0,
                    p.PageUrl,
                    p.IsCombination,
                    p.TId,
                    p.CostPrice
                })
                .ToList()
                .ToDictionary(p => p.ProId);

            // 查询组合产品成本价
            var costPriceData = new Dictionary<int, Dictionary<string, decimal>>();
            var combinationProIds = products
                .Where(p => p.Value.IsCombination == 1)
                .Select(p => p.Key)
                .ToList();

            if (combinationProIds.Any())
            {
                var combinations = db.Queryable<products_selected_attribute_combination>()
                    .Where(c => combinationProIds.Contains(Convert.ToInt32(c.ProId)) &&
                               c.Status == "valid" &&
                               c.StockStatus == "valid")
                    .Select(c => new
                    {
                        c.ProId,
                        c.VariantsId,
                        c.CostPrice
                    })
                    .ToList();

                costPriceData = combinations
                    .GroupBy(c => Convert.ToInt32(c.ProId))
                    .ToDictionary(
                        g => g.Key,
                        g => g.ToDictionary(
                            x => x.VariantsId,
                            x => x.CostPrice
                        )
                    );
            }

            // 构建最终结果（改为列表）
            var result = new List<ProductInfo>();
            foreach (var product in ordersProducts)
            {
                if (product.ProId > 0 && products.Count > 0)
                {
                    var custom = customs.TryGetValue(Convert.ToInt32(product.ProId), out var customData) ? customData : new products_customs();
                    var proInfo = products.TryGetValue(Convert.ToInt32(product.ProId), out var proData) ? proData : null;

                    var item = new ProductInfo
                    {
                        LId = product.LId,
                        ProId = Convert.ToInt32(product.ProId),
                        VariantsId = product.VariantsId,
                        // 合并其他必要字段...
                        CostPrice = costPriceData
                            .TryGetValue(Convert.ToInt32(product.ProId), out var priceDict) &&
                            priceDict.TryGetValue(product.VariantsId, out var price)
                            ? price
                            : proInfo?.CostPrice ?? 0m,
                        orderProduct = product,
                        Qty = Convert.ToInt32(product.Qty)

                    };

                    // 合并定制属性（示例，需根据实际字段调整）
                    item.CustomField1 = custom.CustomsValue.ToString();
                    // item.CustomField2 = custom.CustomField2;

                    result.Add(item); // 添加到列表
                }

            }

            return result;
        }


        public (string Type, string Prefix, string OrdersTable, string OrdersProTable) JudgeOrder(string oId)
        {
            // 初始化默认返回值
            var result = new
            {
                Type = string.Empty,
                Prefix = string.Empty,
                OrdersTable = "orders",
                OrdersProTable = "orders_products_list"
            };

            // 获取全局配置
            //var globalConfig = _appSettings.Config?.Global ?? new GlobalConfig();
            var orderPrefix = string.Empty;
            int prefixLength = orderPrefix.Length;

            // 验证订单号格式
            if (!string.IsNullOrEmpty(oId) &&
                oId.StartsWith('T') &&
                oId.Count(c => c == 'T') > 1 &&
                (string.IsNullOrEmpty(orderPrefix) || !oId.StartsWith(orderPrefix)) &&
                int.TryParse(new string(oId.SkipWhile(c => c != 'T').Skip(1).ToArray()), out _))
            {
                // 匹配临时订单格式
                result = new
                {
                    Type = "temp",
                    Prefix = "Temp",
                    OrdersTable = "temp_orders",
                    OrdersProTable = "temp_orders_products_list"
                };
            }

            return (result.Type, result.Prefix, result.OrdersTable, result.OrdersProTable);
        }

        private async Task<(bool status, string msg)> CallPaymentGatewayAsync(orders order, orders_refund_info refundData, int refundType)
        {

            //private readonly IServiceProvider _serviceProvider;
            using var container = _serviceProvider.CreateScope();
            var _payPalServices = container.ServiceProvider.GetService<IPayPalServices>();
            var _payoneerServices = container.ServiceProvider.GetService<IPayoneerServices>();
            var _oceanPaymentService = container.ServiceProvider.GetService<IOceanPaymentService>();
            if (order.PaymentMethod.ToLower().Trim() == "paypal")
            {
                // 调用PayPal退款接口
                var paypalResponse = await _payPalServices.Refunds(order, refundData);
                return (paypalResponse.status, paypalResponse.msg);
            }
            else if (order.PaymentMethod.ToLower().Trim() == "payoneer")
            {
                // 调用Payoneer退款接口
                var PayoneerResponse = await _payoneerServices.Refunds(order, refundData);
                return (PayoneerResponse.status, PayoneerResponse.msg);
            }
            else if (order.PaymentMethod.ToLower().Trim() == "oceanpayment")
            {
                // 调用钱海退款接口
                var oceanResponse = await _oceanPaymentService.Refunds(order, refundData);
                return (oceanResponse.status, oceanResponse.msg);
            }
            return (false, "未找到支付方式");


            // 根据支付方式调用不同API
            //switch (order.Payment.Method)
            //{
            //    case "Paypal":
            //        // 调用PayPal退款接口
            //        var paypalResponse = await PayPalService.RefundAsync(new PayPalRefundRequest
            //        {
            //            OrderId = order.OrderId,
            //            Amount = refundData.Amount,
            //            Currency = order.Currency
            //        });

            //        if (!paypalResponse.Success)
            //        {
            //            db.Updateable<orders_refund_info>()
            //                .SetColumns(r => new orders_refund_info
            //                {
            //                    Status = "fail",
            //                    RefundDetail = paypalResponse.ErrorMessage
            //                })
            //                .Where(r => r.ReId == refundData.ReId)
            //                .ExecuteCommand();
            //        }
            //        break;

            //        // 其他支付方式处理...
            //}
        }


        //private async Task PushToCrmQueueAsync(int orderId)
        //{
        //    // 使用Hangfire等队列系统
        //    BackgroundJob.Enqueue(() => Console.WriteLine($"推送订单{orderId}到CRM"));
        //}


        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="id"></param>
        /// <param name="totalAmount"></param>
        /// <param name="reason"></param>
        /// <param name="restock"></param>
        /// <param name="actionRefundType"></param>
        /// <param name="paymentMethod"></param>
        /// <returns></returns>
        public async Task<string> OrdersCancelAction(int id, decimal totalAmount, string reason, int restock, string actionRefundType, string paymentMethod)
        {
            // 验证订单
            var orderRow = db.Queryable<orders>()
                .Where(o => o.OrderId == id)
                .First();

            if (orderRow == null)
            {
                return "没查到订单";
            }
            var passOrderStatus = orderRow.OrderStatus;
            // 准备支付信息
            var paymentInfo = db.Queryable<orders_payment_info>()
                .Where(p => p.OrderId == id)
                .First();

            var paymentRow = db.Queryable<payment>()
                .Where(p => p.PId == orderRow.PId)
                .First();

            var paymentMethodName = actionRefundType == "original"
                ? paymentRow.Name_en
                : string.IsNullOrEmpty(paymentMethod) ? paymentRow.Name_en : paymentMethod;

            // 处理产品删除记录
            var ordersPackage = GetOrdersPackage(id);
            var logProData = new Dictionary<int, int>();
            var deleteProducts = new List<int>();

            foreach (var item in ordersPackage.ProInfo)
            {
                logProData[item.LId] = logProData.TryGetValue(item.LId, out int qty) ? qty + item.Qty : item.Qty;
            }

            foreach (var pkg in ordersPackage.Parent)
            {
                var deleteData = new orders_products_delete_list
                {
                    OrderId = id,
                    RefundId = 0,
                    PackageId = pkg.WId,
                    ProInfo = pkg.ProInfo,
                    ShippingStatus = "UnShipped",
                    RestockType = restock == 1 ? "return" : "no-restock",
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                };

                var DeleteId = db.Insertable(deleteData).ExecuteReturnIdentity();
                deleteProducts.Add(DeleteId);
            }

            // 退款逻辑处理
            if ((!string.IsNullOrEmpty(orderRow.PaymentId) && Convert.ToBoolean(paymentRow.IsOnline) &&
                orderRow.PaymentStatus == "paid" && actionRefundType == "original") ||
                (!Convert.ToBoolean(paymentRow.IsOnline) && orderRow.PaymentStatus == "paid" && actionRefundType == "offline"))
            {
                if (totalAmount <= 0)
                {
                    return "操作失败";
                }

                // 创建退款记录
                var refundData = new orders_refund_info
                {
                    OrderId = id,
                    Number = orderRow.PaymentId,
                    RefundMethod = paymentMethodName,
                    Reason = reason,
                    Amount = totalAmount,
                    ShippingAmount=0,
                    Currency = orderRow.Currency,
                    RefundId="",
                    Status = Convert.ToBoolean(paymentRow.IsOnline) ? "pending" : "success",
                    UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    RefundDetail= reason
                };

                db.Insertable(refundData).ExecuteReturnEntity();
                var refundId = refundData.ReId;

                // 更新删除记录的RefundId
                db.Updateable<orders_products_delete_list>()
                    .SetColumns(d => d.RefundId == refundId)
                    .Where(d => d.OrderId == id)
                    .ExecuteCommand();

                // 调用支付接口
                if (paymentRow.SystemType == "custom" || paymentRow.Method is "Paypal" or "Excheckout")
                {
                    //var paymentService = new PaymentService(paymentRow.Method);
                    //var refundResult = paymentService.Refund(new RefundParams
                    //{
                    //    OrderRow = orderRow,
                    //    OrderId = id,
                    //    PaymentRow = paymentRow,
                    //    RefundPaymentId = orderRow.PaymentId,
                    //    RefundType = 1,
                    //    RefundTotalAmount = totalAmount,
                    //    RefundReason = reason,
                    //    RefundId = refundId
                    //});

                    //if (refundResult.Status == "fail")
                    //{
                    //    db.Deleteable<orders_products_delete_list>()
                    //        .Where(d => d.RefundId == refundId)
                    //        .ExecuteCommand();
                    //}



                }





              
                // 记录订单日志
                WriteOrderLog(1, Convert.ToInt32(orderRow.UserId), "", id, 0,
                    "refund", "Refund Operation", totalAmount.ToString(), 0);
            }





            UpdateRefundSuccess(id, deleteProducts, 1, passOrderStatus, 1);
            // 普通取消逻辑
            db.Updateable<orders>()
                .SetColumns(o => o.CancelReason == reason)
                .Where(o => o.OrderId == id)
            .ExecuteCommand();

            //// 发送取消邮件
            //var emailConfig = db.Queryable<system_email_tpl>()
            //    .Where(t => t.Template == "order_cancel" && t.Language == HelpersEmail.GetWebDefaultLang())
            //    .First();

            //if (emailConfig?.IsUsed == true)
            //{
            //    var mailContent = HelpersEmail.OrderCancel(orderRow);
            //    HelpersEmail.SendMail(orderRow.Email, mailContent.mailTitle, mailContent.mailContents);
            //}
            var paidData = new Dictionary<string, object>
            {
                { "reason", reason }
            };
            var logOption = await _helpsWriteOrderLogService.GetWriteOrderLogData("voided", paidData);
            // 记录取消日志
            WriteOrderLog(1, Convert.ToInt32(orderRow.UserId), "", id, 0,
               logOption.LogMessage, logOption.LogTitle, logOption.LogData);
            // 取消订单后 检测并是否返还优惠券
            RefundCouponAction(id);

            //// 推送CRM队列
            //HelpersQueue.Create("pushOrdersToCrmSend", new { OrderId = orderId });


            return "已成功取消订单";




        }


        /// <summary>
        /// 取消订单时处理优惠券返还
        /// </summary>
        public async void RefundCouponAction(int orderId = 0)
        {
            var ordersRow = db.Queryable<orders>()
                .Where(x => x.OrderId == orderId)
                .First();

            if (ordersRow == null || string.IsNullOrEmpty(ordersRow.CouponCode)) return;

            var couponRow = await db.Queryable<sales_coupon>()
                .FirstAsync(c => c.CouponNumber == ordersRow.CouponCode);

            if (couponRow == null ||
                couponRow.OrderCancelReturn != true ||
                couponRow.CId == 0 ||
                ordersRow.UserId == 0)
            {
                return;
            }

            var couponLog = await db.Queryable<sales_coupon_log>()
                .FirstAsync(l => l.CId == couponRow.CId &&
                               l.UserId == ordersRow.UserId &&
                               l.OrderId == ordersRow.OrderId);

            if (couponLog != null)
            {
                couponLog.Status = "returned";
                couponLog.ReturnTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                db.Updateable(couponLog)
                    .UpdateColumns(it => new
                    {
                        it.Status,
                        it.ReturnTime
                    })
                    .ExecuteCommand();
                _helpsUserService.UpdateCouponStatus(couponRow, ordersRow.OrderId, 0);
            }
        }


        /// <summary>
        /// 生成订单日志
        /// </summary>
        /// <param name="isAdmin">是否为管理员 0:买家 1:管理员 2:API 3:APP</param>
        /// <param name="userId">会员ID或管理员ID</param>
        /// <param name="userName">会员名字或管理员名字</param>
        /// <param name="orderId">订单ID</param>
        /// <param name="isTemp">是否为临时订单</param>
        /// <param name="log">记录日志标题</param>
        /// <param name="logTitle">日志管理标题</param>
        /// <param name="logData">记录日志数据</param>
        /// <param name="tId">支付记录关联ID</param>
        public void WriteOrderLog(int isAdmin, int userId, string userName, int orderId,
            int isTemp = 0, string log = "", string logTitle = "", string logData = "", int tId = 0)
        {
            string prefix = isTemp == 1 ? "Temp" : "";
            var orderRow = db.Queryable<orders>()
                            .Where(o => o.OrderId == orderId)
                            .First(); // 对应Yii的limit(1)->one()
            var manageSession = db.Queryable<Entitys.manage>()
                .Where(x => x.UserName != "u")
                .First();

            // 处理不同操作来源
            switch (isAdmin)
            {
                case 1 when manageSession != null:

                    if (manageSession != null)
                    {
                        userId = manageSession?.UserId ?? 0;
                        userName = manageSession?.UserName?.ToString() ?? "";
                    }
                    break;
                case 2:
                    userId = 0;
                    userName = "API";
                    break;
                case 3:
                    userId = 0;
                    userName = "APP";
                    break;
            }

            // 创建日志实体
            var ordersLog = new orders_log
            {
                UserId = userId,
                IsAdmin = Convert.ToBoolean(isAdmin),
                UserName = userName,
                OrderId = orderId,
                TempOrderId = (int)(isTemp == 1 ? orderId : 0), // 处理前缀字段
                PaymentStatus = orderRow?.PaymentStatus,
                ShippingStatus = orderRow?.ShippingStatus,
                OrderStatus = orderRow?.OrderStatus,
                Ip = "", // 需要实现IP获取方法
                Log = log,
                LogManage = logTitle,
                LogData = logData,
                TId = tId,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) // 对应$c['time']
            };

            // 插入日志
            db.Insertable(ordersLog).ExecuteCommand();

            // 处理支付时间更新
            if (isTemp == 0 && orderRow != null &&
                (orderRow.PaymentStatus == "paid" || orderRow.PaymentStatus == "pending") &&
                orderRow.PayTime == null)
            {
                db.Updateable<orders>()
                    .SetColumns(o => new orders { PayTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) })
                    .Where(o => o.OrderId == orderId)
                    .ExecuteCommand();
            }
        }
















    }
}

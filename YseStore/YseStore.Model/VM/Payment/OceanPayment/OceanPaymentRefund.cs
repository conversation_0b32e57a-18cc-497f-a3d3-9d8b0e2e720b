using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace YseStore.Model.VM.Payment.OceanPayment
{


    public class RefundResp
    {
        [XmlElement("account")]
        public string account { get; set; }

        [XmlElement("terminal")]
        public string terminal { get; set; }

        [XmlElement("signValue")]
        public string signValue { get; set; }

        [XmlElement("payment_id")]
        public string payment_id { get; set; }

        [XmlElement("order_number")]
        public string order_number { get; set; }

        [XmlElement("refund_number")]
        public string refund_number { get; set; }

        [XmlElement("refund_id")]
        public string refund_id { get; set; }

        //退款申请结果代码
        [XmlElement("refund_results")]
        public string refund_results { get; set; }

        [XmlElement("refund_description")]
        public string refund_description { get; set; }

        [XmlElement("refund_reference")]
        public string refund_reference { get; set; }
    }
}
